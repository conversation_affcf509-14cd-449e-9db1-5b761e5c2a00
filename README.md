
[![Build Status](https://ecco.ci.cloudbees.com/job/ecco-on-commit-embedded/badge/icon)](https://ecco.ci.cloudbees.com/job/ecco-on-commit-embedded/)

Pre-requisites
==============

See [docs/DEV-ENVIRONMENT.md](docs/DEV-ENVIRONMENT.md) for instructions on installing and setting up

- git
- JDK 17 (as of some time in 2023) - Aiming for JDK 21 in production in 2024
- Maven 3.6+
- Node.js 18.20.8
- IntelliJ, make sure you have things set up so you don't need to do a full Maven build
  to run the server
- Tomcat 9 - https://tomcat.apache.org/download-90.cgi


paths/configs
=============
- JAVA_HOME needs to be set
- JAVA_HOME/bin and maven/bin directories need adding to the PATH environment variable
- Memory needs to be set specifically as we ~~use the Tomcat Maven Plugin which doesn't support forking a JVM~~.
    - `export MAVEN_OPTS="...-Xmx320M -XX:MaxMetaspaceSize=160M"`


Initial Checkout
================

    <NAME_EMAIL>:eccosolutions/ecco
    cd ecco
    mvn install -DskipTests


Building and Running
====================
Always refer the [CI build](.github/workflows/ci.yml) as gospel.

You'll find the right dependencies for as GeckoDriver, NodeJS, Yarn and JDK.

If you have any difficulty, just Skype a team mate.

For getting up and running you should be able to:
- Check the project out into IntelliJ IDEA Ultimate
- Run the Maven run configuration: `ecco-aggregator [install] - no tests`.
  This will ensure all quirky Java APT stuff happens, and will build the
  Javascript code (via Maven Frontend Plugin)
- Run a Tomcat run configuration for your chosen database: `localhost - h2`
  will work for everyone but doesn't persist.
  A browser should open when it's started
- Run either the UI tests or API tests via either of the `ecco-acceptance-tests`
  run configurations (UI requires Geckodriver and will need tweaking on Windows)


runtime options
---------------
Runtime options are varied by setting Java system properties (i.e -Dprop=value) in JVM properties of tomcat's setenv.sh,
and in the Run Configuration for launching Tomcat under Eclipse.  See also the run-*.sh scripts in this folder.

The options are as follows:

- env = dev|prod
    - default is dev
- db = h2|mysql|oracle|sqlserver
    - default is mysql
- db.hostname = {hostname or IP address}
    - default is localhost
- db.embedded-persist-path = path for storage (e.g. `"file:~/h2/ecco"`)
    - If specified will persist embedded db
- You can connect with H2 drivers with URLs <br>
    `jdbc:h2:tcp://localhost:9093/~/h2/ecco`<br>
    or `jdbc:h2:tcp://localhost:9093/mem:test`<br>
    and user: `sa`, password: (empty)
- liquibase = CREATE | UPDATE | DROP_CREATE | DUMP | DISABLED | NOT_SET (i.e. if you don't specify)
    - default is CREATE if running db=H2 (as in memory), otherwise, disabled
- encrypted
    - default is disabled.  -Dencrypted enables.  Causes encryption to be enabled, and
      Liquibase changesets with the 1.x-apply-encryption context to be applied
- browser = FIREFOX (seems to work best) | CHROME | IE | CHROME_HEADLESS | SAUCE
    - default is FIREFOX. Naturally, support will need to be installed.  Ask Neale for details if needed.

- MailTrap (or other SMTP) is autoconfigured for MailSender
    - `-Dspring.mail.host=smtp.mailtrap.io -Dspring.mail.username=<your username> -Dspring.mail.password=<your password>`

YAML Config
-----------
Naturally this is going to be dangerous if we don't distribute configs carefully, such
as Ansible or a DCS, perhaps K8s ConfigMaps

For now though, in order to configure various services, without lots
of crazy database tables, then we need a YAML or JSON config.



Examples:
---------

- SQL Server in a VirtualBox VM:
 -Denv=dev -Ddb.schema=dbo -Ddb=sqlserver -Ddb.hostname=************** -Ddb.extraContexts=central-proc-servicetype -Dliquibase=CREATE -Duser.timezone=UTC

Setup an IDE
============

See [docs/DEV-ENVIRONMENT.md](docs/DEV-ENVIRONMENT.md) for instructions
