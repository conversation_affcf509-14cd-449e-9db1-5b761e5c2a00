#!/usr/bin/env sh

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
cd $DIR/..

unzip ./setup/acctest.zip -d ./setup

mysql --protocol=tcp --host=localhost --port 3306 --user=root --password=password -e "drop database acctest" || true
mysql --protocol=tcp --host=localhost --port 3306 --user=root --password=password  < ./setup/create-acctest-schema-mysql.sql
mysql --protocol=tcp --host=localhost --port 3306 --user=ecco --password=ecco --database=acctest < ./setup/acctest.sql

rm -f ./setup/acctest.sql
