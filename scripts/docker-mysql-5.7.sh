
docker run \
  -p 3306:3306 \
  --name mysql_container --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=5 \
  -e MYSQL_ROOT_PASSWORD=password -e MYSQL_USER=ecco -e MYSQL_PASSWORD=ecco -e MYSQL_DATABASE=acctest \
  --entrypoint sh mysql:5.7-debian -c "exec docker-entrypoint.sh mysqld --default-authentication-plugin=mysql_native_password --lower_case_table_names=1 --character-set-server=utf8 --collation-server=utf8_general_ci"

# To give access to the local machine which appears on docker network at 172 subnet, do the following within the
# docker mysql container (docker exec -it mysql-5.7 /bin/bash)
# CREATE USER 'root'@'172.%' IDENTIFIED BY 'password';
# GRANT ALL PRIVILEGES ON *.* TO 'root'@'172.%' WITH GRANT OPTION;
# FLUSH PRIVILEGES;
