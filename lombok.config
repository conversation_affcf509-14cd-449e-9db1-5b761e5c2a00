# Don't look above here for a lombok.config
config.stopBubbling = true
# See the docs. Adds @Generated which means things like code coverage won't want tests for these methods
lombok.addLombokGeneratedAnnotation = true
# Will need to change to Jakarta or checkerframework - see https://projectlombok.org/features/configuration#addNullAnnotations
lombok.addNullAnnotations = javax
# Mainly so that @ConstructorProperties is added for <PERSON> to know it can use that ctor for deserialisation
#lombok.anyConstructor.addConstructorProperties = true
# We have some places where were weren't adding this manually when using @Data, but should be consistent, and
# exceptions should be explicit and documented
lombok.equalsAndHashCode.callSuper = call
# We could do this
# lombok.toString.callsuper=call