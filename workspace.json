{"version": 2, "projects": {"ecco-math": {"root": "ecco-ui/ecco-math", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-math/build-tsc", "ecco-ui/ecco-math/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-math/build-tsc", "ecco-ui/ecco-math/debug", "ecco-ui/ecco-math/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "@eccosolutions/ecco-mui": {"root": "ecco-ui/ecco-mui", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-mui/build-tsc", "ecco-ui/ecco-mui/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-mui/build-tsc", "ecco-ui/ecco-mui/debug", "ecco-ui/ecco-mui/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "@eccosolutions/ecco-mui-controls": {"root": "ecco-ui/ecco-mui-controls", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-mui-controls/build-tsc", "ecco-ui/ecco-mui-controls/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-mui-controls/build-tsc", "ecco-ui/ecco-mui-controls/debug", "ecco-ui/ecco-mui-controls/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-offline": {"root": "ecco-offline/src/main/resources/com/ecco/offline/staticFiles", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-offline/src/main/resources/com/ecco/offline/staticFiles/build"], "skipNxCache": "true", "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-offline/src/main/resources/com/ecco/offline/staticFiles/build"], "skipNxCache": "true", "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "application-properties": {"root": "ecco-ui/application-properties", "projectType": "library"}, "ecco-admin": {"root": "ecco-ui/ecco-admin", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-admin/build-tsc", "ecco-ui/ecco-admin/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-admin/build-tsc", "ecco-ui/ecco-admin/debug", "ecco-ui/ecco-admin/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-calendar": {"root": "ecco-ui/ecco-calendar", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-calendar/build-tsc", "ecco-ui/ecco-calendar/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-calendar/build-tsc", "ecco-ui/ecco-calendar/debug", "ecco-ui/ecco-calendar/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-commands": {"root": "ecco-ui/ecco-commands", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-commands/build-tsc", "ecco-ui/ecco-commands/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-commands/build-tsc", "ecco-ui/ecco-commands/debug", "ecco-ui/ecco-commands/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-components-core": {"root": "ecco-ui/ecco-components-core", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-components-core/_typings", "ecco-ui/ecco-components-core/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-components-core/_typings", "ecco-ui/ecco-components-core/debug", "ecco-ui/ecco-components-core/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-components": {"root": "ecco-ui/ecco-components", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-components/_typings", "ecco-ui/ecco-components/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-components/_typings", "ecco-ui/ecco-components/debug", "ecco-ui/ecco-components/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-dto": {"root": "ecco-ui/ecco-dto", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-dto/build-tsc", "ecco-ui/ecco-dto/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-dto/build-tsc", "ecco-ui/ecco-dto/debug", "ecco-ui/ecco-dto/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-evidence": {"root": "ecco-ui/ecco-evidence", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-evidence/build-tsc", "ecco-ui/ecco-evidence/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-evidence/build-tsc", "ecco-ui/ecco-evidence/debug", "ecco-ui/ecco-evidence/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-finance": {"root": "ecco-ui/ecco-finance", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-finance/build-tsc", "ecco-ui/ecco-finance/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-finance/build-tsc", "ecco-ui/ecco-finance/debug", "ecco-ui/ecco-finance/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-forms": {"root": "ecco-ui/ecco-forms", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-forms/types", "ecco-ui/ecco-forms/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-forms/types", "ecco-ui/ecco-forms/debug", "ecco-ui/ecco-forms/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-global": {"root": "ecco-ui/ecco-global", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "emit"}}, "prepare": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "prepare"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "ecco-incidents": {"root": "ecco-ui/ecco-incidents", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-incidents/build-tsc", "ecco-ui/ecco-incidents/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-incidents/build-tsc", "ecco-ui/ecco-incidents/debug", "ecco-ui/ecco-incidents/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "ecco-managedvoids": {"root": "ecco-ui/ecco-managedvoids", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-managedvoids/build-tsc", "ecco-ui/ecco-managedvoids/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-managedvoids/build-tsc", "ecco-ui/ecco-managedvoids/debug", "ecco-ui/ecco-managedvoids/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "ecco-repairs": {"root": "ecco-ui/ecco-repairs", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-repairs/build-tsc", "ecco-ui/ecco-repairs/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-repairs/build-tsc", "ecco-ui/ecco-repairs/debug", "ecco-ui/ecco-repairs/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "ecco-offline-data": {"root": "ecco-ui/ecco-offline-data", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-offline-data/build-tsc", "ecco-ui/ecco-offline-data/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-offline-data/build-tsc", "ecco-ui/ecco-offline-data/debug", "ecco-ui/ecco-offline-data/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-reports": {"root": "ecco-ui/ecco-reports", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-reports/build-tsc", "ecco-ui/ecco-reports/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-reports/build-tsc", "ecco-ui/ecco-reports/debug", "ecco-ui/ecco-reports/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}}, "projectType": "library"}, "ecco-rota": {"root": "ecco-ui/ecco-rota", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-rota/build-tsc", "ecco-ui/ecco-rota/debug"], "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-rota/build-tsc", "ecco-ui/ecco-rota/debug", "ecco-ui/ecco-rota/dist"], "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}, "ecco-staff-app": {"root": "ecco-ui/ecco-staff-app", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-staff-app/build"], "skipNxCache": "true", "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-staff-app/build"], "skipNxCache": "true", "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "application"}, "ecco-portal-app": {"root": "ecco-ui/ecco-portal-app", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-portal-app/build"], "skipNxCache": "true", "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-portal-app/build"], "skipNxCache": "true", "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "application"}, "ecco-welcome-menu": {"root": "ecco-ui/_welcome_", "projectType": "application"}, "ecco-test-app": {"root": "ecco-ui/ecco-test-app", "targets": {"emit": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-test-app/build"], "skipNxCache": "true", "options": {"script": "emit"}}, "build": {"executor": "@nrwl/workspace:run-script", "outputs": ["ecco-ui/ecco-test-app/build"], "skipNxCache": "true", "options": {"script": "build"}}, "lint": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "application"}, "ecco-ui-e2e": {"root": "ecco-ui/ecco-ui-e2e", "targets": {"test": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test"}}, "test-parallel-safe": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-parallel-safe"}}, "test-sequential": {"executor": "@nrwl/workspace:run-script", "outputs": [], "options": {"script": "test-sequential"}}}, "projectType": "library"}}}