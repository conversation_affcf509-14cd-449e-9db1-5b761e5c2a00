{"_see-docs-at": "https://nx.dev/latest/angular/getting-started/configuration", "npmScope": "@eccosolutions", "implicitDependencies": {"workspace.json": "*", "package.json": {"dependencies": "*", "devDependencies": "*"}, "nx.json": "*"}, "tasksRunnerOptions": {"encryptionKey": "jP8Gzeu3NIKpaVjDKhWwupfNaPW531zm", "default": {"runner": "@nrwl/nx-cloud", "runnerx": "@nrwl/workspace/tasks-runners/default", "overrides": {"outputPath": ["build-tsc", "dist", "debug", "build"]}, "options": {"outputPath": ["build-tsc", "dist", "debug", "build"], "accessToken": "ODQyOTlkMzYtNWNlMS00N2Q2LWE0YTgtOWZkZDM3MDhjOWIwfHJlYWQtd3JpdGU=", "url": "https://cloud.nx.app", "cacheableOperations": ["build", "emit", "test", "test-parallel-safe", "test-sequential", "lint", "package"], "strictlyOrderedTargets": ["build", "emit", "package"]}}}, "projects": {"ecco-offline": {"implicitDependencies": []}, "ecco-welcome-menu": {"implicitDependencies": []}, "application-properties": {"implicitDependencies": []}, "ecco-admin": {"implicitDependencies": []}, "ecco-calendar": {"implicitDependencies": []}, "ecco-commands": {"implicitDependencies": []}, "ecco-components-core": {"implicitDependencies": []}, "ecco-components": {"implicitDependencies": []}, "ecco-dto": {"implicitDependencies": []}, "ecco-evidence": {"implicitDependencies": []}, "ecco-finance": {"implicitDependencies": []}, "ecco-forms": {"implicitDependencies": []}, "ecco-global": {"implicitDependencies": []}, "ecco-incidents": {"implicitDependencies": []}, "ecco-managedvoids": {"implicitDependencies": []}, "ecco-repairs": {"implicitDependencies": []}, "ecco-math": {"implicitDependencies": []}, "@eccosolutions/ecco-mui": {"implicitDependencies": []}, "@eccosolutions/ecco-mui-controls": {"implicitDependencies": []}, "ecco-offline-data": {"implicitDependencies": []}, "ecco-reports": {"implicitDependencies": []}, "ecco-rota": {"implicitDependencies": []}, "ecco-staff-app": {"implicitDependencies": []}, "ecco-portal-app": {"implicitDependencies": []}, "ecco-test-app": {"implicitDependencies": []}, "ecco-ui-e2e": {"implicitDependencies": []}}, "affected": {"defaultBase": "main"}}