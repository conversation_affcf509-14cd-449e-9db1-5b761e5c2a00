import {ApiClient, ManagedVoidDto, ManagedVoidRepository} from "ecco-dto";

export class ManagedVoidAjaxRepository implements ManagedVoidRepository {
    constructor(private apiClient: ApiClient) {}

    public findById(id: number): Promise<ManagedVoidDto> {
        const apiPath = `managedvoids/${id}/`;
        return this.apiClient.get<ManagedVoidDto>(apiPath);
    }

    public findByServiceRecipientId(srId: number): Promise<ManagedVoidDto> {
        const apiPath = `managedvoids/service-recipients/${srId}/`;
        return this.apiClient.get<ManagedVoidDto>(apiPath);
    }
}
