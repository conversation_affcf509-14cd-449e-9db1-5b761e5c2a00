{"private": true, "name": "ecco-managedvoids", "version": "0.0.0", "license": "UNLICENSED", "exports": {".": "./build-tsc/index.js"}, "sideEffects": false, "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts .", "build": "eslint --ext .ts . && webpack", "lint": "eslint --ext .ts .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Nothing to do", "test-sequential": "yarn cy:test", "cy:test": "cypress run --component", "cy:dev": "cypress open --component"}, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "ecco-commands": "0.0.0", "ecco-components-core": "^0.0.0", "ecco-components": "0.0.0", "ecco-dto": "0.0.0", "react": "16.13.1", "react-dom": "16.13.1", "stream-browserify": "^3.0.0"}, "peerDependencies": {}, "devDependencies": {"@babel/core": "^7.0.0", "@bahmutov/cypress-esbuild-preprocessor": "^2.2.0", "@testing-library/cypress": "^8.0.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-loader": "^8.0.6", "cypress": "^11.2.0", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "terser-webpack-plugin": "^4.2.3", "typescript": "5.8.3", "url-loader": "4.1.1", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.2"}}