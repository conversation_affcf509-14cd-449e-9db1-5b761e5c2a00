import {Encrypted, HateoasResource} from "@eccosolutions/ecco-common";
import {DefaultCryptoService} from "@eccosolutions/ecco-crypto";
import {
    Command,
    CommandAjaxRepository,
    CommandDtoRepository,
    CommandRepository,
    EvidenceCommand,
    EvidenceCommandDtoConverter,
    MergeableCommand
} from "ecco-commands";
// import {ReferralTemplateAjaxRepository} from "./referral/ReferralTemplateAjaxRepository"
import {
    ApiClient,
    BuildingAjaxRepository,
    BuildingRepository,
    CalendarAjaxRepository,
    CalendarRepository,
    ClientAjaxRepository,
    ClientRepository,
    EvidenceDtos,
    FormEvidenceAjaxRepository,
    FormEvidenceRepository,
    getGlobalApiClient,
    isOffline,
    QuestionnaireWorkAjaxRepository,
    QuestionnaireWorkRepository,
    ReferralAjaxRepository,
    ReferralRepository,
    RiskEvidenceAjaxRepository,
    RiskEvidenceRepository,
    SecureCommandAjaxRepository,
    ServiceAjaxRepository,
    ServiceRecipientAjaxRepository,
    ServiceRecipientRepository,
    ServiceRepository,
    ServiceTypeAjaxRepository,
    SessionDataAjaxRepository,
    SessionDataRepository,
    SignatureAjaxRepository,
    SignatureRepository,
    SupportSmartStepsSnapshotRepository,
    SupportWorkAjaxRepository,
    SupportWorkRepository,
    WorkersAjaxRepository,
    WorkersRepository,
    WorkflowAjaxRepository,
    WorkflowDtoRepository
} from "ecco-dto";

import {Observable} from "rxjs";
import {Database, isSupported} from "./db/indexed-db";
import {OfflineSchema} from "./db/OfflineSchema";
import {DtoCryptoService} from "./security/DtoCryptoService";
import {OfflineRepository} from "./OfflineRepository";
import {EvidenceRepositoryEffectsWrapper} from "./evidence/EvidenceRepositoryEffectsWrapper";
import {SecureSessionDataAjaxRepository} from "./feature-config/SecureSessionDataAjaxRepository";
import {SecureCalendarAjaxRepository} from "./calendar/SecureCalendarAjaxRepository";
import {SecureQuestionnaireWorkAjaxRepository} from "./questionnaire/SecureQuestionnaireWorkAjaxRepository";
import {SecureServiceAjaxRepository} from "./service-config/SecureServiceAjaxRepository";
import {SecureSignatureAjaxRepository} from "./evidence/SecureSignatureAjaxRepository";
import {applicationRootPath} from "application-properties";
import {SecureReferralAjaxRepository} from "./referral/SecureReferralAjaxRepository";
import {SecureClientAjaxRepository} from "./clientdetails/SecureClientAjaxRepository";
import {SecureSupportWorkAjaxRepository} from "./evidence/support/SecureSupportWorkAjaxRepository";
import {SecureRiskEvidenceAjaxRepository} from "./evidence/risk/SecureRiskEvidenceAjaxRepository";
import {SecureWorkflowAjaxRepository} from "./workflow/SecureWorkflowAjaxRepository";
import {getUserSessionManager} from "./core-services";
import {SecureFormEvidenceAjaxRepository} from "./evidence/forms/SecureFormEvidenceAjaxRepository";

const lazyRepos = new Map();

/* NOTE: A pure way of ensuring that WeakMap keeps keys alive would be as follows, but it seems a Map is all we want
  for our lazy init.

const fooRepository = Symbol("fooRepository");

const repositoryFactories = {
    [fooRepository]: fooRepositoryFactory
};

const repositories = new WeakMap();

function fooRepositoryFactory() {
    return new FooRepository();
}

function getRepository(symbol) {
    const repository = repositories.get(symbol) ?? repositoryFactories[symbol]();
    if (!repositories.has(symbol)) {
        repositories.set(symbol, repository);
    }
    return repository
}

class FooRepository {
    readonly symbol = fooRepository; // Keep reference in WeakMap
}
 */
function lazy<T>(factory: () => T): T {
    let repo = lazyRepos.get(factory);
    if (!repo) {
        repo = factory();
        lazyRepos.set(factory, repo);
        if(!factory.name) console.error("Must use a named variable or function for factory: %o", factory);
        // console.debug("Got new %o", repo);
    }
    // else {
    //     console.debug("Got cached %o", repo);
    // }
    return repo;
}

/**
 * A dummy implementation for the common usage of EvidenceRepositoryEffectsWrapper,
 */
export class OnlineCommandDtoRepository implements CommandDtoRepository<EvidenceDtos> {
    constructor(private apiClient: ApiClient) {
    }

    findPendingCommands(): Observable<EvidenceDtos> {
        return Observable.empty<EvidenceDtos>();
        // return async.promiseToSequence(Q(Lazy(new Array<EvidenceDtos>())));
    }

    submitCommand<T extends EvidenceDtos>(commandDto: T): Promise<T> {
        return this.apiClient.postWithReAuth<void>(commandDto.commandUri, commandDto)
            .then(() => commandDto);
    }
}


/**
 * Offline implementation which provides updated domain objects for EvidenceRepositoryEffectsWrapper
 * based on commands that have been done offline - allows 'see updates offline'.
 */
export class OfflineEvidenceCommandDtoRepository implements CommandDtoRepository<EvidenceDtos> {
    constructor(private database: Promise<Database<OfflineSchema>>,
                private dtoCryptoService: DtoCryptoService) {
    }

    findPendingCommands(): Observable<EvidenceDtos> {
        return Observable.fromPromise(this.database)
            .flatMap(database =>
                database.asyncFindAll<Encrypted<EvidenceDtos, any>>(OfflineSchema.EVIDENCE_COMMANDS))
            .flatMap((secureDto: Encrypted<EvidenceDtos, any>) =>
                Observable.fromPromise(this.dtoCryptoService.decryptAndUnmangle(secureDto)));
    }

    submitCommand<T extends EvidenceDtos>(command: T): Promise<T> {
        let encryptedDto = this.dtoCryptoService.encrypt({
            secret: command,
            plain: {}
        });

        return this.database.then(database =>
            encryptedDto.then(encryptedDto => {
                database.save(OfflineSchema.EVIDENCE_COMMANDS, encryptedDto);
                return command;
            }));
    }
}


/**
 * FOR OFFLINE & ONLINE - online ones are used to sync in offline
 */
const evidenceCmdDtoConverter = new EvidenceCommandDtoConverter();
const warFactory = () => new WorkflowAjaxRepository(getGlobalApiClient());
const workflowAjaxRepository = () => lazy(warFactory);



/**
 * DIRECT ACCESS TO OFFLINE - populate if we are offline
 */
let offlineRepository: OfflineRepository;

export function getOfflineRepository(): OfflineRepository {
    if (!offlineRepository) {
        initOffline();
    }
    return offlineRepository;
}

export class EffectDelegatingCommandRepository implements CommandRepository {

    constructor(
        private commandRepository: CommandRepository,
        private effectsSink: EvidenceRepositoryEffectsWrapper
    ) {
    }


    // @ts-ignore  FIXME - Work through with Dan
    sendCommand(command: Command | MergeableCommand) {
        // if an actual Command (newer offline style) then submit and call effects
        if (command instanceof EvidenceCommand) {
            return this.effectsSink.submitCommand(command)
                .then(() => undefined); // To please Typescript
        }
        else {
            return this.commandRepository.sendCommand(command);
        }
    }

    exchangeCommand<T extends HateoasResource>(command: Command | MergeableCommand): Promise<T> {
        return this.commandRepository.exchangeCommand(command);
    }
}


/**
 * DEFINE OFFLINE REPOS
 */
let offlineEvidenceRepositoryWrapper: EvidenceRepositoryEffectsWrapper;

function initOffline() {
// If indexedDb is supported then we need repositories for falling back to offline use
    if (isSupported()) {

        const contextPath = applicationRootPath.startsWith("http") ? new URL(applicationRootPath).pathname : applicationRootPath;
        const databaseName = "ecco-offline-" + contextPath.replace(/\//g, "-");
        const database = Database.open(new OfflineSchema(databaseName));

        const cryptoService = new DefaultCryptoService();

        const commandAjaxRepository = new SecureCommandAjaxRepository(getGlobalApiClient());

        const upstreamSessionDataAjaxRepository = new SecureSessionDataAjaxRepository(getGlobalApiClient());
        const upstreamCalendarAjaxRepository = new SecureCalendarAjaxRepository(getGlobalApiClient());
        const upstreamReferralAjaxRepository = new SecureReferralAjaxRepository(getGlobalApiClient());
        const upstreamClientAjaxRepository = new SecureClientAjaxRepository(getGlobalApiClient());
        const upstreamSupportWorkAjaxRepository = new SecureSupportWorkAjaxRepository(getGlobalApiClient());
        const upstreamRiskEvidenceAjaxRepository = new SecureRiskEvidenceAjaxRepository(getGlobalApiClient());
        const upstreamFormEvidenceAjaxRepository = new SecureFormEvidenceAjaxRepository(
            getGlobalApiClient()
        );
        const upstreamQuestionnaireWorkRepository = new SecureQuestionnaireWorkAjaxRepository(getGlobalApiClient());
        const upstreamServiceAjaxRepository = new SecureServiceAjaxRepository(getGlobalApiClient());
        const upstreamSignatureAjaxRepository = new SecureSignatureAjaxRepository(getGlobalApiClient());
        const upstreamWorkflowAjaxRepository = new SecureWorkflowAjaxRepository(getGlobalApiClient());

        const dtoCryptoService = new DtoCryptoService(cryptoService, getUserSessionManager()!);

        offlineRepository = new OfflineRepository(
            database,
            upstreamSessionDataAjaxRepository,
            upstreamCalendarAjaxRepository,
            commandAjaxRepository,
            upstreamReferralAjaxRepository,
            upstreamClientAjaxRepository,
            upstreamSupportWorkAjaxRepository,
            upstreamRiskEvidenceAjaxRepository,
            upstreamFormEvidenceAjaxRepository,
            upstreamQuestionnaireWorkRepository,
            upstreamWorkflowAjaxRepository,
            upstreamServiceAjaxRepository,
            upstreamSignatureAjaxRepository,
            getUserSessionManager()!,
            dtoCryptoService
        );

        offlineEvidenceRepositoryWrapper = new EvidenceRepositoryEffectsWrapper(
            new OfflineEvidenceCommandDtoRepository(database, dtoCryptoService),
            evidenceCmdDtoConverter,
            {
                getFormEvidenceRepository,
                getRiskWorkRepository,
                getSignatureRepository,
                getSupportWorkRepository
            }
        );

    }
}


/*
 * DEFINE ONLINE REPOS
 */
const brFactory = () => new BuildingAjaxRepository(getGlobalApiClient());
const onlineBuildingRepository = () => lazy(brFactory);

const calFactory = () => new CalendarAjaxRepository(getGlobalApiClient());
const onlineCalendarRepository = () => lazy(calFactory);

const sigFactory = () => new SignatureAjaxRepository(getGlobalApiClient());
const onlineSignatureRepository = () => lazy(sigFactory);

const crFactory = () => new ClientAjaxRepository(getGlobalApiClient());
const onlineClientRepository = () => lazy(crFactory);

const owrFactory = () => new WorkersAjaxRepository(getGlobalApiClient());
const onlineWorkerRepository = () => lazy(owrFactory);

const ocqrFactory = () => new CommandAjaxRepository(getGlobalApiClient());
const onlineCommandQueueRepository = () => lazy(ocqrFactory);

const fcrFactory = () => new SessionDataAjaxRepository(getGlobalApiClient());
const onlineFeatureConfigRepository = () => lazy(fcrFactory);

const srFactory = () => new ServiceRecipientAjaxRepository(getGlobalApiClient());
const onlineServiceRecipientRepository = () => lazy(srFactory);

const rrFactory = () => new ReferralAjaxRepository(getGlobalApiClient());
const onlineReferralRepository = () => lazy(rrFactory);

const rerFactory = () => new RiskEvidenceAjaxRepository(getGlobalApiClient());
const onlineRiskEvidenceRepository = () => lazy(rerFactory);

const sFactory = () => new ServiceAjaxRepository(getGlobalApiClient());
const onlineServiceRepository = () => lazy(sFactory);

const swrFactory = () => new SupportWorkAjaxRepository(getGlobalApiClient());
const onlineSupportEvidenceRepository = () => lazy(swrFactory)

const quFactory = () => new QuestionnaireWorkAjaxRepository(getGlobalApiClient());
const onlineQuestionnaireEvidenceRepository = () => lazy(quFactory);

const ferFactory = () => new FormEvidenceAjaxRepository(getGlobalApiClient());
const onlineFormEvidenceRepository = () => lazy(ferFactory);

const erewrFactory = () =>
    new EvidenceRepositoryEffectsWrapper(
        new OnlineCommandDtoRepository(getGlobalApiClient()),
        evidenceCmdDtoConverter,
        {
            getFormEvidenceRepository,
            getRiskWorkRepository,
            getSignatureRepository,
            getSupportWorkRepository
        }
    );
const onlineEvidenceRepositoryWrapper = () => lazy(erewrFactory);

/*
 * DIRECT ACCESS TO ONLINE REPOS - for where we have them
 */
const stFactory = () => new ServiceTypeAjaxRepository(getGlobalApiClient());
export const serviceTypeAjaxRepository = () => lazy(stFactory);

export const questionnaireWorkAjaxRepository = onlineQuestionnaireEvidenceRepository;
// export const templateRepository = new ReferralTemplateAjaxRepository();
// export const signatureRepository = new SignatureAjaxRepository(apiClient);


/**
 * REPO-SWITCHER
 */
function useOffline() {
    const useOffline = isSupported() && (isOffline());
    if (!offlineRepository) {
        initOffline();
    }
    return useOffline;
}

export function getBuildingRepository(): BuildingRepository {
    if (useOffline()) {
        throw new Error("can't do buildings offline");
    }
    return onlineBuildingRepository();
}

export function getCalendarRepository(): CalendarRepository {
    return useOffline() ? offlineRepository : onlineCalendarRepository();
}

export function getClientRepository(): ClientRepository {
    return useOffline() ? offlineRepository : onlineClientRepository();
}

export function getWorkerRepository(): WorkersRepository {
    return /*useOffline() ? offlineRepository :*/ onlineWorkerRepository();
}


export function getEvidenceEffectsRepository(): EvidenceRepositoryEffectsWrapper {
    return useOffline() ? offlineEvidenceRepositoryWrapper: onlineEvidenceRepositoryWrapper();
}

export function getCommandQueueRepository(): CommandRepository {
    // @ts-ignore FIXME - was okay when less strict
    return new EffectDelegatingCommandRepository(
        useOffline() ? offlineRepository : onlineCommandQueueRepository(),
        getEvidenceEffectsRepository()
    );
}

export function getFormEvidenceRepository(): FormEvidenceRepository {
    return useOffline() ? offlineRepository : onlineFormEvidenceRepository();
}

export function getSupportSmartStepsSnapshotRepository(): SupportSmartStepsSnapshotRepository {
    return useOffline() ? offlineRepository : onlineSupportEvidenceRepository();
}

export function getFeatureConfigRepository(): SessionDataRepository {
    return useOffline() ? offlineRepository : onlineFeatureConfigRepository();
}

export function getServiceRecipientRepository(): ServiceRecipientRepository {
    return useOffline() ? offlineRepository : onlineServiceRecipientRepository();
}

export function getReferralRepository(): ReferralRepository {
    return useOffline() ? offlineRepository : onlineReferralRepository();
}

export function getServiceRepository(): ServiceRepository {
    return useOffline() ? offlineRepository : onlineServiceRepository();
}

export function getSupportWorkRepository(): SupportWorkRepository {
    return useOffline() ? offlineRepository : onlineSupportEvidenceRepository();
}

export function getRiskWorkRepository(): RiskEvidenceRepository {
    return useOffline() ? offlineRepository : onlineRiskEvidenceRepository();
}

export function getQuestionnaireSnapshotRepository(): QuestionnaireWorkRepository {
    return useOffline() ? offlineRepository : onlineQuestionnaireEvidenceRepository();
}

export function getWorkflowRepository(): WorkflowDtoRepository {
    return useOffline() ? offlineRepository : workflowAjaxRepository();
}

export function getSignatureRepository(): SignatureRepository {
    return useOffline() ? offlineRepository : onlineSignatureRepository();
}
