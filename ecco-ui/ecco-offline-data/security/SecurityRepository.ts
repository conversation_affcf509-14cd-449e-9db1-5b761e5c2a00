import {arrayBuffers, base64} from "@eccosolutions/ecco-common";
import ArrayBufferView = arrayBuffers.ArrayBufferView;
import {CipherText, cipherTextToDto, Uuid} from "@eccosolutions/ecco-crypto";
import {SecurityDtoRepository} from "./SecurityDtoRepository";
import {UserDevice} from "./UserDevice";

export class SecurityRepository {
    constructor(private securityDtoRepository: SecurityDtoRepository) {
    }

    /** Finds the UserDevice representing the user with the specified username
     * on this device.
     * @returns Promise.resolve(null) if not found */
    public findOneUserDeviceByUsername(username: string): Promise<UserDevice> {
        return this.securityDtoRepository.findOneUserDeviceByUsername(username)
                .then(userDeviceDto => UserDevice.fromDto(userDeviceDto));
    }

    /** Use with caution, and only when sure the key no longer exists at the remote end (i.e. got a 404).  Any
     * queued commands will be unrecoverable if doing things this way (which is the way it should be if a UserDevice
     * is invalidated.
     */
    public deleteUserDeviceByUsername(username: string): Promise<void> {
        console.debug(`Deleting user device for ${username}`);
        return this.securityDtoRepository.deleteUserDeviceByUsername(username);
    }

    /** Registers a UserDevice record to represent the user with the specified
     * username on this device.
     *
     * If there is already a UserDevice record for the specified user, then the
     * existing record will be replaced. */
    public registerUserDevice(username: string, credentialsKeySalt: ArrayBufferView,
            userDeviceId: Uuid, encryptedUserDeviceKey: CipherText): Promise<UserDevice> {
        console.debug(`Registering user device for ${username}`);

        let credentialsKeySaltBase64 = base64.encodeToString(credentialsKeySalt);

        let encryptedUserDeviceKeyDto = cipherTextToDto(encryptedUserDeviceKey);

        return this.securityDtoRepository
                .registerUserDevice(username, credentialsKeySaltBase64,
                        userDeviceId.toString(), encryptedUserDeviceKeyDto)
                .then(userDeviceDto => UserDevice.fromDto(userDeviceDto));
    }
}
