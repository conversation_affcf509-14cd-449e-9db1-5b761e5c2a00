import { arrayBuffers, base64 } from "@eccosolutions/ecco-common";
import ArrayBufferView = arrayBuffers.ArrayBufferView;
import {UserDevice as UserDeviceDto} from "ecco-dto";
import {CipherText, cipherTextFromDto} from "@eccosolutions/ecco-crypto";
import {Uuid} from "@eccosolutions/ecco-crypto";

/** Represents a User who is registered to use this Device. */
export class UserDevice {
    constructor(
        private username: string,
        private userDeviceId: Uuid,
        private credentialsKeySalt: ArrayBufferView,
        private userDeviceKeyCipherMessage: CipherText
    ) {}

    /** Gets the User's username. */
    public getUsername(): string {
        return this.username;
    }

    /** Gets the User Device ID, which uniquely identifies this User-Device. */
    public getUserDeviceId(): Uuid {
        return this.userDeviceId;
    }

    /** Gets the Credentials Key Salt, which is used in conjunction with the
     * user's credentials to derive the Credentials Key.
     *
     * The Credentials Key is used to encrypt the User Device Key. The User
     * Device Key is never stored unencrypted.
     *
     * This method is only useful to the UserSessionManager and UserSession
     * classes. Other callers should not call this method, and should not
     * attempt to derive the Credentials Key or decrypt the User Device Key. */
    public getCredentialsKeySalt(): ArrayBufferView {
        return this.credentialsKeySalt;
    }

    /** Gets the encrypted User Device Key.
     *
     * The User Device Key is used to encrypt, decrypt and validate commands
     * and data relating to the corresponding User on this Device.
     *
     * This method is only useful to the UserSession class. Other callers
     * should not call this method, and should not attempt to decrypt the
     * User Device Key.
     *
     * Other callers should encrypt data by calling UserSession.encrypt(), or
     * decrypt by calling UserSession.decrypt(). */
    public getUserDeviceKeyCipherMessage(): CipherText {
        return this.userDeviceKeyCipherMessage;
    }

    /** Constructs a new UserDevice from the specified data-transfer object.
     *
     * If the specified DTO is null or undefined, returns null. */
    public static fromDto(dto: UserDeviceDto): UserDevice {
        return (
            dto &&
            new UserDevice(
                dto.username,
                Uuid.parse(dto.userDeviceId)!!,
                base64.decodeFromString(dto.credentialsKeySalt),
                cipherTextFromDto(dto.userDeviceKeyCipherMessage)
            )
        );
    }
}
