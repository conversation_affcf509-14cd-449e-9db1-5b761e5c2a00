import {arrayBuffers, base64, Decrypted, Encrypted, utf8} from "@eccosolutions/ecco-common";
import {CipherText, CryptoService, Payload} from "@eccosolutions/ecco-crypto";
import {UserSessionManager} from "../UserSessionManager";
import {loginPageAuthErrorHandler} from "../auth/loginPageAuthErrorHandler";
import {stringifyPossibleError} from "../auth/errors";

const toUint8Array = arrayBuffers.toUint8Array;

const CIPHER = "AES";
const MODE = "GCM";
const KEY_SIZE_BITS = 4 * 32;
const TAG_SIZE_BITS = 128;
const IV_SIZE_BYTES = 16;

export class DtoCryptoService {
    constructor(
        private cryptoService: CryptoService,
        private userSessionManager: UserSessionManager
    ) {}

    /** Decrypts the specified encrypted data-transfer object. Returns a
     * promise that will resolve to the decrypted data-transfer object.
     *
     * If the specified data-transfer object is null or undefined, then the
     * promise resolves to null. */
    public decrypt<TSecretFields extends Object, TPlainFields extends Object>(
        encryptedDto: Encrypted<TSecretFields, TPlainFields>
    ): Promise<Decrypted<TSecretFields, TPlainFields> | null> {
        if (encryptedDto == null) {
            return Promise.resolve(null); // FIXME: WHY is this ever null?? (nu Feb 2020)
        }

        let userSession = this.userSessionManager.findCurrentUserSession();

        let encryptedData = base64.decodeFromString(encryptedDto.secret.base64Payload);

        let cipherText: CipherText = {
            cipher: CIPHER,
            mode: MODE,
            keySizeBits: KEY_SIZE_BITS,
            tagSizeBits: TAG_SIZE_BITS,
            initializationVector: encryptedData.subarray(0, IV_SIZE_BYTES),
            cipherText: encryptedData.subarray(IV_SIZE_BYTES),
            authenticatedData: new Uint8Array(0)
        };

        let secretFieldsByteArray = userSession
            .then(userSession => userSession.decrypt(this.cryptoService, cipherText))
            .then(payload => payload.secretData!!);

        let secretFieldsJsonString = secretFieldsByteArray.then(secretFieldsByteArray =>
            utf8.decode_utf8(secretFieldsByteArray)
        );

        let secretFields = secretFieldsJsonString.then(secretFieldsJsonString =>
            JSON.parse(secretFieldsJsonString)
        );

        return secretFields.then(secretFields => ({
            secret: secretFields,
            plain:
                encryptedDto.plain ||
                // FIXME: The following line violates the type signature of this function.
                <TPlainFields>(<any>encryptedDto) // Legacy support TODO: remove this line when all on 16.12 release or later
        }));
    }

    private unmangle<TSecretFields extends Object, TPlainFields extends Object>(
        decrypted: Decrypted<TSecretFields, TPlainFields>
    ): TSecretFields & TPlainFields {
        return {...decrypted.plain, ...decrypted.secret};
    }

    public decryptAndUnmangle<TSecretFields extends Object, TPlainFields extends Object>(
        encryptedDto: Encrypted<TSecretFields, TPlainFields>
    ): Promise<TSecretFields & TPlainFields> {
        return (
            this.decrypt(encryptedDto)
                .then(decrypted => this.unmangle(decrypted!!)) // Will throw if actually null
                // FIXME: The following failure handler violates the type signature of this function.
                .catch<TSecretFields & TPlainFields>(
                    reason => <any>DtoCryptoService.handleError(encryptedDto, reason)
                )
        );
    }

    /** Encrypts the specified data-transfer object. Returns a promise that
     * will resolve to the encrypted data-transfer object.
     *
     * If the specified data-transfer object is null or undefined, then the
     * promise resolves to null. */
    public encrypt<TSecretFields extends Object, TPlainFields extends Object>(
        dto: Decrypted<TSecretFields, TPlainFields>
    ): Promise<Encrypted<TSecretFields, TPlainFields>> {
        if (dto == null) {
            return Promise.resolve(null!!); // FIXME: Work out whether we really do want to call encrypt with null
        }

        let userSession = this.userSessionManager.findCurrentUserSession();

        let payload: Payload = {
            secretData: utf8.encode_utf8(JSON.stringify(dto.secret))
        };

        let cipherText = userSession.then(userSession =>
            userSession.encrypt(this.cryptoService, payload)
        );

        let ivCipherTextByteArray = cipherText.then(cipherText => {
            let length =
                cipherText.initializationVector.byteLength + cipherText.cipherText.byteLength;
            let byteArray = new Uint8Array(length);
            byteArray.set(toUint8Array(cipherText.initializationVector), 0);
            byteArray.set(
                toUint8Array(cipherText.cipherText),
                cipherText.initializationVector.byteLength
            );
            return byteArray;
        });

        let ivCipherTextBase64 = ivCipherTextByteArray.then(ivCipherTextByteArray =>
            base64.encodeToString(ivCipherTextByteArray)
        );

        return ivCipherTextBase64.then(ivCipherTextBase64 =>
            userSession.then(userSession => ({
                secret: {
                    userDeviceId: userSession.getUserDevice().getUserDeviceId().toString(),
                    base64Payload: ivCipherTextBase64
                },
                plain: dto.plain
            }))
        );
    }

    private static handleError<S extends Object, P extends Object>(
        secureDto: Encrypted<S, P>,
        reason: any
    ): Encrypted<S, P> {
        // Redirect to login form if auth error
        if (!loginPageAuthErrorHandler(reason)) {
            throw reason;
        }

        // If there is any error other than an AuthenticationException,
        // then we add an _errors field to the Secure DTO and resolve
        // the promise with the resulting Secure DTO as its value
        // (instead of the decrypted DTO).
        //
        // This strange behaviour violates the type signature, but I
        // have retained it for compatibility with existing code.
        // Please let's avoid relying on it in future.
        //
        // TODO: Provide a Fallible<T> data type for handling errors cleanly.

        if (!secureDto._errors) {
            secureDto._errors = [];
        }

        secureDto._errors.push(typeof reason == "string" ? reason : stringifyPossibleError(reason));

        return secureDto;
    }
}
