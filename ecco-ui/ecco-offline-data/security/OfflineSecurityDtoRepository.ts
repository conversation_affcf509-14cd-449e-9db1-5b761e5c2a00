import {CipherTextDto} from "@eccosolutions/ecco-crypto";
import {UserEncryptionDto} from "ecco-dto";
import {Database} from "../db/indexed-db";
import {OfflineSchema} from "../db/OfflineSchema";
import {SecurityDtoRepository} from "./SecurityDtoRepository";


function notNull(value: any, field: string) {
    if (value == null) {
        throw new TypeError(field + " must not be null.");
    }
}

function validateUsername(username: string) {
    notNull(username, "username");
    if (username !== username.toLowerCase()) {
        throw new Error("Username must be lower case.");
    }
}

export class OfflineSecurityDtoRepository implements SecurityDtoRepository {
    constructor(private database: Promise<Database<OfflineSchema>>) {
    }

    public deleteUserDeviceByUsername(username: string): Promise<void> {
        validateUsername(username);
        return this.database
            .then(database => database.remove(OfflineSchema.USERS, username));
    }

    public findOneUserDeviceByUsername(username: string): Promise<UserEncryptionDto> {
        validateUsername(username);
        return this.database
                .then(database => database.findOne(OfflineSchema.USERS, username));
    }

    registerUserDevice(username: string, credentialsKeySaltBase64: string,
            userDeviceId: string, encryptedUserDeviceKey: CipherTextDto): Promise<UserEncryptionDto> {
        validateUsername(username);
        notNull(credentialsKeySaltBase64, "credentialsKeySaltBase64");
        notNull(userDeviceId, "userDeviceId");
        notNull(encryptedUserDeviceKey, "encryptedUserDeviceKey");

        let userDto: UserEncryptionDto = {
            username: username,
            userDeviceId: userDeviceId,
            credentialsKeySalt: credentialsKeySaltBase64,
            userDeviceKeyCipherMessage: encryptedUserDeviceKey
        };

        console.debug(`Storing user device data for ${username}`);

        return this.database
                .then(database => database.save<UserEncryptionDto>(OfflineSchema.USERS, userDto));
    }
}

