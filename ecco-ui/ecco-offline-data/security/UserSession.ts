import { arrayBuffers, base64 } from "@eccosolutions/ecco-common";
import ArrayBufferView = arrayBuffers.ArrayBufferView;
import {CipherText, CryptoService, Payload} from "@eccosolutions/ecco-crypto";
import {UserSession as UserSessionDto} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import { UserDevice } from "./UserDevice";

/** Represents a logged-in UserSession, and performs encryption and decryption
 * using the corresponding User Device Key as a secret key.
 *
 * For security, the Credentials Key and User Device Key are intentionally not
 * exposed to callers. Callers should not attempt to decrypt the User Device
 * Key or maintain a reference to the Credentials Key or User Device Key.
 *
 * Callers should perform encryption and decryption only by calls to the
 * UserSession.decrypt() and .encrypt() methods. */
export class UserSession {
    constructor(
        private userDevice: UserDevice,
        private credentialsKey: ArrayBufferView
    ) {}

    public getUserDevice(): UserDevice {
        return this.userDevice;
    }

    /** Decrypts the specified cipher text, using the User Device Key for this
     * session as the secret key.
     *
     * The User Device Key is intentionally not exposed to the caller. To
     * maintain security, callers should not attempt to decrypt the User
     * Device Key or keep a reference to it. Callers should perform encryption
     * and decryption only by calls to the UserSession.decrypt() and .encrypt()
     * methods. */
    public decrypt(cryptoService: CryptoService, cipherText: CipherText): Promise<Payload> {
        return this.decryptUserDeviceKey(cryptoService).then(userDeviceKey =>
            cryptoService.decrypt(cipherText, userDeviceKey)
        );
    }

    /** Encrypts the specified payload, using the User Device Key for this
     * session as the secret key.
     *
     * The User Device Key is intentionally not exposed to the caller. To
     * maintain security, callers should not attempt to decrypt the User
     * Device Key or keep a reference to it. Callers should perform encryption
     * and decryption only by calls to the UserSession.decrypt() and .encrypt()
     * methods. */
    public encrypt(cryptoService: CryptoService, payload: Payload): Promise<CipherText> {
        return this.decryptUserDeviceKey(cryptoService).then(userDeviceKey =>
            cryptoService.encrypt(payload, userDeviceKey)
        );
    }

    /** Decrypts the User Device Key.
     *
     * This method is private since callers should not keep a reference to the
     * unencrypted User Device Key.
     *
     * The User Device Key should only be kept unencrypted while the user is
     * logged in, and only then for as long as it is needed.
     *
     * Callers should perform encryption and decryption only by calling the
     * UserSession.encrypt() and .decrypt() methods. */
    private decryptUserDeviceKey(cryptoService: CryptoService): Promise<ArrayBufferView> {
        let encryptedUserDeviceKey = this.userDevice.getUserDeviceKeyCipherMessage();

        return Promise.resolve(
            cryptoService
                .decrypt(encryptedUserDeviceKey, this.credentialsKey)
                .then(payload => payload.secretData!!)
        );
    }

    public static fromDto(userSessionDto: UserSessionDto, userDevice: UserDevice) {
        var userDeviceId = Uuid.parse(userSessionDto.userDeviceId);

        if (
            userSessionDto.username != userDevice.getUsername() ||
            !userDeviceId.equals(userDevice.getUserDeviceId())
        ) {
            throw new Error("UserSession does not match UserDevice.");
        }

        return new UserSession(userDevice, base64.decodeFromString(userSessionDto.credentialsKey));
    }
}
