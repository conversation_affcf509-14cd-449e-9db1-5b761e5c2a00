import {UserDevice} from "ecco-dto";
import {CipherTextDto} from "@eccosolutions/ecco-crypto";

export interface SecurityDtoRepository {
    /** Finds the UserDevice representing the user with the specified username
     * on this device. */
    findOneUserDeviceByUsername(username: string): Promise<UserDevice>;

    /** Delete the UserDevice with the specified username.  This will invalidate any local data and would be used
     *  only if a UserDevice is not found at the remote end (which is only really possible to happen if a database
     *  has been copied to a test instance - overwriting the valid UserDevices table with those from another instance). */
    deleteUserDeviceByUsername(username: string): Promise<void>;

    /** Registers a UserDevice record to represent the user with the specified
     * username on this device.
     *
     * If there is already a UserDevice record for the specified user, then the
     * existing record will be replaced. */
    registerUserDevice(
        username: string,
        credentialsKeySaltBase64: string,
        userDeviceId: string,
        encryptedUserDeviceKey: CipherTextDto
    ): Promise<UserDevice>;
}
