import {omit} from "lodash"
import {
    EccoDate,
    EccoDateTime,
    HateoasResource,
    Slice,
    WebApiError
} from "@eccosolutions/ecco-common";
import {Command, CommandRepository, MergeableCommand} from "ecco-commands";

import {
    BaseServiceRecipientCommandDto,
    CalendarRepository,
    Client,
    ClientRepository,
    CommandRequestDto,
    ConfigResolverDefault,
    EncryptedEvidenceDtos,
    EncryptedFormEvidence,
    EncryptedRiskWork,
    EncryptedSignature,
    EncryptedSupportWork,
    EvidenceAttachment,
    EventResourceDto,
    EvidenceGroup,
    FlagArea,
    FlagEvidenceDto,
    FormDefinition,
    FormEvidence,
    FormEvidenceRepository,
    QuestionnaireAnswersSnapshotDto,
    ReferralDto,
    ReferralRepository,
    ReferralSummaryDto,
    ReferralSummaryWithEntities,
    ReferralWithEntities,
    RelatedRelationship,
    ReportCriteriaDto,
    RiskEvidenceRepository,
    RiskFlagsSnapshotDto,
    RiskGroupEvidenceDto,
    RiskWorkEvidenceDto,
    SecureCommandRepository,
    SecureEventResource,
    SecurePayloadDto,
    ServiceDto,
    ServiceRecipient,
    ServiceRecipientAssociatedContact,
    ServiceRecipientRepository,
    ServiceRecipientTaskBaseCommandDto as ReferralTaskBaseCommand,
    ServiceRecipientWithEntities,
    ServiceRepository,
    Services,
    ServiceType,
    SessionData,
    SessionDataDto,
    SessionDataGlobal,
    SessionDataRepository,
    Signature,
    SignatureRepository,
    SupportSmartStepsSnapshotDto,
    SupportSmartStepsSnapshotRepository,
    SupportWork,
    SupportWorkRepository,
    UserSessionDataDto,
    WorkflowDto,
    WorkflowDtoRepository
} from "ecco-dto";
import {
    Database,
    DtoCryptoService,
    EncryptedClientDto,
    EncryptedQuestionAnswersSnapshot,
    EncryptedReferralDto,
    EncryptedSupportSmartStepsSnapshot,
    EncryptedWorkflow,
    NOT_SUPPORTED,
    OfflineSchema,
    OfflineSyncStatus,
    OfflineSyncStatusEvent,
    ReadWriteTransaction,
    SecureCalendarAjaxRepository,
    SecureClientAjaxRepository,
    SecureFormEvidenceAjaxRepository,
    SecureQuestionnaireWorkAjaxRepository,
    SecureReferralAjaxRepository,
    SecureRiskEvidenceAjaxRepository,
    SecureServiceAjaxRepository,
    SecureSessionDataAjaxRepository,
    SecureSignatureAjaxRepository,
    SecureSupportWorkAjaxRepository,
    SecureWorkflowAjaxRepository,
    UserSessionManager
} from "./index";
import {keyFirstBy} from "@softwareventures/array";

export type AllSecureDtos = SecurePayloadDto | EncryptedEvidenceDtos;

/** We store the default that gets returned as "current user" */
const FEATURE_CFG_KEY = "current user";

function isMergeableCommand(command: any): command is MergeableCommand {
    return command.getCommandTargetUri != undefined;
}

function assertNumber(n: any) {
    if (typeof n !== "number") {
        console.error("Expected number but got " + typeof n);
        throw new TypeError("Expected number but got " + typeof n);
    }
}

function throwNotSupported<T>(): Promise<T> {
    throw new Error("not supported offline");
}

// function filterNonNull<T>(stream: (T | null)[]): T[] {
//     return stream.filter(it => it != null) as T[];
// }

function filterEmptyArrays<T>(arrayStream: T[][]): T[][] {
    return arrayStream.filter(it => it.length > 0);
}

/**
 * Offline repository that implements all interfaces.
 */
export class OfflineRepository
    implements
        ReferralRepository,
        ClientRepository,
        SupportWorkRepository,
        RiskEvidenceRepository,
        WorkflowDtoRepository,
        ServiceRepository,
        ServiceRecipientRepository,
        SupportSmartStepsSnapshotRepository,
        SessionDataRepository,
        CommandRepository,
        CalendarRepository,
        FormEvidenceRepository,
        SignatureRepository
{
    constructor(
        private database: Promise<Database<OfflineSchema>>,
        private upstreamSessionDataRepository: SecureSessionDataAjaxRepository,
        private upstreamCalendarRepository: SecureCalendarAjaxRepository,
        private upstreamCommandRepository: SecureCommandRepository,
        private upstreamReferralRepository: SecureReferralAjaxRepository,
        private upstreamClientRepository: SecureClientAjaxRepository,
        private upstreamSupportWorkRepository: SecureSupportWorkAjaxRepository,
        private upstreamRiskEvidenceRepository: SecureRiskEvidenceAjaxRepository,
        private upstreamFormEvidenceRepository: SecureFormEvidenceAjaxRepository,
        private upstreamQuestionnaireWorkRepository: SecureQuestionnaireWorkAjaxRepository,
        private upstreamWorkflowRepository: SecureWorkflowAjaxRepository,
        private upstreamServiceRepository: SecureServiceAjaxRepository,
        private upstreamSignatureRepository: SecureSignatureAjaxRepository,
        private userSessionManager: UserSessionManager,
        private dtoCryptoService: DtoCryptoService
    ) {}

    /** SPECIFIC OFFLINE METHODS */
    public countMessageQueue(): Promise<number> {
        return this.database.then((database: Database<OfflineSchema>) =>
            database
                .count(OfflineSchema.COMMAND_QUEUE)
                .then(commandQueueCount =>
                    database
                        .count(OfflineSchema.EVIDENCE_COMMANDS)
                        .then(evidenceCount => commandQueueCount + evidenceCount)
                )
        );
    }

    public countReferrals(): Promise<number> {
        return this.database.then((database: Database<OfflineSchema>) =>
            database.count(OfflineSchema.REFERRALS)
        );
    }

    public findAllReferralsForOffline(): Promise<ReferralDto[]> {
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findAll<EncryptedReferralDto>(OfflineSchema.REFERRALS)
            )
            .then((secureDtos: EncryptedReferralDto[]): Promise<ReferralDto[]> => {
                const dtos: Promise<ReferralDto>[] = secureDtos.map(secureDto =>
                    this.dtoCryptoService.decryptAndUnmangle(secureDto)
                );
                return Promise.all(dtos);
            });
    }
    findAllReferralSummary(reportDto: ReportCriteriaDto): Promise<ReferralDto[]> {
        return Promise.resolve([]);
    }

    /** INTERFACES */

    public findOneSignature(uuid: string): Promise<Signature> {
        return (
            this.database
                // was this in the OfflineSignatureRepository
                //.then(database => database.findOne<EncryptedSignature>(OfflineSchema.SIGNATURES, id))
                .then(database =>
                    database.findOneBy<EncryptedSignature>(
                        OfflineSchema.SIGNATURES,
                        OfflineSchema.SIGNATURES_BY_UUID,
                        uuid
                    )
                )
                .then(encryptedSignature =>
                    this.dtoCryptoService.decryptAndUnmangle(encryptedSignature)
                )
        );
    }

    public findOneReferral(referralId: number): Promise<ReferralDto> {
        assertNumber(referralId);
        return this.database
            .then(database =>
                database.findOne<EncryptedReferralDto>(OfflineSchema.REFERRALS, referralId)
            )
            .then(secureDto => this.dtoCryptoService.decryptAndUnmangle(secureDto));
    }

    public findOneReferralByServiceRecipientId(serviceRecipientId: number): Promise<ReferralDto> {
        assertNumber(serviceRecipientId);
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findBy<EncryptedReferralDto>(
                    OfflineSchema.REFERRALS,
                    OfflineSchema.REFERRAL_BY_SERVICERECIPIENTID_INDEX,
                    serviceRecipientId
                )
            )
            .then(secureDtos => {
                if (secureDtos.length == 0) {
                    throw new Error("This referral was not synchronised offline");
                } else if (secureDtos.length > 1) {
                    throw new TypeError("Found > 1 referrals for srId=" + serviceRecipientId);
                }
                return this.dtoCryptoService.decryptAndUnmangle(secureDtos[0]);
            });
    }

    public findOneServiceRecipientWithEntities(
        serviceRecipientId: number
    ): Promise<ServiceRecipientWithEntities> {
        assertNumber(serviceRecipientId);
        return this.findOneReferralWithEntities(serviceRecipientId);
    }

    public findOneReferralWithEntities(serviceRecipientId: number): Promise<ReferralWithEntities> {
        assertNumber(serviceRecipientId);
        const referralPromise = this.findOneReferralByServiceRecipientId(serviceRecipientId);
        const featuresPromise = this.getSessionData();
        return Promise.all([referralPromise, featuresPromise]).then(([referral, features]) => {
            const result = <ReferralWithEntities>referral;
            result.features = features;
            result.configResolver = ConfigResolverDefault.fromServiceRecipient(
                features,
                result.serviceAllocationId,
                result.serviceTypeId
            );
            return result;
        });
    }

    public findOneReferralSummaryWithEntitiesUsingDto(
        serviceRecipientId: number
    ): Promise<ReferralSummaryWithEntities> {
        // ReferralSummaryDto extends ServiceRecipient (plain and secret), ReferralSummarySecretFields
        // Referral extends ReferralPlainFields (some ids along with ServiceRecipient plain), ReferralSecretFields (SignpostedFields, ServiceRecipient secret, ReferralSummarySecretFields)
        // therefore Referral extends everything ReferralSummaryDto does
        return this.findOneReferralWithEntities(serviceRecipientId); // TODO: Review what will be different. We need clear line between aggregate and relations
    }

    public findOneReferralSummaryByServiceRecipientIdUsingDto(
        serviceRecipientId: number
    ): Promise<ReferralSummaryDto> {
        return this.findOneReferralSummaryWithEntitiesUsingDto(serviceRecipientId);
    }

    public findAllReferralSummaryByServiceRecipientId(
        serviceRecipientIds: number[] | string[]
    ): Promise<ReferralSummaryDto[]> {
        return Promise.resolve([]);
    }

    findAssociatedContactsByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<ServiceRecipientAssociatedContact[]> {
        return throwNotSupported();
    }

    public findAllReferralsInsideBuilding(buildingId: number): Promise<ReferralDto[]> {
        return throwNotSupported();
    }

    public findAllReferralWithoutSecuritySummaryByClient(
        clientId: number
    ): Promise<ReferralSummaryDto[]> {
        return throwNotSupported();
    }

    public findFormDefinition(uuid: string): Promise<FormDefinition | null> {
        return uuid
            ? this.getSessionData().then(config => config.findFormDefinition(uuid))
            : Promise.resolve(null);
    }

    public findLatestFormEvidenceSnapshotByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<FormEvidence<any> | null> {
        assertNumber(serviceRecipientId);
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findOne<EncryptedFormEvidence<any>[]>(
                    OfflineSchema.FORM_SNAPSHOTS,
                    serviceRecipientId
                )
            )
            .then((secureDtos: EncryptedFormEvidence<any>[]): Promise<FormEvidence<any> | null> => {
                // the upstream SecureFormEvidenceAjaxRepository uses handle404AsNullResult, so we need to allow for null here
                const dtos: Promise<FormEvidence<any>>[] = secureDtos
                    ? secureDtos.map(secureDto =>
                          this.dtoCryptoService.decryptAndUnmangle(secureDto)
                      )
                    : [];
                return Promise.all(dtos).then(
                    dtos =>
                        dtos.find(
                            dto => dto.evidenceGroupKey == evidenceGroup.name
                        ) as FormEvidence<any> | null
                );
            });
    }

    public findOneFormEvidenceWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<FormEvidence<any> | null> {
        return this.findLatestFormEvidenceSnapshotByServiceRecipientId(
            serviceRecipientId,
            evidenceGroup
        );
    }

    public findAllFormEvidenceByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        pageNumber?: number,
        attachmentsOnly?: boolean
    ): Promise<FormEvidence<any>[]> {
        return throwNotSupported();
    }

    public findAllBetweenFormEvidenceByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        fromDateTime: EccoDateTime,
        toDateTime: EccoDateTime
    ): Promise<FormEvidence<any>[]> {
        return throwNotSupported();
    }

    public findOneCommand(
        uuid: string,
        optional?: boolean
    ): Promise<BaseServiceRecipientCommandDto> {
        return throwNotSupported();
    }

    public findLatestCommandPerTaskName(
        serviceRecipientId: number
    ): Promise<ReferralTaskBaseCommand[]> {
        return Promise.resolve([]);
    }

    public sendCommand(command: Command | MergeableCommand): Promise<undefined> {
        if (isMergeableCommand(command)) {
            return this.sendCommandInternal(command.getCommandTargetUri(), command.toDto());
        } else {
            const dto = command.toCommandDto();
            return this.sendCommandInternal(dto.commandUri, dto);
        }
    }

    public exchangeCommand<T extends HateoasResource>(
        command: Command | MergeableCommand
    ): Promise<T> {
        return throwNotSupported();
    }

    private sendCommandInternal(targetUri: string, dto: any): Promise<undefined> {
        const path = `/api/${targetUri}`;

        const commandRequestDto = new CommandRequestDto("POST", path, JSON.stringify(dto));
        let payload = this.userSessionManager.findCurrentUserSession().then(userSession =>
            this.dtoCryptoService.encrypt(
                // TODO: Review with Dan
                {
                    secret: commandRequestDto,
                    plain: {guid: userSession.getUserDevice().getUserDeviceId().toString()}
                }
            )
        );

        return this.database
            .then(database =>
                payload.then(payload =>
                    database.save(OfflineSchema.COMMAND_QUEUE, {
                        name: "sendSecureCommand",
                        arguments: [payload]
                    })
                )
            )
            .then(() => undefined);
    }

    public fetchCalendarsByContactIds(
        contactIds: number[],
        start: EccoDate,
        end: EccoDate
    ): Promise<EventResourceDto[]> {
        return Promise.resolve([]); // FIXME: should try online or throw
    }

    public fetchEventsByCalendarIdAndDate(
        calendarIds: string[],
        start: EccoDate,
        end: EccoDate
    ): Promise<EventResourceDto[]> {
        return Promise.resolve([]);
    }

    /**
     * Fetch nearby (-3 days -> +2 wks) calendar events for specified calendar
     * @param calendarId
     * @param nearby ignored for offline, today is assumed
     */
    public nearby(calendarId: string, nearby?: EccoDate): Promise<EventResourceDto[]> {
        const dtos = this.database
            .then((database: Database<OfflineSchema>) =>
                database.findBy<SecureEventResource>(
                    OfflineSchema.EVENTS,
                    OfflineSchema.EVENTS_BY_CALENDARID,
                    calendarId
                )
            )
            .then((secureDtos: SecureEventResource[]) =>
                secureDtos.map(secureDto => this.dtoCryptoService.decryptAndUnmangle(secureDto))
            );
        return dtos.then(unwrapped => Promise.all(unwrapped)); // Explicit type to help compiler find right signature
    }
    public fetchEventsById(eventIds: string[]): Promise<EventResourceDto[]> {
        return Promise.resolve([]); // FIXME: should try online or throw
    }

    public fetchEventsByCalendarId(
        calendarIds: string[],
        start: EccoDateTime,
        end: EccoDateTime
    ): Promise<EventResourceDto[]> {
        return Promise.resolve([]); // should try online or throw
    }

    public findOneClient(clientId: number): Promise<Client> {
        assertNumber(clientId);
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findOne<EncryptedClientDto>(OfflineSchema.CLIENTS, clientId)
            )
            .then(secureDto => this.dtoCryptoService.decryptAndUnmangle(secureDto));
    }

    public findOneExternalClientBySourceAndRef(
        externalSourceName: string,
        externalRef: string
    ): Promise<Client> {
        throw new Error("not implemented offline");
    }

    public findOneClientByServiceRecipientId(serviceRecipientId: number): Promise<Client> {
        throw new Error("not implemented offline");
        //return Promise.resolve({} as Client)
    }

    public findAllClients(): Promise<Client[]> {
        return this.findAllSecureClients().then(
            (secureDtos: EncryptedClientDto[]): Promise<Client[]> => {
                const dtos: Promise<Client>[] = secureDtos.map(secureDto =>
                    this.dtoCryptoService.decryptAndUnmangle(secureDto)
                );
                return Promise.all(dtos);
            }
        );
    }

    private findAllSecureClients(): Promise<EncryptedClientDto[]> {
        return this.database.then((database: Database<OfflineSchema>) =>
            database.findAll<EncryptedClientDto>(OfflineSchema.CLIENTS)
        );
    }

    public findRiskWorkByServiceRecipientId(
        serviceRecipientId: number,
        pageNumber?: number,
        attachmentsOnly?: boolean,
        serviceIds?: number[]
    ): Promise<RiskWorkEvidenceDto[]> {
        assertNumber(serviceRecipientId);
        return this.findSecureRiskWorkByServiceRecipientId(serviceRecipientId).then(
            (secureDtos: EncryptedRiskWork[]): Promise<RiskWorkEvidenceDto[]> => {
                const dtos: Promise<RiskWorkEvidenceDto>[] = secureDtos.map(secureDto =>
                    this.dtoCryptoService.decryptAndUnmangle(secureDto)
                );
                return Promise.all(dtos)
                    .then(dtos =>
                        dtos.sort((a, b) => {
                            const workSort = b.workDate.localeCompare(a.workDate); // IS8601 is char-sortable
                            return workSort != 0
                                ? workSort
                                : b.createdDate.localeCompare(a.createdDate);
                        })
                    )
                    .then(dtos => {
                        return attachmentsOnly
                            ? dtos.filter(dto => dto.attachments!!.length > 0)
                            : dtos;
                    });
            }
        );
    }

    findRiskFlagsSnapshotByClientId(clientId: number): Promise<RiskFlagsSnapshotDto> {
        return throwNotSupported();
    }
    findRiskSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
        serviceRecipientId: number,
        workDate: EccoDateTime
    ): Promise<SupportSmartStepsSnapshotDto> {
        return throwNotSupported();
    }

    public findRiskWorkByClientId(
        clientId: number,
        pageNumber?: number,
        attachmentsOnly?: boolean
    ): Promise<RiskWorkEvidenceDto[]> {
        return throwNotSupported();
    }

    findRiskAreaEvidenceByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<RiskGroupEvidenceDto[]> {
        return throwNotSupported();
    }

    findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number,
        evidenceGroup?: EvidenceGroup
    ): Promise<RiskFlagsSnapshotDto> {
        return throwNotSupported();
    }

    public findSupportWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        pageNumber?: number,
        attachmentsOnly?: boolean,
        hactOnly?: boolean,
        statusChangeOnly?: boolean,
        serviceIds?: number[]
    ): Promise<SupportWork[]> {
        assertNumber(serviceRecipientId);
        return this.findSecureSupportWorkByServiceRecipientId(serviceRecipientId).then(
            (secureDtos: EncryptedSupportWork[]): Promise<SupportWork[]> => {
                const dtos: Promise<SupportWork>[] = secureDtos
                    // TODO: add evidenceGroup to view model .filter(secureDto => secureDto.plain.evidenceGroup == evidenceGroup)
                    .map(secureDto => this.dtoCryptoService.decryptAndUnmangle(secureDto));
                return Promise.all(dtos)
                    .then(dtos =>
                        dtos.sort((a, b) => {
                            const workSort = b.workDate.localeCompare(a.workDate); // IS8601 is char-sortable
                            return workSort != 0
                                ? workSort
                                : b.createdDate.localeCompare(a.createdDate);
                        })
                    )
                    .then(dtos => {
                        return attachmentsOnly
                            ? dtos.filter(dto => dto.attachments!!.length > 0)
                            : dtos;
                    });
            }
        );
    }

    public findSupportWorkByServiceRecipientIdFirstPage(
        serviceRecipientId: number,
        evidenceGroup = EvidenceGroup.needs
    ): Promise<Slice<SupportWork>> {
        assertNumber(serviceRecipientId);
        return this.findSupportWorkByServiceRecipientId(serviceRecipientId, evidenceGroup).then(
            dtos => ({
                content: dtos,
                first: true,
                last: true,
                size: dtos.length // We return a single slice with all the offline results
            })
        );
    }

    public findSupportWorkByServiceRecipientIdAndRiskManagementOutstanding(
        serviceRecipientId: number
    ): Promise<SupportWork[]> {
        assertNumber(serviceRecipientId);
        return this.findSupportWorkByServiceRecipientId(serviceRecipientId, EvidenceGroup.needs) // TODO: allow all evidence groups to be synced
            .then((allWork: SupportWork[]) => {
                return allWork.filter(
                    work => work.riskManagementRequired && work.riskManagementHandled == null
                );
            });
    }

    public findOneSupportWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        workUuid: string
    ): Promise<SupportWork> {
        return throwNotSupported();
    }

    public findOneRiskWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        workUuid: string
    ): Promise<RiskWorkEvidenceDto> {
        return throwNotSupported();
    }

    /**
     * See SupportWorkAjaxRepository.findEvidenceSnapshotsByServiceRecipientId
     * TODO: Filter for evidenceGroupKey (can't use index cos IE10/11 don't support compound)
     */
    public findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number,
        evidenceGroupKey: string
    ) {
        assertNumber(serviceRecipientId);
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findBy<EncryptedSupportSmartStepsSnapshot>(
                    OfflineSchema.SUPPORT_SNAPSHOTS,
                    OfflineSchema.SUPPORT_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX,
                    serviceRecipientId
                )
            )
            .then(
                (
                    secureDtos: EncryptedSupportSmartStepsSnapshot[]
                ): Promise<SupportSmartStepsSnapshotDto> =>
                    this.dtoCryptoService.decryptAndUnmangle(secureDtos[0])
            );
    }

    findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
        serviceRecipientId: number,
        evidenceGroupKey: string,
        workDate: EccoDateTime
    ): Promise<SupportSmartStepsSnapshotDto> {
        return throwNotSupported();
    }

    public findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<QuestionnaireAnswersSnapshotDto> {
        assertNumber(serviceRecipientId);
        return (
            this.database
                .then((database: Database<OfflineSchema>) =>
                    database.findBy<EncryptedQuestionAnswersSnapshot>(
                        OfflineSchema.QUESTIONNAIRE_SNAPSHOTS,
                        OfflineSchema.QUESTIONNAIRE_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX,
                        serviceRecipientId
                    )
                )
                // filter by evidenceGroup
                .then((secureDtos: EncryptedQuestionAnswersSnapshot[]) =>
                    secureDtos.filter(
                        plainDto => plainDto.plain.evidenceGroupKey == evidenceGroup.name
                    )
                )
                .then(
                    (
                        secureDtos: EncryptedQuestionAnswersSnapshot[]
                    ): Promise<QuestionnaireAnswersSnapshotDto> =>
                        this.dtoCryptoService.decryptAndUnmangle(secureDtos[0])
                )
        );
    }

    public findLatestFlagsByServiceRecipientIdAndEvidenceGroupKey(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<FlagEvidenceDto[]> {
        return Promise.resolve([]);
    }

    private findSecureRiskWorkByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<EncryptedRiskWork[]> {
        return this.database.then((database: Database<OfflineSchema>) =>
            database.findBy<EncryptedRiskWork>(
                OfflineSchema.RISK_WORK,
                OfflineSchema.RISK_WORK_BY_SERVICERECIPIENTID_INDEX,
                serviceRecipientId
            )
        );
    }

    private findSecureSupportWorkByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<EncryptedSupportWork[]> {
        return this.database.then((database: Database<OfflineSchema>) =>
            database.findBy<EncryptedSupportWork>(
                OfflineSchema.SUPPORT_WORK,
                OfflineSchema.SUPPORT_WORK_BY_SERVICERECIPIENTID_INDEX,
                serviceRecipientId
            )
        );
    }

    /*
    public findOneService(serviceId: number): Promise<Service> {
        assertNumber(serviceId);
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findOne<ServiceDto>(OfflineSchema.SERVICES, serviceId))
            .then((dto) => new Service(dto));
    }
    */
    public findOneServiceRecipientById(serviceRecipientId: number): Promise<ServiceRecipient> {
        return throwNotSupported();
    }

    public findManyServiceRecipientByIds(
        serviceRecipientIds: number[]
    ): Promise<ServiceRecipient[]> {
        return throwNotSupported();
    }

    findServiceRecipientDraftCommands(
        serviceRecipientId: number,
        taskName: string,
        userId: number
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return throwNotSupported();
    }

    public findServiceRecipientTaskCommandsByCreated(
        serviceRecipientId: number,
        pageNumber: number,
        taskName: string
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return throwNotSupported();
    }

    findServiceRecipientCommandsBySearch(
        criteria: ReportCriteriaDto,
        pageNumber: number | null
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return throwNotSupported();
    }

    public findServiceRecipientCommandsByCreated(
        serviceRecipientId: number,
        pageNumber: number | null
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return throwNotSupported();
    }

    public findServiceRecipientAttachments(
        serviceRecipientId: number
    ): Promise<EvidenceAttachment[]> {
        return throwNotSupported();
    }

    fetchStatusAreaEvidenceFlags(
        sessionData: SessionData,
        serviceRecipientId: number,
        serviceType: ServiceType
    ): Promise<FlagArea[]> {
        return throwNotSupported();
    }
    findExternalFlagsBySourceAndRef(
        externalSourceName: string,
        externalRef: string
    ): Promise<FlagEvidenceDto[]> {
        return throwNotSupported();
    }

    public findAllServices(): Promise<Services> {
        return throwNotSupported();
    }

    public findOneServiceDto(serviceId: number): Promise<ServiceDto> {
        return throwNotSupported();
    }

    public findAllServicesProjects(): Promise<ServiceDto[]> {
        throw new Error("findAllServicesProjects is not available offline");
    }

    public findOneWorkflowByServiceRecipientId(serviceRecipientId: number): Promise<WorkflowDto> {
        assertNumber(serviceRecipientId);
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findBy<EncryptedWorkflow>(
                    OfflineSchema.WORKFLOWS,
                    OfflineSchema.WORKFLOWS_BY_SERVICERECIPIENT_INDEX,
                    serviceRecipientId
                )
            )
            .then(
                (secureDtos: EncryptedWorkflow[]): Promise<WorkflowDto> =>
                    this.dtoCryptoService.decryptAndUnmangle(secureDtos[0])
            );
    }

    public findRelatedReferrals(referralId: number): Promise<RelatedRelationship[]> {
        return throwNotSupported();
    }

    public findUnfilteredReferralsByClientName(
        firstName: string,
        lastName: string
    ): Promise<ReferralDto[]> {
        return throwNotSupported();
    }

    public hideReferral(referral: {referralId: number}): Promise<void> {
        return throwNotSupported();
    }

    public unhideReferral(referral: {referralId: number}): Promise<void> {
        return throwNotSupported();
    }

    /** Return an array of arrays - we can flatten this when we commit it to the database */
    private findFormSnapshots(
        userDeviceId: string,
        referralsQ: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedFormEvidence<any>[][]> {
        return referralsQ
            .then((referrals: EncryptedReferralDto[]) => {
                return Promise.all(
                    referrals.map(referral =>
                        this.upstreamFormEvidenceRepository.findLatestFormEvidenceSnapshotByServiceRecipientId(
                            userDeviceId,
                            referral.plain.serviceRecipientId
                        )
                    )
                );
            })
            .then(filterEmptyArrays);
    }

    /** Return promise for array of all support work for the given referrals array */
    private findRiskWorkByReferrals(
        userDeviceId: string,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedRiskWork[]> {
        return referrals.then((referrals: EncryptedReferralDto[]) => {
            const supportWorkGroups = Promise.all(
                referrals.map(referral =>
                    this.upstreamRiskEvidenceRepository.findRiskWorkByServiceRecipientId(
                        userDeviceId,
                        referral.plain.serviceRecipientId
                    )
                )
            );

            return supportWorkGroups.then(supportWorkGroups =>
                supportWorkGroups.reduce(
                    (supportWork, supportWorkGroup) => supportWork.concat(supportWorkGroup),
                    []
                )
            );
        });
    }

    /** Return promise for array of all support work for the given referrals array */
    private findSupportWorkByReferrals(
        userDeviceId: string,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedSupportWork[]> {
        return referrals.then((referrals: EncryptedReferralDto[]) => {
            const supportWorkGroups = Promise.all(
                referrals.map(referral =>
                    this.upstreamSupportWorkRepository.findSupportWorkByServiceRecipientId(
                        userDeviceId,
                        referral.plain.serviceRecipientId
                    )
                )
            );

            return supportWorkGroups.then(supportWorkGroups =>
                supportWorkGroups.reduce(
                    (supportWork, supportWorkGroup) => supportWork.concat(supportWorkGroup),
                    []
                )
            );
        });
    }

    private retrieveServices(
        userDeviceId: string,
        sessionData: SessionData,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<ServiceDto[]> {
        const serviceCatIds = referrals.then(referrals =>
            referrals.map(referral => referral.plain.serviceAllocationId)
        );

        return serviceCatIds.then(svcCatIds => {
            const serviceIds = svcCatIds.map(
                sc => sessionData.getServiceCategorisation(sc).serviceId
            );
            return Promise.all(
                serviceIds
                    .sort((a, b) => a - b) // So that we can remove duplicates efficiently.
                    .filter((serviceId, index, serviceIds) => serviceId != serviceIds[index - 1]) // Remove duplicates.
                    .map(serviceId =>
                        this.upstreamServiceRepository
                            .findOne(userDeviceId, serviceId)
                            .then(service => this.dtoCryptoService.decryptAndUnmangle(service))
                    )
            );
        });
    }

    private retrieveWorkflows(
        userDeviceId: string,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedWorkflow[]> {
        return referrals.then((referrals: EncryptedReferralDto[]) => {
            return Promise.all(
                referrals.map(referral => {
                    return this.upstreamWorkflowRepository.findOneWorkflowByServiceRecipientId(
                        userDeviceId,
                        referral.plain.serviceRecipientId
                    );
                })
            );
        });
    }

    /** retrieve EvidenceSnapshot for each referral */
    private retrieveSmartStepEvidenceSnapshots(
        userDeviceId: string,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedSupportSmartStepsSnapshot[]> {
        return referrals.then((referrals: EncryptedReferralDto[]) => {
            return Promise.all(
                referrals.map(referral => {
                    return this.upstreamSupportWorkRepository.findSupportSmartStepsSnapshotsByServiceRecipientId(
                        userDeviceId,
                        referral.plain.serviceRecipientId
                    );
                })
            );
        });
    }
    private retrieveQuestionnaireEvidenceSnapshots(
        userDeviceId: string,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedQuestionAnswersSnapshot[]> {
        return referrals.then((referrals: EncryptedReferralDto[]) => {
            return Promise.all(
                referrals.map(referral => {
                    return this.upstreamQuestionnaireWorkRepository.findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey(
                        userDeviceId,
                        referral.plain.serviceRecipientId,
                        EvidenceGroup.fromName("TODO - all qnr?")
                    ); // TODO loop all that are configured now
                })
            );
        });
    }
    /** retrieve Client for each referral */
    private retrieveClients(
        userDeviceId: string,
        referrals: Promise<EncryptedReferralDto[]>
    ): Promise<EncryptedClientDto[]> {
        return referrals.then((referrals: EncryptedReferralDto[]) => {
            return Promise.all(
                referrals
                    .map(referral => referral.plain.clientId)
                    .sort((a, b) => a - b) // So that we can remove duplicates efficiently.
                    .filter((clientId, index, clientIds) => clientId != clientIds[index - 1]) // Remove duplicates.
                    .map(clientId =>
                        this.upstreamClientRepository.findOneClient(userDeviceId, clientId)
                    )
            );
        });
    }

    private retrieveDefaultSessionData(userDeviceId: string): Promise<UserSessionDataDto> {
        return this.upstreamSessionDataRepository
            .getUserConfig(userDeviceId)
            .then(featureConfig => this.dtoCryptoService.decryptAndUnmangle(featureConfig));
    }

    private retrieveEvents(
        userDeviceId: string,
        sessionData: Promise<UserSessionDataDto>
    ): Promise<SecureEventResource[]> {
        return sessionData.then(sessionData =>
            this.upstreamCalendarRepository.nearby(userDeviceId, sessionData.calendarId)
        );
    }

    private retrieveSignatures(
        userDeviceId: string,
        supportWork: Promise<EncryptedSupportWork[]>
    ): Promise<EncryptedSignature[]> {
        return supportWork
            .then(supportWork => {
                const withSigs = supportWork
                    .filter(it => it.plain.signatureId != null)
                    .map(it => it.plain.signatureId);
                const firstBySigId = keyFirstBy(withSigs, it => it);
                return Object.values(firstBySigId).map(signatureId =>
                    this.upstreamSignatureRepository.findOneSignature(userDeviceId, signatureId)
                );
            })
            .then(signatures => Promise.all(signatures));
    }

    /** Note: this can be called expecting to get some features, for which we'll return SessionData(null) */
    public getSessionData(): Promise<SessionData> {
        return this.getSessionDataDto().then(dto => new SessionData(dto));
    }

    public getSessionDataGlobal(): Promise<SessionDataGlobal> {
        return this.getSessionDataDto().then(dto => new SessionDataGlobal(dto));
    }

    public getSessionDataDto(): Promise<SessionDataDto> {
        return this.database
            .then((database: Database<OfflineSchema>) =>
                database.findOne<UserSessionDataDto>(OfflineSchema.FEATURE_CONFIG, FEATURE_CFG_KEY)
            )
            .then(user =>
                this.upstreamSessionDataRepository
                    .getSessionData()
                    .then(global => ({...user, ...global}))
            );
    }

    private syncInboundDataToOfflineDatabase(): Promise<void> {
        return this.getSessionData().then(sessionData => {
            this.userSessionManager.findCurrentUserSession().then(userSession => {
                const userDeviceId = userSession.getUserDevice().getUserDeviceId().toString();
                OfflineSyncStatusEvent.bus.fire(
                    new OfflineSyncStatusEvent(OfflineSyncStatus.RECEIVING, 0.2, "Receiving:")
                );

                // referral related data - 'referrals' returned are used to load remaining items
                const since = this.userSessionManager.getLastSyncInstant();
                const referrals = this.upstreamReferralRepository.findAllReferralsForOffline(
                    userDeviceId,
                    since
                );
                const clients = this.retrieveClients(userDeviceId, referrals);
                const services = this.retrieveServices(userDeviceId, sessionData, referrals);
                const supportWork = this.findSupportWorkByReferrals(userDeviceId, referrals);
                const workflows = this.retrieveWorkflows(userDeviceId, referrals);
                const supportSnapshots = this.retrieveSmartStepEvidenceSnapshots(
                    userDeviceId,
                    referrals
                );
                const questionnaireSnapshots = Promise.resolve([]); // this.retrieveQuestionnaireEvidenceSnapshots(userDeviceId, referrals);
                const formSnapshots = this.findFormSnapshots(userDeviceId, referrals);
                const riskWork = this.findRiskWorkByReferrals(userDeviceId, referrals);
                const signatures = this.retrieveSignatures(userDeviceId, supportWork);

                // more static data
                const userConfig = this.retrieveDefaultSessionData(userDeviceId);

                const events = this.retrieveEvents(userDeviceId, userConfig);

                const receivePromises: Promise<any>[] = [
                    referrals,
                    workflows,
                    clients,
                    supportWork,
                    services,
                    supportSnapshots,
                    questionnaireSnapshots,
                    formSnapshots,
                    userConfig,
                    riskWork,
                    signatures,
                    events
                ];

                const promisesCount = receivePromises.length;
                let promisesFulfilled = 0;

                receivePromises.forEach(promise =>
                    promise.then(() => {
                        ++promisesFulfilled;
                        OfflineSyncStatusEvent.bus.fire(
                            new OfflineSyncStatusEvent(
                                OfflineSyncStatus.RECEIVING,
                                0.2 + (promisesFulfilled / promisesCount) * 0.8,
                                "Receiving: " +
                                    promisesFulfilled +
                                    "/" +
                                    promisesCount +
                                    " completed"
                            )
                        );
                    })
                );

                return (
                    Promise.all([this.database].concat(receivePromises) as unknown[]) as Promise<
                        [
                            Database<OfflineSchema>,
                            EncryptedReferralDto[],
                            EncryptedWorkflow[],
                            EncryptedClientDto[],
                            EncryptedSupportWork[],
                            ServiceDto[], // NOTE: service is already decrypted
                            EncryptedSupportSmartStepsSnapshot[],
                            EncryptedQuestionAnswersSnapshot[],
                            EncryptedFormEvidence<any>[][],
                            UserSessionDataDto,
                            EncryptedRiskWork[],
                            EncryptedSignature[],
                            SecureEventResource[]
                        ]
                    >
                )
                    .then(
                        ([
                            database,
                            referrals,
                            workflows,
                            clients,
                            supportWork,
                            services,
                            supportSnapshots,
                            questionnaireSnapshots,
                            formSnapshotsBySrId,
                            userConfig,
                            riskWork,
                            signatures,
                            events
                        ]) => {
                            function storeReceivedData(
                                transaction: ReadWriteTransaction<void>
                            ): Promise<any>[] {
                                let promises: Promise<any>[] = referrals.map(referral =>
                                    transaction.save(
                                        OfflineSchema.REFERRALS,
                                        referral,
                                        referral.plain.referralId
                                    )
                                );

                                promises = promises.concat(
                                    workflows.map(workflow =>
                                        transaction.save(
                                            OfflineSchema.WORKFLOWS,
                                            workflow,
                                            workflow.plain.serviceRecipientId
                                        )
                                    )
                                );

                                promises = promises.concat(
                                    clients.map(client =>
                                        transaction.save(
                                            OfflineSchema.CLIENTS,
                                            client,
                                            client.plain.clientId
                                        )
                                    )
                                );

                                promises = promises.concat(
                                    supportWork.map(supportWork =>
                                        // put each work into the database under uuid
                                        transaction.save(
                                            OfflineSchema.SUPPORT_WORK,
                                            supportWork,
                                            supportWork.plain.id
                                        )
                                    )
                                );

                                promises = promises.concat(
                                    services.map(service =>
                                        transaction.save(
                                            OfflineSchema.SERVICES,
                                            service,
                                            service.id
                                        )
                                    )
                                );

                                promises = promises.concat(
                                    supportSnapshots.map(snapshot => {
                                        // put the single snapshot into the database under referralId (TODO put under serviceRecipientId)
                                        return transaction.save(
                                            OfflineSchema.SUPPORT_SNAPSHOTS,
                                            snapshot,
                                            snapshot.plain.parentId
                                        );
                                    })
                                );

                                promises = promises.concat(
                                    questionnaireSnapshots.map(snapshot => {
                                        // put the single snapshot into the database under serviceRecipientId
                                        // NB the id of the entry is no resemblence to the retrieved id
                                        return transaction.save(
                                            OfflineSchema.QUESTIONNAIRE_SNAPSHOTS,
                                            snapshot,
                                            snapshot.plain.serviceRecipientId
                                        );
                                    })
                                );

                                promises = promises.concat(
                                    formSnapshotsBySrId.map(snapshots => {
                                        // put the single snapshot into the database under serviceRecipientId
                                        // NB the id of the entry is no resemblence to the retrieved id
                                        return transaction.save(
                                            OfflineSchema.FORM_SNAPSHOTS,
                                            snapshots,
                                            snapshots[0].plain.serviceRecipientId
                                        );
                                    })
                                );

                                promises.push(
                                    transaction.save(
                                        OfflineSchema.FEATURE_CONFIG,
                                        userConfig,
                                        FEATURE_CFG_KEY
                                    )
                                );

                                promises = promises.concat(
                                    riskWork.map(riskWork =>
                                        // put each work into the database under uuid
                                        transaction.save(
                                            OfflineSchema.RISK_WORK,
                                            riskWork,
                                            riskWork.plain.id
                                        )
                                    )
                                );

                                promises = promises.concat(
                                    signatures.map(signature =>
                                        transaction.save(
                                            OfflineSchema.SIGNATURES,
                                            signature,
                                            signature.plain.id
                                        )
                                    )
                                );

                                return promises.concat(
                                    events.map(event =>
                                        transaction.save(
                                            OfflineSchema.EVENTS,
                                            event,
                                            event.plain.uid
                                        )
                                    )
                                );
                            }

                            return database.withReadWriteTransaction<void>(
                                /* NOTE: You need to include it in the transaction! */
                                OfflineSchema.SYNCED_TABLES,
                                (transaction: ReadWriteTransaction<void>) => {
                                    let promises = storeReceivedData(transaction);

                                    Promise.all(promises)
                                        .then(() => {
                                            transaction.resolve();
                                            this.userSessionManager.updateLastSync(
                                                userSession.getUserDevice().getUsername()
                                            );
                                            OfflineSyncStatusEvent.bus.fire(
                                                new OfflineSyncStatusEvent(
                                                    OfflineSyncStatus.COMPLETE,
                                                    1,
                                                    "Sync completed."
                                                )
                                            );
                                        })
                                        .catch((reason: any) => {
                                            transaction.reject(reason);
                                            throw reason;
                                        });
                                }
                            );
                        }
                    )
                    .catch((reason: WebApiError) => {
                        OfflineSyncStatusEvent.bus.fire(
                            new OfflineSyncStatusEvent(
                                OfflineSyncStatus.RECEIVE_FAILED,
                                0,
                                (reason && reason.toString()) || "Inbound sync failed."
                            )
                        );
                        throw reason;
                    });
            });
        });
    }

    private syncOutboundDataToServer(): Promise<void> {
        const _repos = this;
        function sendCommands(
            database: Database<OfflineSchema>,
            tableName: string,
            commands: {id: number; payload: AllSecureDtos}[]
        ): Promise<void> {
            const handleCommand = (i: number): Promise<void> => {
                if (i < commands.length) {
                    const command = commands[i];
                    return _repos
                        .sendSecureCommand(command.payload)
                        .then(() => database.remove(tableName, command.id))
                        .then(() => {
                            OfflineSyncStatusEvent.bus.fire(
                                new OfflineSyncStatusEvent(
                                    OfflineSyncStatus.SENDING,
                                    (i / commands.length) * 0.2,
                                    "Sending: " +
                                        (i + 1).toString() +
                                        "/" +
                                        commands.length.toString() +
                                        " completed"
                                )
                            );
                            return handleCommand(i + 1);
                        });
                } else {
                    return Promise.resolve();
                }
            };

            return handleCommand(0);
        }

        return this.database
            .then((database: Database<OfflineSchema>): Promise<void> => {
                OfflineSyncStatusEvent.bus.fire(
                    new OfflineSyncStatusEvent(OfflineSyncStatus.SENDING, 0, "Sending:")
                );
                return database
                    .findAll<{id: number; arguments: [SecurePayloadDto]}>(
                        OfflineSchema.COMMAND_QUEUE
                    )
                    .then(commands =>
                        commands.map(command => ({id: command.id, payload: command.arguments[0]}))
                    )
                    .then(commands => sendCommands(database, OfflineSchema.COMMAND_QUEUE, commands))
                    .then(() =>
                        database.findAll<EncryptedEvidenceDtos>(OfflineSchema.EVIDENCE_COMMANDS)
                    )
                    .then((commands: EncryptedEvidenceDtos[]) =>
                        sendCommands(
                            database,
                            OfflineSchema.EVIDENCE_COMMANDS,
                            commands.map(command => ({id: command.id!!, payload: command}))
                        )
                    )
                    .then(() =>
                        OfflineSyncStatusEvent.bus.fire(
                            new OfflineSyncStatusEvent(OfflineSyncStatus.SENT, 0.2, "Sent all")
                        )
                    );
            })
            .catch((reason: any): void => {
                OfflineSyncStatusEvent.bus.fire(
                    new OfflineSyncStatusEvent(OfflineSyncStatus.SEND_FAILED, null, "Send failed.")
                );
                console.error("Failed to submit command queue: %o", reason);
            });
    }

    public update(): Promise<void> {
        return this.syncOutboundDataToServer()
            .then((): Promise<void> => this.syncInboundDataToOfflineDatabase())
            .catch(reason => {
                OfflineSyncStatusEvent.bus.fire(
                    new OfflineSyncStatusEvent(OfflineSyncStatus.FAILED, 0, "Update failed.")
                );
                throw reason;
            });
    }

    /** Deletes all data stored in the offline repository. */
    public clear(): Promise<void> {
        return this.database
            .then(database =>
                database.deleteDatabase().catch(reason => {
                    if (reason == NOT_SUPPORTED) {
                        return database.withReadWriteTransaction<void>(
                            OfflineSchema.TABLES_TO_CLEAR,
                            (transaction: ReadWriteTransaction<void>) => {
                                OfflineSchema.TABLES_TO_CLEAR.forEach(table => {
                                    database.clear(table);
                                });
                                transaction.resolve();
                            }
                        );
                    } else {
                        throw reason;
                    }
                })
            )
            .then(() => this.userSessionManager.logout())
            .then(() => this.userSessionManager.clearLastSync());
    }

    /** This matches SecureCommandProxy.submitSecureCommand which takes {secret, plain} DTOs */
    private sendSecureCommand(body: AllSecureDtos): Promise<void> {
        if ((<EncryptedEvidenceDtos>body).id != undefined) {
            // TODO: Use interface type tests here - review with Dan
            body = omit<AllSecureDtos>(body, "id") as AllSecureDtos;
        }
        return this.upstreamCommandRepository.sendCommand(body as SecurePayloadDto); // TODO: Hacky
    }
}
