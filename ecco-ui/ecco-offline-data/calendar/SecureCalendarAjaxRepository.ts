import {ApiClient, SecureEventResource} from "ecco-dto";


export class SecureCalendarAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    /** Fetch nearby (-1 wk -> +4 wks) calendar events for specifiedcalendar */
    public nearby(userDeviceId: string, calendarId: string): Promise<SecureEventResource[]> {
        return this.apiClient.secureGet<SecureEventResource[]>(userDeviceId,
            "calendar/event/nearby",
            { query: { calendarId } });
    }
}
