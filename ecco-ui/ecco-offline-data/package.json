{"private": true, "name": "ecco-offline-data", "version": "0.0.0", "license": "UNLICENSED", "exports": {".": "./build-tsc/index.js"}, "sideEffects": false, "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts .", "build": "eslint --ext .ts . && webpack", "lint": "eslint --ext .ts .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "jest", "test-sequential": "echo Nothing to do"}, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "application-properties": "0.0.0", "bowser": "^1.9.4", "delay": "^4.3.0", "ecco-commands": "0.0.0", "ecco-dto": "0.0.0", "moment": "2.24.0", "rxjs": "^5.1.1"}, "devDependencies": {"@testing-library/dom": "^8.11.3", "@testing-library/jest-dom": "^5.11.4", "@types/jest": "^29.5.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-loader": "^8.0.6", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "jest": "^29.5.0", "jest-cli": "^29.5.0", "terser-webpack-plugin": "^4.2.3", "ts-jest": "^29.1.0", "typescript": "5.8.3", "webpack": "^5.101.0", "webpack-cli": "^6.0.1"}}