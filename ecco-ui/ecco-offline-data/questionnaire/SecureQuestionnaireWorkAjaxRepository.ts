
import {Encrypted} from "@eccosolutions/ecco-common";
import {EvidenceGroup, ApiClient} from "ecco-dto";
import {QuestionnaireAnswersSnapshotPlainFields, QuestionnaireAnswersSnapshotSecretFields} from "ecco-dto";

export interface EncryptedQuestionAnswersSnapshot extends Encrypted<QuestionnaireAnswersSnapshotSecretFields, QuestionnaireAnswersSnapshotPlainFields> { }

export class SecureQuestionnaireWorkAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    /** See QuestionnaireWorkAjaxRepository.findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey */
    findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey(
        userDeviceId: string,
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup): Promise<EncryptedQuestionAnswersSnapshot> {
        if (!evidenceGroup) {
            throw new Error("evidenceGroup must be specified");
        }
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/questionnaire/snapshots/${evidenceGroup.name}/`;

        return this.apiClient.secureGet<EncryptedQuestionAnswersSnapshot>(userDeviceId, apiPath);
    }

}
