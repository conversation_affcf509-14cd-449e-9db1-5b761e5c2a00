import {Encrypted} from "@eccosolutions/ecco-common";
import {ApiClient, Client<PERSON>lainFields, ClientSecretFields} from 'ecco-dto';

/** A client (encrypted). */
export interface EncryptedClientDto extends Encrypted<ClientSecretFields, ClientPlainFields> {
}

export class SecureClientAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    findOneClient(userDeviceId: string, clientId: number): Promise<EncryptedClientDto> {
        return this.apiClient.secureGet<EncryptedClientDto>(userDeviceId, `clients/${clientId}/`);
    }

    findOneClientByReferralId(userDeviceId: string, referralId: number): Promise<EncryptedClientDto> {
        return this.apiClient.secureGet<EncryptedClientDto>(userDeviceId, `referrals/${referralId}/client/`);
    }

    findAllClients(userDeviceId: string): Promise<EncryptedClientDto[]> {
        return this.apiClient.secureGet<EncryptedClientDto[]>(userDeviceId, "clients/");
    }
}

