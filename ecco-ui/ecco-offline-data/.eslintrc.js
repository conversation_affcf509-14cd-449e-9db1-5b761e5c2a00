module.exports = {
    "env": {
        "browser": true,
        "es6": true
    },
    "extends": [
        // "eslint:recommended",
        // "plugin:@typescript-eslint/eslint-recommended"
    ],
    "globals": {
        "Atomics": "readonly",
        "SharedArrayBuffer": "readonly"
    },
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "ecmaVersion": 2018,
        "sourceType": "module"
    },
    "plugins": [
        "@typescript-eslint"
    ],
    "rules": {
        "no-restricted-imports": ["error", {
            "paths": ["ecco-offline-data", "..", "."],
            "patterns": [
                "ecco-offline-data/*",
                "ecco-commands/**",
                "**/build-tsc/**"
            ]
        }],
        // "no-unused-vars": "warn" // This doesn't work as it marks everything exported as unused - should rely on tsc or IDEA instead
    }
};