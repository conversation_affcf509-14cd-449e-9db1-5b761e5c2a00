import {CipherText, cipherTextFromDto} from "@eccosolutions/ecco-crypto";
import {UserEncryptionDto} from "ecco-dto";

// FIXME (or delete me) Seems to be unused!
export class OfflineUser {
    private username: string;
    private userDeviceId: string;
    private credentialsKeySalt: string;
    private userDeviceKeyCipherMessage: CipherText;

    constructor(userDto: UserEncryptionDto) {
        if (!userDto) {
            throw new TypeError("userDto must not be null");
        }

        this.username = userDto.username;
        this.userDeviceId = userDto.userDeviceId;
        this.credentialsKeySalt = userDto.credentialsKeySalt;
        this.userDeviceKeyCipherMessage = cipherTextFromDto(userDto.userDeviceKeyCipherMessage);
    }

    public getUsername(): string {
        return this.username;
    }

    public getUserDeviceId(): string {
        return this.userDeviceId;
    }

    public getCredentialsKeySalt(): string {
        return this.credentialsKeySalt;
    }

    public getUserDeviceKeyCipherMessage(): CipherText {
        return this.userDeviceKeyCipherMessage;
    }
}
