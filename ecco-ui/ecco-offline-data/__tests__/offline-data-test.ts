import {EvidenceRepositoryEffectsWrapper} from "../evidence/EvidenceRepositoryEffectsWrapper";
import {CommandDtoRepository, EvidenceCommandDtoConverter, FormEvidenceWork} from "ecco-commands";
import {Observable} from "rxjs";
import {EvidenceDtos, EvidenceGroup, FormEvidenceRepository} from "ecco-dto";
import {waitFor} from '@testing-library/dom'


const mockFormEvidenceRepository = (
    impls: Partial<FormEvidenceRepository>
): FormEvidenceRepository => ({
    findAllFormEvidenceByServiceRecipientId: impls.findAllFormEvidenceByServiceRecipientId || (() => Promise.reject("findAllFormEvidenceByServiceRecipientId mock is not implemented")),
    findFormDefinition: impls.findFormDefinition || (() => Promise.reject("findFormDefinition mock is not implemented")),
    findAllBetweenFormEvidenceByServiceRecipientId: impls.findAllBetweenFormEvidenceByServiceRecipientId || (() => Promise.reject("findAllBetweenFormEvidenceByServiceRecipientId mock is not implemented")),
    findOneFormEvidenceWorkByServiceRecipientId: impls.findOneFormEvidenceWorkByServiceRecipientId || (() => Promise.reject("findOneFormEvidenceWorkByServiceRecipientId mock is not implemented")),
    findLatestFormEvidenceSnapshotByServiceRecipientId: impls.findLatestFormEvidenceSnapshotByServiceRecipientId || (() => Promise.reject("findLatestFormEvidenceSnapshotByServiceRecipientId mock is not implemented"))

})

const failOnError = error => {
    console.log("got error:", error);
    fail(error);
}

const converter = new EvidenceCommandDtoConverter();

describe("EvidenceRepositoryEffectsWrapper", () => {

    it.skip("effects wrapper emits single null result if no form found", async () => {
        let flag = false;
        let commandEffectsReceived = [];
        let firstResult: FormEvidenceWork<any> | null | undefined = undefined;

        const mockPerhaps: CommandDtoRepository<EvidenceDtos> = {
            submitCommand<T extends EvidenceDtos>(commandDto: T): Promise<T> {
                console.log("Command submitted: " + JSON.stringify(commandDto));
                return Promise.resolve(commandDto);
            },
            findPendingCommands(): Observable<EvidenceDtos> {
                console.log("findPendingCommands");
                return Observable.from([]);
            }
        };
        const repo = new EvidenceRepositoryEffectsWrapper(mockPerhaps, converter, {
            getRiskWorkRepository: () => fail(),
            getSupportWorkRepository: () => fail(),
            getFormEvidenceRepository: () =>
                mockFormEvidenceRepository({
                    findLatestFormEvidenceSnapshotByServiceRecipientId: () => Promise.resolve(null)
                }),
            getSignatureRepository: () => fail()
        });
        const subscription = repo.findLatestFormSnapshotByServiceRecipientId(
            111,
            EvidenceGroup.fromSameTaskName("referralDetails")
        );
        subscription.getSnapshots().then(result => {
            console.info("got result: ", result);
            firstResult = result[0][1];
            flag = true;
            console.log("finished"); // TODO: should we be getting complete if we are expecting effects from New() or Change() effects?
        }, failOnError);
        subscription.getEffects().subscribe(effect => {
            console.log("Got effect: " + JSON.stringify(effect));
            commandEffectsReceived.push(effect);
        });
        return waitFor(
            () => {
                expect(flag).toBeTruthy();
                expect(firstResult).toBeNull();
                expect(commandEffectsReceived.length).toBe(0); // NOTE: .toBeEmpty() is on .d.ts but is not found - expect() is extended in mysterious ways
            },
            {timeout: 150}
        );
    });

    it.skip("we should get null, then an effect emitted when we subsequently save a form", () => {});

    it.skip("decrypts form data from offline", () => {});

    it.skip("correctly shows offline changes to form that's modified offline", () => {});
});
