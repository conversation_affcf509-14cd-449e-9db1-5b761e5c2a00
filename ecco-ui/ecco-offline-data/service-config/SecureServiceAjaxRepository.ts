import {Encrypted} from "@eccosolutions/ecco-common";
import {
    ApiClient,
    ServicePlainFields,
    ServiceSecretFields,
    ServiceTypePlainFields,
    ServiceTypeSecretFields
} from "ecco-dto";

export interface SecureService extends Encrypted<ServiceSecretFields, ServicePlainFields> {
}

export interface SecureServiceType extends Encrypted<ServiceTypeSecretFields, ServiceTypePlainFields> {
}
export class SecureServiceAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    public findOne(userDeviceId: string, serviceId: number): Promise<SecureService> {
        return this.apiClient.secureGet<SecureService>(userDeviceId, `service/${serviceId}/`);
    }
}
