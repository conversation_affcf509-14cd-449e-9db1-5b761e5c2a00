import {Observable} from "rxjs";

// Long stack support generates lots of exceptions to get stack traces, so avoid it in production
// TODO: This was RxJS v4. if (window.location.href.indexOf("//localhost") >= 0) { RxConfig.longStackSupport = true; }

/** Utility functions for asynchronous operations. */


/** Converts a promise for a synchronous sequence to a Reactive Observable. */
export function promiseToSequence<T>(sequence: Promise<ArrayLike<T>>): Observable<T> {
    return Observable.fromPromise(sequence).flatMap(a => a);
}


