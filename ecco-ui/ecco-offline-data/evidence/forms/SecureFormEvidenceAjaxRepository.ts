import {ApiClient, EncryptedFormEvidence, handle404AsNullR<PERSON>ult} from "ecco-dto";

export class SecureFormEvidenceAjaxRepository {
    constructor(private apiClient: ApiClient) {}

    /** For all evidence groups */
    public findLatestFormEvidenceSnapshotByServiceRecipientId(
        userDeviceId: string,
        serviceRecipientId: number
    ): Promise<EncryptedFormEvidence<any>[]> {
        let apiPath = `service-recipients/${serviceRecipientId}/evidence/form/snapshots/latest/`;

        return this.apiClient
            .secureGet<EncryptedFormEvidence<any>[]>(userDeviceId, apiPath)
            .catch(handle404AsNullResult)
            .then(r => (r == null ? [] : r)); // because we'll be asking for it for some referrals
    }
}
