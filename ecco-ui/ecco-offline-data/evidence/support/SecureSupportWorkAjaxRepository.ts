import {
    ApiClient,
    EncryptedSupportWork,
    EvidenceGroup,
    SupportSmartStepsSnapshotPlainFields,
    SupportSmartStepsSnapshotSecretFields
} from "ecco-dto";
import {Encrypted} from "@eccosolutions/ecco-common";

/** A snapshot of the evidence state (e.g. needs assessment/support plan state) (encrypted). */
export interface EncryptedSupportSmartStepsSnapshot extends Encrypted<SupportSmartStepsSnapshotSecretFields, SupportSmartStepsSnapshotPlainFields> { }

export class SecureSupportWorkAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    findSupportWorkByServiceRecipientId(userDeviceId: string, serviceRecipientId: number): Promise<EncryptedSupportWork[]> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/all/`;
        return this.apiClient.secureGet<EncryptedSupportWork[]>(userDeviceId, apiPath);
    }

    /** See SupportWorkAjaxRepository.findEvidenceSnapshotsByServiceRecipientId */
    findSupportSmartStepsSnapshotsByServiceRecipientId(userDeviceId: string, serviceRecipientId: number): Promise<EncryptedSupportSmartStepsSnapshot> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/snapshots/latest/`;
        return this.apiClient.secureGet<EncryptedSupportSmartStepsSnapshot>(userDeviceId, apiPath);
    }
}
