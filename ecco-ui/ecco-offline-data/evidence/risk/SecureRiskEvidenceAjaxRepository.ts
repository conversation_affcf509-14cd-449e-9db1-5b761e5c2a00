import {ApiClient, EncryptedRiskWork, EvidenceGroup} from "ecco-dto";

export class SecureRiskEvidenceAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    findRiskWorkByServiceRecipientId(userDeviceId: string, serviceRecipientId: number): Promise<EncryptedRiskWork[]> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.threat.name}/all/`;
        return this.apiClient.secureGet<EncryptedRiskWork[]>(userDeviceId, apiPath);
    }

}
