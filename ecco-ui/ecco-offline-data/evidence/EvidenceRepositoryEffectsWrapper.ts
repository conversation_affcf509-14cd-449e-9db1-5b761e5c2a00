import {
    CollectionSubscription,
    CommandDtoRepository,
    EvidenceCommand,
    EvidenceCommandDtoConverter,
    FormEvidenceWork,
    ObjectSubscription,
    RepositoryBaseEffectsWrapper,
    RiskWork,
    Signature,
    SupportWork
} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    CommandDto,
    EvidenceGroup,
    FormEvidenceRepository,
    RiskEvidenceRepository,
    SignatureRepository,
    SignWorkCommandDto,
    SupportWorkRepository
} from "ecco-dto";


interface OfflineRepositories {
    readonly getFormEvidenceRepository: () => FormEvidenceRepository;
    readonly getRiskWorkRepository: () => RiskEvidenceRepository;
    readonly getSignatureRepository: () => SignatureRepository;
    readonly getSupportWorkRepository: () => SupportWorkRepository;
}

/**
 * Migrate to this class, as its designed to be used online and offline.
 * When online we don't technically need the subscription/effects model as we have direct
 * access to updated data, but when offline this is key. To avoid handling online/offline
 * separately we can use this class.
 * NB we can't implement the interfaces separately (to keep the method names in sync)
 * because we need to return ObjectSubscription and CollectionSubscription objects.
 */
export class EvidenceRepositoryEffectsWrapper extends RepositoryBaseEffectsWrapper<
    EvidenceCommand,
    CommandDto
> {
    constructor(
        commandDtoRepository: CommandDtoRepository<SignWorkCommandDto>,
        commandDtoConverter: EvidenceCommandDtoConverter,
        private apis: OfflineRepositories
    ) {
        super(commandDtoRepository, commandDtoConverter);
    }

    /** SignatureRepository */
    public findOneSignature(uuid: Uuid): ObjectSubscription<Signature> {
        const signature = this.apis
            .getSignatureRepository()
            .findOneSignature(uuid.toString())
            .then(signatureDto => Signature.fromDto(signatureDto));

        return this.objectSubscription(uuid.toString(), signature);
    }

    /** SupportRepository */
    public findSupportWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup = EvidenceGroup.needs,
        pageNumber?: number,
        attachmentsOnly?: boolean,
        hactOnly?: boolean,
        statusChangeOnly?: boolean,
        serviceIds?: number[]
    ): CollectionSubscription<SupportWork> {
        const snapshots = this.apis
            .getSupportWorkRepository()
            .findSupportWorkByServiceRecipientId(
                serviceRecipientId,
                evidenceGroup,
                pageNumber,
                attachmentsOnly,
                undefined,
                undefined,
                serviceIds
            )
            .then(it => it.map(dto => SupportWork.fromDto(dto)));
        return this.collectionSubscription(
            work =>
                work instanceof SupportWork &&
                work.getDto().serviceRecipientId == serviceRecipientId, // TODO: filter effects to those that match evidenceGroup
            snapshots
        );
    }

    public findOneSupportWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup = EvidenceGroup.needs,
        workUuid: string
    ): CollectionSubscription<SupportWork> {
        const snapshot = this.apis
            .getSupportWorkRepository()
            .findOneSupportWorkByWorkUuid(serviceRecipientId, evidenceGroup, workUuid)
            .then(dto => dto && [SupportWork.fromDto(dto)]);
        return this.collectionSubscription(
            work => work.getDto().serviceRecipientId == serviceRecipientId, // TODO: filter effects to those that match evidenceGroup
            snapshot
        );
    }

    /** RiskRepository */
    public findRiskWorkByServiceRecipientId(
        serviceRecipientId: number,
        pageNumber?: number,
        attachmentsOnly?: boolean,
        serviceIds?: number[]
    ): CollectionSubscription<RiskWork> {
        const snapshots = this.apis
            .getRiskWorkRepository()
            .findRiskWorkByServiceRecipientId(
                serviceRecipientId,
                pageNumber,
                attachmentsOnly,
                serviceIds
            )
            .then(it => it.map(dto => RiskWork.fromDto(dto)));
        return this.collectionSubscription(
            work =>
                work instanceof RiskWork && work.getDto().serviceRecipientId == serviceRecipientId,
            snapshots
        );
    }
    public findRiskWorkByClientId(
        clientId: number,
        pageNumber?: number,
        attachmentsOnly?: boolean
    ): CollectionSubscription<RiskWork> {
        const snapshots = this.apis
            .getRiskWorkRepository()
            .findRiskWorkByClientId(clientId, pageNumber, attachmentsOnly)
            .then(it => it.map(dto => RiskWork.fromDto(dto)));
        return this.collectionSubscription(
            work => work instanceof RiskWork, // TODO: filter effects to those that match the client
            snapshots
        );
    }

    // NB This should return ObjectSubscription (like findOneSignature above, but the usage is )
    public findOneRiskWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup = EvidenceGroup.threat,
        workUuid: string
    ): CollectionSubscription<RiskWork> {
        const snapshot = this.apis
            .getRiskWorkRepository()
            .findOneRiskWorkByWorkUuid(serviceRecipientId, evidenceGroup, workUuid)
            .then(dto => dto && [RiskWork.fromDto(dto)]);
        return this.collectionSubscription(
            work => work.getDto().serviceRecipientId == serviceRecipientId, // TODO: filter effects to those that match evidenceGroup
            snapshot
        );
    }

    public findOneFormEvidenceWorkByWorkUuid(
        serviceRecipientId: number,
        workUuid: string
    ): CollectionSubscription<FormEvidenceWork> {
        const snapshot = this.apis
            .getFormEvidenceRepository()
            .findOneFormEvidenceWorkByServiceRecipientId(serviceRecipientId, null, workUuid)
            .then(dto => (dto ? [FormEvidenceWork.fromDto(dto)] : []));
        return this.collectionSubscription<FormEvidenceWork>(
            work => work.getDto().serviceRecipientId == serviceRecipientId, // TODO: filter effects to those that match evidenceGroup
            snapshot
        );
    }

    public findLatestFormSnapshotByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): CollectionSubscription<FormEvidenceWork> {
        const snapshots =
            // We keep collectionSubscription because we don't have objectUuid that objectSubscription would need
            this.apis
                .getFormEvidenceRepository()
                .findLatestFormEvidenceSnapshotByServiceRecipientId(
                    serviceRecipientId,
                    evidenceGroup
                )
                .then(it => (it == null ? [] : [FormEvidenceWork.fromDto(it)]));
        return this.collectionSubscription<FormEvidenceWork>(
            work =>
                work instanceof FormEvidenceWork &&
                work.getDto().serviceRecipientId == serviceRecipientId,
            snapshots
        );
    }

    public findFormEvidenceWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        pageNumber?: number,
        attachmentsOnly?: boolean
    ): CollectionSubscription<FormEvidenceWork> {
        const snapshots =
            // We keep collectionSubscription because we don't have objectUuid that objectSubscription would need
            this.apis
                .getFormEvidenceRepository()
                .findAllFormEvidenceByServiceRecipientId(
                    serviceRecipientId,
                    evidenceGroup,
                    pageNumber,
                    attachmentsOnly
                )
                .then(evidences => evidences.map(it => FormEvidenceWork.fromDto(it)));
        return this.collectionSubscription<FormEvidenceWork>(
            work =>
                work instanceof FormEvidenceWork &&
                work.getDto().serviceRecipientId == serviceRecipientId,
            snapshots
        );
    }
}
