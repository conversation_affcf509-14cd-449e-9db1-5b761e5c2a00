import {Schema} from "./indexed-db";

// These are no longer in production use, so deleted in Nov 2016 release
const RISK_WORK_BY_REFERRALID_INDEX = "IDX_RISK_WORK_REFERRALID";
const SUPPORT_WORK_BY_REFERRALID_INDEX = "IDX_SUPP_WORK_REFERRALID";
const EVIDENCE_SNAPSHOTS_BY_PARENTID_INDEX = "IDL_EVD_SNP_PARENTID";

/** The schema for the offline Indexed DB.
 *
 * To modify this schema, you must increment the version number and then add
 * your changes to the end of the upgrade() function. */
export class OfflineSchema implements Schema {
    // Table names
    public static CLIENTS = "clients";
    public static COMMAND_QUEUE = "command_queue";
    public static EVENTS = "events";
    public static EVIDENCE_COMMANDS = "evidence_commands";
    public static SUPPORT_SNAPSHOTS = "evidence_snapshots";
    public static QUESTIONNAIRE_SNAPSHOTS = "questionnaire_snapshots";
    public static FORM_SNAPSHOTS = "form_snapshots";
    public static FEATURE_CONFIG = "feature_config";
    public static REFERRALS = "referrals";
    public static RISK_WORK = "risk_work";
    public static SUPPORT_WORK = "support_work";
    public static USERS = "users";
    public static SERVICES = "services";
    public static SERVICE_TYPES = "service_types";
    public static SIGNATURES = "signatures";
    public static WORKFLOWS = "workflows";

    public static SYNCED_TABLES = [
        OfflineSchema.EVENTS,
        OfflineSchema.SUPPORT_SNAPSHOTS,
        OfflineSchema.QUESTIONNAIRE_SNAPSHOTS,
        OfflineSchema.FORM_SNAPSHOTS,
        OfflineSchema.FEATURE_CONFIG,
        OfflineSchema.REFERRALS,
        OfflineSchema.CLIENTS,
        OfflineSchema.RISK_WORK,
        OfflineSchema.SUPPORT_WORK,
        OfflineSchema.SERVICES,
        OfflineSchema.SERVICE_TYPES,
        OfflineSchema.SIGNATURES,
        OfflineSchema.WORKFLOWS
    ];

    /** This is to allow offline to be started afresh */
    public static TABLES_TO_CLEAR = [
        OfflineSchema.EVENTS,
        OfflineSchema.SUPPORT_SNAPSHOTS,
        OfflineSchema.QUESTIONNAIRE_SNAPSHOTS,
        OfflineSchema.FORM_SNAPSHOTS,
        OfflineSchema.FEATURE_CONFIG,
        OfflineSchema.REFERRALS,
        OfflineSchema.CLIENTS,
        OfflineSchema.RISK_WORK,
        OfflineSchema.SUPPORT_WORK,
        OfflineSchema.SERVICES,
        OfflineSchema.SERVICE_TYPES,
        OfflineSchema.SIGNATURES,
        OfflineSchema.WORKFLOWS,
        OfflineSchema.COMMAND_QUEUE,
        OfflineSchema.USERS
    ];

    // Index names must be unique for WebSQL shim. IndexedDB doesn't care.
    public static REFERRAL_BY_SERVICERECIPIENTID_INDEX = "IDX_REFERRAL_SVC_REC_ID";

    public static RISK_WORK_BY_SERVICERECIPIENTID_INDEX = "IDX_RISK_WORK_SVC_REC_ID";
    public static SUPPORT_WORK_BY_SERVICERECIPIENTID_INDEX = "IDX_SUPP_WORK_SVC_REC_ID";
    public static SUPPORT_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX = "IDX_EVD_SNP_SVC_REC_ID";
    public static QUESTIONNAIRE_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX = "IDX_QNR_SNP_SVC_REC_ID";
    public static SIGNATURES_BY_UUID = "IDX_SIGNATURE_UUID";
    public static EVENTS_BY_CALENDARID = "IDX_EVENT_CAL_ID";
    public static WORKFLOWS_BY_SERVICERECIPIENT_INDEX = "IDX_WORKFLOW_SVC_REC_ID";

    constructor(private databaseName = "ecco") {}

    /** The name of the IndexedDB database. */
    getName(): string {
        return this.databaseName;
    }

    /** The database version number.
     *
     * You must increment this version number whenever the schema changes. */
    getVersion(): number {
        return 15;
    }

    /** Upgrades or creates the database schema.
     *
     * The implementation of this method is the only opportunity to create or
     * modify the database schema. It is called when opening a newly-created
     * database, or when opening an existing database whose version number is
     * less than the version number of this Schema object.
     *
     * This method must be able to create a schema from scratch as well as be
     * able to upgrade a database from any earlier version of the schema to the
     * current version, so implement it carefully.
     *
     * The upgrade takes place in an isolated transaction before the database
     * is available for querying, so it either succeeds or fails as a whole.
     *
     * After the upgrade, the IndexedDB infrastructure automatically sets the
     * database version number to the version number of this Schema object. */
    upgrade(database: IDBDatabase, transaction: IDBTransaction, oldVersion: number) {
        if (!database.objectStoreNames.contains(OfflineSchema.REFERRALS)) {
            database.createObjectStore(OfflineSchema.REFERRALS);
        }

        if (!database.objectStoreNames.contains(OfflineSchema.CLIENTS)) {
            database.createObjectStore(OfflineSchema.CLIENTS);
        }

        if (!database.objectStoreNames.contains(OfflineSchema.SUPPORT_WORK)) {
            database.createObjectStore(OfflineSchema.SUPPORT_WORK);
        }

        if (!database.objectStoreNames.contains(OfflineSchema.COMMAND_QUEUE)) {
            database.createObjectStore(OfflineSchema.COMMAND_QUEUE, {
                keyPath: "id",
                autoIncrement: true
            });
        }

        if (!database.objectStoreNames.contains(OfflineSchema.USERS)) {
            database.createObjectStore(OfflineSchema.USERS, {keyPath: "username"});
        }

        // Schema version 2
        if (!database.objectStoreNames.contains(OfflineSchema.SERVICES)) {
            database.createObjectStore(OfflineSchema.SERVICES);
        }

        if (!database.objectStoreNames.contains(OfflineSchema.SERVICE_TYPES)) {
            database.createObjectStore(OfflineSchema.SERVICE_TYPES);
        }

        // Schema version 3 - NOTE: could be parentId, evidenceGroup, but IE10,11 don't support compound indexes
        // http://codepen.io/cemerick/pen/Itymi, http://caniuse.com/#search=indexedDb
        if (!database.objectStoreNames.contains(OfflineSchema.SUPPORT_SNAPSHOTS)) {
            database.createObjectStore(OfflineSchema.SUPPORT_SNAPSHOTS);
        }

        if (!database.objectStoreNames.contains(OfflineSchema.FEATURE_CONFIG)) {
            database.createObjectStore(OfflineSchema.FEATURE_CONFIG);
        }

        // Schema version 5 was RISK_WORK by referralId
        if (!database.objectStoreNames.contains(OfflineSchema.RISK_WORK)) {
            database.createObjectStore(OfflineSchema.RISK_WORK);
        }

        if (oldVersion < 6) {
            OfflineSchema.deleteNonLowerCaseUsernames(transaction);
        }

        // Schema version 7
        if (!database.objectStoreNames.contains(OfflineSchema.SIGNATURES)) {
            database.createObjectStore(OfflineSchema.SIGNATURES);
        }

        if (oldVersion < 8) {
            transaction
                .objectStore(OfflineSchema.SUPPORT_SNAPSHOTS)
                .createIndex(
                    OfflineSchema.SUPPORT_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX,
                    "serviceRecipientId"
                );
            transaction
                .objectStore(OfflineSchema.SUPPORT_WORK)
                .createIndex(
                    OfflineSchema.SUPPORT_WORK_BY_SERVICERECIPIENTID_INDEX,
                    "serviceRecipientId"
                );
            transaction
                .objectStore(OfflineSchema.RISK_WORK)
                .createIndex(
                    OfflineSchema.RISK_WORK_BY_SERVICERECIPIENTID_INDEX,
                    "serviceRecipientId"
                );
            transaction
                .objectStore(OfflineSchema.REFERRALS)
                .createIndex(
                    OfflineSchema.REFERRAL_BY_SERVICERECIPIENTID_INDEX,
                    "serviceRecipientId"
                );
        }

        if (oldVersion < 9) {
            transaction
                .objectStore(OfflineSchema.SIGNATURES)
                .createIndex(OfflineSchema.SIGNATURES_BY_UUID, "uuid");

            database.createObjectStore(OfflineSchema.EVIDENCE_COMMANDS, {
                keyPath: "id",
                autoIncrement: true
            });
        }

        if (oldVersion < 10) {
            database
                .createObjectStore(OfflineSchema.EVENTS)
                .createIndex(OfflineSchema.EVENTS_BY_CALENDARID, "calendarId");
        }

        if (oldVersion < 11) {
            transaction
                .objectStore(OfflineSchema.REFERRALS)
                .deleteIndex(OfflineSchema.REFERRAL_BY_SERVICERECIPIENTID_INDEX);
            transaction
                .objectStore(OfflineSchema.REFERRALS)
                .createIndex(
                    OfflineSchema.REFERRAL_BY_SERVICERECIPIENTID_INDEX,
                    "plain.serviceRecipientId"
                );

            transaction
                .objectStore(OfflineSchema.EVENTS)
                .deleteIndex(OfflineSchema.EVENTS_BY_CALENDARID);
            transaction
                .objectStore(OfflineSchema.EVENTS)
                .createIndex(OfflineSchema.EVENTS_BY_CALENDARID, "plain.calendarId");

            const snapshots = transaction.objectStore(OfflineSchema.SUPPORT_SNAPSHOTS);
            if (snapshots.indexNames.contains(EVIDENCE_SNAPSHOTS_BY_PARENTID_INDEX)) {
                snapshots.deleteIndex(EVIDENCE_SNAPSHOTS_BY_PARENTID_INDEX);
            }
            snapshots.deleteIndex(OfflineSchema.SUPPORT_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX);
            snapshots.createIndex(
                OfflineSchema.SUPPORT_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX,
                "plain.serviceRecipientId"
            );

            const supportWork = transaction.objectStore(OfflineSchema.SUPPORT_WORK);
            if (supportWork.indexNames.contains(SUPPORT_WORK_BY_REFERRALID_INDEX)) {
                supportWork.deleteIndex(SUPPORT_WORK_BY_REFERRALID_INDEX);
            }
            supportWork.deleteIndex(OfflineSchema.SUPPORT_WORK_BY_SERVICERECIPIENTID_INDEX);
            supportWork.createIndex(
                OfflineSchema.SUPPORT_WORK_BY_SERVICERECIPIENTID_INDEX,
                "plain.serviceRecipientId"
            );

            const riskWork = transaction.objectStore(OfflineSchema.RISK_WORK);
            if (riskWork.indexNames.contains(RISK_WORK_BY_REFERRALID_INDEX)) {
                riskWork.deleteIndex(RISK_WORK_BY_REFERRALID_INDEX);
            }
            riskWork.deleteIndex(OfflineSchema.RISK_WORK_BY_SERVICERECIPIENTID_INDEX);
            transaction
                .objectStore(OfflineSchema.RISK_WORK)
                .createIndex(
                    OfflineSchema.RISK_WORK_BY_SERVICERECIPIENTID_INDEX,
                    "plain.serviceRecipientId"
                );

            transaction
                .objectStore(OfflineSchema.SIGNATURES)
                .deleteIndex(OfflineSchema.SIGNATURES_BY_UUID);
            transaction
                .objectStore(OfflineSchema.SIGNATURES)
                .createIndex(OfflineSchema.SIGNATURES_BY_UUID, "plain.id");
        }

        // schema version 12
        if (!database.objectStoreNames.contains(OfflineSchema.QUESTIONNAIRE_SNAPSHOTS)) {
            database.createObjectStore(OfflineSchema.QUESTIONNAIRE_SNAPSHOTS);
            transaction
                .objectStore(OfflineSchema.QUESTIONNAIRE_SNAPSHOTS)
                .createIndex(
                    OfflineSchema.QUESTIONNAIRE_SNAPSHOTS_BY_SERVICERECIPIENTID_INDEX,
                    "plain.serviceRecipientId"
                );
        }

        // schema version 13
        if (!database.objectStoreNames.contains(OfflineSchema.WORKFLOWS)) {
            database.createObjectStore(OfflineSchema.WORKFLOWS);
            transaction
                .objectStore(OfflineSchema.WORKFLOWS)
                .createIndex(
                    OfflineSchema.WORKFLOWS_BY_SERVICERECIPIENT_INDEX,
                    "plain.serviceRecipientId"
                );
        }

        // schema version 14
        if (!database.objectStoreNames.contains(OfflineSchema.FORM_SNAPSHOTS)) {
            database.createObjectStore(OfflineSchema.FORM_SNAPSHOTS);
        }

        // schema version 15
        if (database.objectStoreNames.contains("referral_comments")) {
            database.deleteObjectStore("referral_comments");
        }
    }

    private static deleteNonLowerCaseUsernames(transaction: IDBTransaction) {
        var usersStore = transaction.objectStore(OfflineSchema.USERS);
        var cursorRequest = usersStore.openCursor();

        cursorRequest.onsuccess = () => {
            var cursor: IDBCursorWithValue | null = cursorRequest.result;

            if (cursor) {
                var user = cursor.value;

                if (
                    typeof user.username != "string" ||
                    user.username != user.username.toLowerCase()
                ) {
                    cursor["delete"]();
                }

                cursor["continue"]();
            }
        };
    }
}