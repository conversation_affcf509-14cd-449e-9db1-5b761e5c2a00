// const config = require("@softwareventures/webpack-config");
//
// module.exports = config({
//     title: "Ecco Commands",
//     vendor: "ecco",
//     entry: "./index.ts"
// });

var path = require("path");
var TerserJsPlugin = require('terser-webpack-plugin');

var projectDir = __dirname;
var distDir = path.resolve(projectDir, "./dist/");
var debugDir = path.resolve(projectDir, "./debug/");

function configuration(mode) {
    var configuration = {
        mode: mode,
        name: mode === "production" ? "prod" : "dev",
        entry: "./index.ts",
        externals: [
            "application-properties",
            "bowser",
            "@eccosolutions/ecco-common",
            "@eccosolutions/ecco-crypto",
            "ecco-dto",
            "lodash",
            "moment",
            "rxjs"
        ],
        module: {
            rules: [
                {
                    test: /\.js$/,
                    loader: "babel-loader",
                    options: {
                        presets: ["@babel/preset-env"]
                    }
                },
                {
                    test: /\.tsx?$/,
                    loader: "esbuild-loader",
                    options: {
                        loader: "tsx", // Or 'ts' if you don't need tsx
                        target: "es2015"
                    },
                    exclude: /(^|\/)node_modules\//
                }
            ]
        },
        resolve: {
            extensions: [".tsx", ".ts", ".js"]
        },
        output: {
            path: mode === "production" ? distDir : debugDir,
            filename: "ecco-offline-data.js",
            libraryTarget: "umd",
            devtoolModuleFilenameTemplate: "[resource-path]?[loaders]"
        }
    };

    if (mode === "production") {
        configuration.optimization = {
            minimizer: [
                new TerserJsPlugin({
                    parallel: true,
                    terserOptions: {
                        compress: {
                            passes: 2
                        }
                    }
                })
            ]
        };
    } else {
        configuration.devtool = "inline-source-map"
    }

    return configuration;
}

module.exports = [configuration("production"), configuration("development")];
