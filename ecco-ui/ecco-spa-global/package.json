{"name": "ecco-spa-global", "version": "0.0.0", "description": "Global variables used by ecco single-page web apps", "license": "UNLICENSED", "exports": {".": {"types": "./index.d.ts"}}, "sideEffects": false, "scripts": {"clean": "echo Nothing to do", "emit": "echo Nothing to do", "lint": "tsc", "build": "echo Nothing to do", "test": "echo Nothing to do"}, "dependencies": {"ecco-dto": "0.0.0"}, "devDependencies": {"@softwareventures/tsconfig": "9.0.0-alpha.1", "typescript": "5.8.3"}}