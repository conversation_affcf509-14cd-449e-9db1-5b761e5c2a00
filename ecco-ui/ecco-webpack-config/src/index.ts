import {webpackConfig, WebpackConfig} from "./webpack-config";
import {Options} from "./options/options";
import {jestConfig, JestConfig} from "./jest-config";
import {WebpackEnv} from "./webpack/env";
import {WebpackArguments} from "./webpack/arguments";

export {Options};

export type Config = WebpackConfig & {
    readonly jest: JestConfig;
};

export function config(options: Options): Config {
    const webpack = webpackConfig(options);
    const config = (webpackEnv: WebpackEnv | undefined, args: WebpackArguments) =>
        webpack(webpackEnv, args);
    config.jest = jestConfig(options);
    return config;
}
