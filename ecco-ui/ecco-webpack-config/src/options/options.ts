import {WebpackEnv} from "../webpack/env";
import {WebpackArguments} from "../webpack/arguments";
import {
    ResolvedSinglePageAppOptions,
    resolveSinglePageAppBuildOptions,
    resolveSinglePageAppTestOptions,
    SinglePageAppOptions
} from "./single-page-app";
import {
    ResolvedScriptOptions,
    resolveScriptBuildOptions,
    resolveScriptTestOptions,
    ScriptOptions
} from "./script";

export type Options = SinglePageAppOptions | ScriptOptions;

export type ResolvedOptions<TMode extends string = "development" | "production"> =
    | ResolvedSinglePageAppOptions<TMode>
    | ResolvedScriptOptions<TMode>;

export async function resolveBuildOptions(
    options: Options,
    webpackEnv: WebpackEnv = {},
    args: WebpackArguments
): Promise<ResolvedOptions> {
    if (options.target === "script") {
        return resolveScriptBuildOptions(options, webpackEnv, args);
    } else {
        return resolveSinglePageAppBuildOptions(options, webpackEnv, args);
    }
}

export async function resolveTestOptions(options: Options): Promise<ResolvedOptions<"test">> {
    if (options.target === "script") {
        return resolveScriptTestOptions(options);
    } else {
        return resolveSinglePageAppTestOptions(options);
    }
}
