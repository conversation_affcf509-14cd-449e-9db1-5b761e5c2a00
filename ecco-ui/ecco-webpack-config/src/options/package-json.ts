import {readFile} from "node:fs/promises";
import {hasProperty} from "unknown";

export async function readPackageJson(path: string): Promise<unknown> {
    try {
        return JSON.parse(await readFile(path, "utf-8"));
    } catch (e: unknown) {
        if (e instanceof SyntaxError) {
            throw new Error(`Invalid package.json at ${path}`);
        } else if (hasProperty(e, "code") && e.code === "ENOENT") {
            throw new Error(`Missing package.json at ${path}`);
        } else {
            throw e;
        }
    }
}
