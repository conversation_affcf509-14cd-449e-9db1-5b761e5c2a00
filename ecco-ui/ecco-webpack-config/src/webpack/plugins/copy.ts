import CopyPlugin from "copy-webpack-plugin";
import {posix, sep} from "node:path";
import {ResolvedSinglePageAppOptions} from "../../options/single-page-app";

export function copyPlugin(options: ResolvedSinglePageAppOptions) {
    return new CopyPlugin({
        patterns: [
            {
                from: options.paths.public,
                to: options.paths.build,
                globOptions: {ignore: [options.paths.html.split(sep).join(posix.sep)]}
            }
        ]
    });
}
