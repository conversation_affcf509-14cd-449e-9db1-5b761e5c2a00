import HtmlWebpackPlugin from "html-webpack-plugin";
import {ResolvedSinglePageAppOptions} from "../../options/single-page-app";

export function htmlWebpackPlugin(options: ResolvedSinglePageAppOptions) {
    return new HtmlWebpackPlugin({
        inject: true,
        template: options.paths.html,
        templateParameters: options.clientEnv,
        ...(options.mode === "production"
            ? {
                  minify: {
                      removeComments: true,
                      collapseWhitespace: true,
                      removeRedundantAttributes: true,
                      useShortDoctype: true,
                      removeEmptyAttributes: true,
                      removeStyleLinkTypeAttributes: true,
                      keepClosingSlash: true,
                      minifyJS: true,
                      minifyCSS: true,
                      minifyURLs: true
                  }
              }
            : {})
    });
}
