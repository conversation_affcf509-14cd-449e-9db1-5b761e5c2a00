import {RuleSetRule} from "webpack";
import {ResolvedOptions} from "../../options/options";

export default function babelLoader(options: ResolvedOptions): RuleSetRule {
    // Process any JS outside of the app with Babel.
    return {
        test: /\.[cm]?js$/,
        exclude: /@babel(?:\/|\\{1,2})runtime/,
        loader: require.resolve("babel-loader"),
        options: {
            babelrc: false,
            configFile: false,
            compact: false,
            presets: [[require.resolve("babel-preset-react-app/dependencies"), {helpers: true}]],
            cacheDirectory: true,
            // See https://github.com/facebook/create-react-app/issues/6846
            // for context on why cacheCompression is disabled
            cacheCompression: false,

            // Babel sourcemaps are needed for debugging into node_modules
            // code.  Without the options below, debuggers like VSCode
            // show incorrect code and set breakpoints on the wrong lines.
            sourceMaps: options.sourceMap,
            inputSourceMap: options.sourceMap
        },
        type: "javascript/auto"
    };
}
