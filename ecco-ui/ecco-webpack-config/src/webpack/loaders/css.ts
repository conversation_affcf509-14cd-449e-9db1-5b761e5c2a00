import MiniCssExtractPlugin from "mini-css-extract-plugin";
import postcssNormalize from "postcss-normalize";
import postcssPresetEnv from "postcss-preset-env";
import {RuleSetRule, RuleSetUse} from "webpack";
import {ResolvedOptions} from "../../options/options";

export function cssLoaders(options: ResolvedOptions): RuleSetRule[] {
    // CSS Modules are supported with the extension .module.css
    // (https://github.com/css-modules/css-modules)
    return [
        {
            test: /\.css$/,
            use: styleLoaders(options, {}),
            // Don't consider CSS imports dead code even if the
            // containing package claims to have no side effects.
            // Remove this when webpack adds a warning or an error for this.
            // See https://github.com/webpack/webpack/issues/6571
            sideEffects: true
        },
        {
            test: /\.(scss|sass)$/,
            use: styleLoaders(options, {preprocessor: "sass-loader"}),
            // Don't consider CSS imports dead code even if the
            // containing package claims to have no side effects.
            // Remove this when webpack adds a warning or an error for this.
            // See https://github.com/webpack/webpack/issues/6571
            sideEffects: true
        }
    ];
}

interface StyleLoadersOptions {
    preprocessor?: string | undefined;
}

function styleLoaders(
    options: ResolvedOptions,
    styleLoadersOptions: StyleLoadersOptions
): RuleSetUse {
    const sourceMap = options.mode === "development" || options.sourceMap;

    return [
        ...(options.target === "script" || options.mode === "development"
            ? [
                  // Turns CSS into JS modules that inject <style> tags.
                  // In development mode, enables hot editing of CSS.
                  require.resolve("style-loader")
              ]
            : [
                  // Extracts CSS to an external file.
                  MiniCssExtractPlugin.loader
              ]),
        {
            loader: require.resolve("css-loader"),
            options: {
                importLoaders: styleLoadersOptions.preprocessor === undefined ? 1 : 3
            }
        },
        {
            // Adds vendor prefixing based on your specified browser support in
            // package.json
            loader: require.resolve("postcss-loader"),
            options: {
                // Necessary for external CSS imports to work
                // https://github.com/facebook/create-react-app/issues/2677
                ident: "postcss",
                plugins: () => [
                    postcssPresetEnv({
                        autoprefixer: {
                            flexbox: "no-2009"
                        },
                        stage: 3
                    }),
                    // Adds PostCSS Normalize as the reset css with default options,
                    // so that it honors browserslist config in package.json
                    // which in turn lets users customize the target behavior as per their needs.
                    postcssNormalize()
                ],
                sourceMap
            }
        },
        ...(styleLoadersOptions.preprocessor === undefined
            ? []
            : [
                  {
                      loader: require.resolve("resolve-url-loader"),
                      options: {
                          sourceMap,
                          root: options.paths.src
                      }
                  },
                  {
                      loader: require.resolve(styleLoadersOptions.preprocessor),
                      options: {sourceMap: true}
                  }
              ])
    ];
}
