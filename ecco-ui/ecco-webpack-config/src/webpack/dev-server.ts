import {Configuration} from "webpack";
import path from "node:path";
import {hasProperty} from "unknown";
import {ResolvedSinglePageAppOptions} from "../options/single-page-app";

export function devServer(options: ResolvedSinglePageAppOptions): Configuration["devServer"] {
    const publicRootPath = publicUrlToPublicRootPath(options.publicUrl);

    return {
        static: {
            directory: options.paths.public,
            publicPath: publicRootPath.replace(/\/+$/, "")
        },
        hot: true,
        port: options.devServer.port,
        historyApiFallback: {
            // Paths with dots should still use the history fallback.
            // See https://github.com/facebook/create-react-app/issues/387.
            disableDotRule: true,
            index: publicRootPath
        },
        setupMiddlewares: middlewares => [
            ...middlewares,
            redirectPathMiddleware(publicRootPath.slice(0, -1))
        ]
    };
}

function publicUrlToPublicRootPath(publicUrl: string): string {
    try {
        return new URL(publicUrl).pathname;
    } catch (e: unknown) {
        if (hasProperty(e, "code") && e.code === "ERR_INVALID_URL") {
            return publicUrl;
        } else {
            throw e;
        }
    }
}

/** If the request path is not a subpath of the root path, resolve the
 * request path relative to the root path and redirect to the resulting
 * path. */
function redirectPathMiddleware(
    publicRootPath: string
): (req: any, res: any, next: (err?: any) => void) => void {
    if (publicRootPath === "") {
        return (req, res, next) => next();
    } else {
        return (req, res, next) => {
            if (req.url === publicRootPath || req.url.startsWith(`${publicRootPath}/`)) {
                next();
            } else {
                res.redirect(path.join(publicRootPath, req.url));
            }
        };
    }
}
