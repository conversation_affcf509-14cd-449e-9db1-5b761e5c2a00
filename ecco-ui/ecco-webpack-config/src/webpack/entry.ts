import {ResolvedOptions} from "../options/options";
import {Configuration} from "webpack";
import {mapObject} from "@softwareventures/object";

export function entry(options: ResolvedOptions): NonNullable<Configuration["entry"]> {
    if (options.target === "single-page-app") {
        return `${options.paths.src}/index`;
    } else {
        return mapObject(
            options.entry,
            (key, value) => [key, `${options.paths.src}/${value}`] as const
        );
    }
}
