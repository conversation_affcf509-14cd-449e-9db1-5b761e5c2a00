import {parse, posix, relative, resolve} from "node:path";
import {ResolvedOptions} from "../options/options";
import {Configuration} from "webpack";
import {hasProperty} from "unknown";

export function devtool(options: ResolvedOptions): NonNullable<Configuration["devtool"]> {
    return options.mode === "production"
        ? options.sourceMap
            ? "source-map"
            : false
        : "cheap-module-source-map";
}

export function devtoolModuleFilenameTemplate(
    options: ResolvedOptions
): NonNullable<NonNullable<Configuration["output"]>["devtoolModuleFilenameTemplate"]> {
    return (info: unknown) => {
        if (
            hasProperty(info, "absoluteResourcePath") &&
            typeof info.absoluteResourcePath === "string"
        ) {
            return nativePathToUrlPath(
                options.mode === "production"
                    ? relative(options.paths.src, info.absoluteResourcePath)
                    : resolve(info.absoluteResourcePath)
            );
        } else {
            throw new Error("Invalid module info");
        }
    };
}

function nativePathToUrlPath(path: string): string {
    return posix.format(parse(path));
}
