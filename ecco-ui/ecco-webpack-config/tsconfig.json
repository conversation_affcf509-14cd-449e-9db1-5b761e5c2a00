{
  // DOM types are required for WebAssembly types required by esbuild
  "extends": "@softwareventures/tsconfig/dom",
  "compilerOptions": {
    "types": ["node", "webpack-dev-server"],
    "rootDir": "src",
    "outDir": "dist",
    "incremental": true,
    "tsBuildInfoFile": "dist/.tsbuildinfo",

    // The following compiler options are required for compatibility with
    // legacy code. Remove them as soon as possible.
    "skipLibCheck": true
  },
  "include": ["src"]
}
