#!/usr/bin/env node

// Wrapper for jest.
//
// * Ensures we get the correct version of jest by resolving from the context
//   of ecco-webpack-config.
// * Sets up BABEL_ENV and NODE_ENV environment variables for tests.
// * Compiles ecco-webpack-config on demand if required.

"use strict";

const {fork} = require("child_process");
const {argv, execArgv, exit} = require("process");

// Compile ecco-webpack-config if required
const compiler = fork(require.resolve("typescript/bin/tsc"), [], {
    cwd: __dirname
});

compiler.on("exit", code => {
    if (code === 0) {
        // Set up environment variables
        process.env.BABEL_ENV = "test";
        process.env.NODE_ENV = "test";

        // Run jest
        // --experimental-vm-modules is required to support ES Modules in jest
        // see: https://jestjs.io/docs/ecmascript-modules
        // see: https://nodejs.org/api/vm.html#vm_class_vm_module
        const jest = fork(require.resolve("jest/bin/jest"), argv.slice(2), {
            execArgv: [...execArgv, "--experimental-vm-modules"]
        });

        jest.on("exit", code => {
            exit(code ?? 1);
        });
    } else {
        exit(code ?? 1);
    }
});
