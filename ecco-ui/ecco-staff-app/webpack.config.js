import {config} from "ecco-webpack-config";

export default config({
    target: "single-page-app",
    publicUrl: "_CONTEXT_PATH_/r/app",
    imageInlineSizeLimit: 81920,
    externals: {
        "application-properties": "applicationProperties", // i.e. window.applicationProperties
        "bloodhound": "notAvailOffline",
        // "jquery": "$",
        "lazy": "Lazy",
        "URI": "notAvailOffline",
        "jquery-bundle": "$", // TODO: This will break for Tabs and old Date/Time pickers. Migrate these
        "jquery-ui-bundle": "$", // TODO: This won't support things like drag/drop and other jquery-ui features
        "jquery-file-upload": "notAvailOffline", // we shouldn't try doing anything with files if offline
        "jquery-jqplot-bundle": "notAvailOffline",
        "raphael": "notAvailOffline" // we shouldn't try doing anything with files if offline
    }
});
