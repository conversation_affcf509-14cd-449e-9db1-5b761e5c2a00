<!DOCTYPE html>
<!--suppress HtmlUnknownTarget, JSUnresolvedLibraryURL -->
<html lang="en" class="v3">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="<%= PUBLIC_URL %>favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Auditable management for care and support"
    />
    <link rel="x-ecco-public-url" href="<%= PUBLIC_URL %>" />
    <link rel="apple-touch-icon" href="<%= PUBLIC_URL %>/ecco-e-192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
      NOTE: See service-worker.ts for caching config
    -->
    <link rel="manifest" href="<%= PUBLIC_URL %>manifest.json">
    <link rel="shortcut icon" href="<%= PUBLIC_URL %>favicon.ico">
    <link href="<%= PUBLIC_URL %>css/font-awesome.min.css" rel="stylesheet">
    <link href="<%= PUBLIC_URL %>css/bootstrap.min.css" rel="stylesheet">
    <link href="<%= PUBLIC_URL %>css/propeller.css" rel="stylesheet">
    <link href="<%= PUBLIC_URL %>css/jqueryui.css" rel="stylesheet">
    <link href="<%= PUBLIC_URL %>common.css" rel="stylesheet">
    <link href="<%= PUBLIC_URL %>evidence.css" rel="stylesheet">
    <!-- TODO: Remove both of these when we've migrated tabs and pickers and need for jquery for bootstrap dropdown etc -->
    <script src="<%= PUBLIC_URL %>jquery-3.6.0.min.js"></script>
    <script src="<%= PUBLIC_URL %>jquery-ui-1.10.3.custom.min.js"></script>
    <script src="<%= PUBLIC_URL %>jquery-datepicker.js"></script>
    <script src="<%= PUBLIC_URL %>lazy.min.js"></script>
<!--    <script src="https://cdn.jsdelivr.net/npm/urijs@1.19.2/src/URI.min.js"></script>-->
    <script src="<%= PUBLIC_URL %>bootstrap.min.js"></script>
    <!--
      Notice the use of <%= PUBLIC_URL %> in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "<%= PUBLIC_URL %>favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>ecco - Care and Support</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <script>
      window.notAvailOffline = {
        default: () => {
          console.error("not available offline")
        }
      }
    </script>
    <div id="appbar"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
