{"name": "application-properties", "version": "0.0.0", "description": "TypeScript definitions for ecco application-properties API call", "license": "UNLICENSED", "exports": {".": {"types": "./index.d.ts"}}, "sideEffects": false, "scripts": {"clean": "echo Nothing to do", "emit": "echo Nothing to do", "lint": "tsc", "build": "echo Nothing to do", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Nothing to do", "test-sequential": "echo Nothing to do"}, "devDependencies": {"@softwareventures/tsconfig": "9.0.0-alpha.1", "typescript": "5.8.3"}}