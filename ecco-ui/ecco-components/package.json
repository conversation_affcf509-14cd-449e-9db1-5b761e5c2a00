{"private": true, "name": "ecco-components", "version": "0.0.0", "license": "UNLICENSED", "exports": {".": {"types": "./_typings/index.d.ts", "default": "./index.js"}, "./cmd-queue/testUtils": {"types": "./_typings/cmd-queue/testUtils.d.ts", "default": "./cmd-queue/testUtils.js"}, "./images/body-front.png": "./images/body-front.png", "./images/body-back.png": "./images/body-back.png", "./test-support/mock-utils": {"types": "./_typings/test-support/mock-utils.d.ts", "default": "./test-support/mock-utils.js"}, "./test-support/TestServicesContextProvider": {"types": "./_typings/test-support/TestServicesContextProvider.d.ts", "default": "./test-support/TestServicesContextProvider.js"}}, "sideEffects": ["./index.js", "**/*.css"], "scripts": {"analyse": "webpack --json | webpack-bundle-size-analyzer", "clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts,.tsx .", "build": "eslint --ext .ts,.tsx . && webpack", "lint": "eslint --ext .ts,.tsx .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "jest", "test-sequential": "yarn cy:test", "cy:test": "cypress run --component", "cy:dev": "cypress open --component"}, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "0.0.0", "@eccosolutions/ecco-mui-controls": "0.0.0", "@reach/auto-id": "^0.12.1", "@softwareventures/array": "^6.1.0", "@softwareventures/nullable": "^3.2.0", "qr-scanner": "^1.4.2", "application-properties": "0.0.0", "bowser": "^1.9.4", "ecco-commands": "0.0.0", "ecco-dto": "0.0.0", "ecco-components-core": "0.0.0", "ecco-forms": "0.0.0", "ecco-offline-data": "0.0.0", "ecco-rota": "0.0.0", "ecco-spa-global": "0.0.0", "file-saver": "^2.0.5", "immutability-helper": "3.0.0", "moment": "2.24.0", "prop-types": "^15.7.2", "react": "16.13.1", "react-async": "10.0.1", "react-bootstrap": "^0.33.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scroll": "^1.8.4", "react-select": "^3.0.7", "react-signature-canvas": "^1.0.3", "react-to-print": "^2.11.0", "signature_pad": "2.3.2", "sheetjs-style": "^0.15.8", "stream-browserify": "^3.0.0"}, "devDependencies": {"@bahmutov/cypress-esbuild-preprocessor": "^2.2.0", "@testing-library/cypress": "^8.0.1", "@types/enzyme": "^3.1.14", "@types/enzyme-adapter-react-16": "^1.0.5", "@types/file-saver": "^2.0.5", "@types/jest": "^29.5.2", "@types/react": "^16.9.56", "@types/react-bootstrap": "^0.32.17", "@types/react-dom": "^16.9.24", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "@types/react-scroll": "^1.8.3", "@types/react-select": "^3.0.5", "@types/react-signature-canvas": "^1.0.2", "@types/signature_pad": "^2.3.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@welldone-software/why-did-you-render": "^7.0.1", "css-loader": "^6.11.0", "cypress": "^11.2.0", "enzyme": "^3.6.0", "enzyme-adapter-react-16": "^1.5.0", "enzyme-to-json": "^3.3.4", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "jest": "^29.5.0", "jest-cli": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "rxjs": "^5.1.1", "style-loader": "^2.0.0", "svg-url-loader": "^7.1.1", "terser-webpack-plugin": "^4.2.3", "ts-jest": "^29.1.0", "typescript": "5.8.3", "url-loader": "4.1.1", "webpack": "^5.101.0", "webpack-bundle-size-analyzer": "^3.1.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.2"}}