import * as React from "react"
import {FC, MutableRefObject, useCallback, useRef} from "react"
import SignatureCanvas from "react-signature-canvas";
import {showReactInModal} from "ecco-components-core";
import {EccoModal, Footer} from "ecco-components-core";

export type {SignatureCanvas}

interface Props {
    // callback required to preserve the data as its changed - before the modal closes
    callback: (imageDataUrl: string | null) => void;
}

interface State {
    withSignature: boolean;
    imageDataUrl: string | null;
}

export const SmartSignatureCanvas: FC<{
    widthRatio: number
    onPenUp: (canvas: HTMLCanvasElement) => void
    canvasProps?: React.CanvasHTMLAttributes<HTMLCanvasElement> | undefined
}> = ({widthRatio, onPenUp, canvasProps}) => {
    const sigCanvas = useRef<SignatureCanvas | null>(null)

    const measuredRef = useCallback((node: SignatureCanvas) => {
        const resizeCanvas = (signaturePad: SignaturePad, canvas: HTMLCanvasElement) => {
            canvas.width = canvas.parentElement!.clientWidth // width of the .canvasWrapper
            canvas.height = canvas.parentElement!.clientWidth / widthRatio
            signaturePad.clear()
        }

        if (node !== null) {
            sigCanvas.current = node
            resizeCanvas(node.getSignaturePad(), node.getCanvas())
        }
    }, [widthRatio])

    return (
        <div className="canvasWrapper signature-capture">
            <SignatureCanvas
                canvasProps={canvasProps}
                ref={measuredRef}
                onEnd={() => sigCanvas.current && !sigCanvas.current?.isEmpty() && onPenUp(sigCanvas.current.getTrimmedCanvas())}
            />
        </div>
    )
}

/**
 * @param title obvious
 * @param callback called when dialog is submitted.  Should be called with null if no signature was recorded
 */
export function showSignatureCaptureModal(title: string, callback: (imageDataUrl: string | null) => void) {
    const sig: MutableRefObject<string | null> = {current: null}
    const content =
        <SmartSignatureCanvas
            widthRatio={0.8}
            onPenUp={canvas => sig.current = canvas.toDataURL()}
            canvasProps={{
                // width:400,
                // height: 300,
                className: 'signature'}}
        />

    showReactInModal(title, content,
        {
            onAction: () => callback(sig.current),
            maxWidth: "sm"
        })
}

/** A component to capture signature as a PNG Data URL using React Signature Canvas */
export class SignatureCapture extends React.Component<Props, State> {
    private canvas: SignatureCanvas | null = null;

    constructor(props: Props) {
        super(props);
        this.state = {
            withSignature: false,
            imageDataUrl: null
        };
    }

    private getImageDataUrlFromCanvas(): string | null {
        return this.state.withSignature && this.canvas != null && !this.canvas.isEmpty()
            ? this.canvas.getTrimmedCanvas().toDataURL()
            : null;
    }

    private openSignBox() {
        this.setState({
            withSignature: true
        });
    }
    private closeSignBox() {
        const sigData = this.getImageDataUrlFromCanvas();
        this.setState({
            withSignature: false,
            imageDataUrl: sigData
        });
        this.props.callback(sigData);
    }

    override render() {
        const footer = (
            <Footer onCancel={() => this.closeSignBox()} saveEnabled={false} action="close" />
        );

        const SignatureIfSelected = !this.state.withSignature ? null : (
            <EccoModal
                title="sign"
                footer={footer}
                show={true}
                onEscapeKeyDown={() => this.closeSignBox()}
            >
                <SignatureCanvas
                    ref={ref => {
                        this.canvas = ref;
                        this.canvas && this.canvas.clear();
                    }} // allows isEmpty to work
                    canvasProps={{width: 800, height: 500, className: "signature"}}
                />
            </EccoModal>
        );

        return (
            <>
                <div>
                    <a className="btn btn-link" onClick={() => this.openSignBox()}>
                        {this.state.imageDataUrl ? "redo signature" : "with signature"}
                    </a>
                </div>
                {SignatureIfSelected}
            </>
        );
    }
}
