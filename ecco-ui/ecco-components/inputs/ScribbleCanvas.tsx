import {FC, MutableRefObject, useEffect, useRef, useState} from "react"
// @ts-ignore
import SignaturePad from "signature_pad";
import * as React from "react";

export interface Props {
    readonly padRef?: MutableRefObject<SignaturePad> | undefined;
    readonly width: number;
    readonly height: number;
    readonly content?: string | undefined;
    readonly setContent?: ((content: string) => void) | undefined;
}

export const ScribbleCanvas: FC<Props> = ({padRef, width, height, content, setContent}) => {
    const canvas = useRef<HTMLCanvasElement | null>(null);
    const [signaturePad, setSignaturePad] = useState<SignaturePad | null>(null);

    useEffect(() => {
        if (canvas.current != null) {
            let pad = new SignaturePad(canvas.current);
            setSignaturePad(pad);
            pad.fromDataURL(content, {width, height});
            if (padRef) padRef.current = pad;
        }
        return () => {
            signaturePad?.off();
            if (setContent) {
                setContent(signaturePad?.toDataURL() ?? "");
            }
            setSignaturePad(null);
            if (padRef) padRef.current = null;
        };
    }, [canvas.current]);

    return <canvas ref={canvas} className="ScribbleCanvas" width={width} height={height} />;
};