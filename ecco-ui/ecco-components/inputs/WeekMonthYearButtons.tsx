import {createButtonGroup} from "ecco-components-core";
import {Component} from "react"
import {EccoDate} from "@eccosolutions/ecco-common"
import "../styles/components.css"

const today = EccoDate.todayLocalTime()
const todayIso = today.formatIso8601()
const yesterdayIso = today.subtractDays(1).formatIso8601()
const startThisYear = today.withMonth(1).withDate(1)
const startThisYearIso = startThisYear.formatIso8601()
const endThisYearIso = today.withMonth(12).withDate(31).formatIso8601()
const startThisMonth = today.withDate(1)
const startThisMonthIso = startThisMonth.formatIso8601()
const endThisMonthIso = startThisMonth.addMonths(1).subtractDays(1).formatIso8601()
const startLastMonthIso = startThisMonth.subtractMonths(1).formatIso8601()
const endLastMonthIso = startThisMonth.subtractDays(1).formatIso8601()
const dayOfWeekRelToSun = today.getDayOfWeek()
const dayOfWeekRelToMon = dayOfWeekRelToSun == 0 ? 6 : dayOfWeekRelToSun - 1
const startThisWeek = today.subtractDays(dayOfWeekRelToMon)
const startThisWeekIso = startThisWeek.formatIso8601()
const endThisWeekIso = startThisWeek.addDays(6).formatIso8601()
const startLastWeekIso = startThisWeek.subtractDays(7).formatIso8601()
const endLastWeekIso = startThisWeek.subtractDays(1).formatIso8601()

interface Props {
    rangeEnabled: boolean;
    start: string | null;
    end: string | null;
    setStart: (isoDate: string) => void;
    setEnd: (isoDate: string) => void;
}

type TIME_SINGLE = "today" | "yesterday"
type TIME_RANGE = "today" | "yesterday" | "this week" | "last week" | "this month" | "last month" | "this year"

export class WeekMonthYearButtons extends Component<Props> {
    private onClick = (type: TIME_SINGLE | TIME_RANGE) => {
        const {setStart, setEnd, rangeEnabled} = this.props;

        switch (type) {
            case "today":
                rangeEnabled && setStart(todayIso);
                setEnd(todayIso);
                break;
            case "yesterday":
                rangeEnabled && setStart(yesterdayIso);
                setEnd(yesterdayIso);
                break;
            case "this week":
                setStart(startThisWeekIso);
                setEnd(endThisWeekIso);
                break;
            case "last week":
                setStart(startLastWeekIso);
                setEnd(endLastWeekIso);
                break;
            case "this month":
                setStart(startThisMonthIso);
                setEnd(endThisMonthIso);
                break;
            case "last month":
                setStart(startLastMonthIso);
                setEnd(endLastMonthIso);
                break;
            case "this year":
                setStart(startThisYearIso);
                setEnd(endThisYearIso);
                break;
        }
    };

    override render() {
        const start = this.props.start;
        const end = this.props.end;

        const buttonsSingle: Record<TIME_SINGLE, boolean> = {
            today: end == todayIso,
            yesterday: end == yesterdayIso
        };

        const buttonsRange: Record<TIME_RANGE, boolean> = {
            "today": start == todayIso && end == todayIso,
            "yesterday": start == yesterdayIso && end == yesterdayIso,
            "this week": start == startThisWeekIso && end == endThisWeekIso,
            "last week": start == startLastWeekIso && end == endLastWeekIso,
            "this month": start == startThisMonthIso && end == endThisMonthIso,
            "last month": start == startLastMonthIso && end == endLastMonthIso,
            "this year": start == startThisYearIso && end == endThisYearIso
        };

        return this.props.rangeEnabled
            ? createButtonGroup("days", null, buttonsRange, btn => this.onClick(btn))
            : createButtonGroup("days", null, buttonsSingle, btn => this.onClick(btn));
    }
}
