import * as React from "react";
import {FC, useState} from "react";
import {EccoDate} from "@eccosolutions/ecco-common";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import * as ReactDOM from "react-dom"


interface Props {
    title: string
    date: EccoDate | null
    onChange: (value: EccoDate | null) => void

}

export function iconDatePickerAsElement(props: Props): Element {
    const element = document.createElement("span");
    ReactDOM.render(<IconDatePicker {...props}/>, element);
    return element;
}

/**
 * Inline
 */
export const IconDatePicker: FC<Props> = props => {

    const [show, setShow] = useState(false);
    const [date, setDate] = useState(props.date);

    return <>
        <span
            title={props.title} onClick={() => setShow(true)}
            className="clickable-image datepicker fa fa-calendar"
            style={{padding: "0 8px 14px", verticalAlign: "bottom"}}/>
        <span style={{display: "inline-block"}}>
            {show && <DatePickerEccoDate
                noKeyboard
                label={props.title}
                size="small"
                variant="outlined"
                value={date}
                onChange={date => {
                    setDate(date);
                    props.onChange(date);
                }
            }/>}
        </span>
    </>
}
