import {PersonUserSummary, SessionData} from "ecco-dto";

export const mockWorkerDto: PersonUserSummary = {
    id: 1,
    name: "dave r",
    disabled: false,
    individualId: 1,
    firstName: "dave",
    lastName: "worker",
    userId: 1,
    username: "davestaff",
    displayName: "dave r",
    calendarIdUserReferenceUri: "entity://HibUser/1", // cosmo representation - see CosmoHelper.syncAttendeesWithCalendars
    calendarId: "-calId-uuid-"
};

export function mockWorkersDtoPromise(sessionData: SessionData, excludeMe?: boolean) {
    return Promise.resolve([mockWorkerDto]);
}
