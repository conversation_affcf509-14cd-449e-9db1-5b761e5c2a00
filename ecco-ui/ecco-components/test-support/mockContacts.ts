import {Agency, Individual} from "ecco-dto";

const professional1: Individual = {
    organisationId: 55,
    calendarId: undefined,
    contactId: 58,
    jobTitle: undefined,
    title: "mr",
    firstName: "bob",
    lastName: "marley",
    preferredContactMethod: undefined,
    code: undefined,
    avatarId: undefined,
    discriminator: "individual",
    address: undefined,
    addressedLocationId: undefined,
    phoneNumber: undefined,
    mobileNumber: undefined,
    email: undefined,
    isUser: false,
    userLastLoggedIn: undefined,
    archived: null
};

const agency1: Agency = {
    contactId: 55,
    outOfArea: false,
    agencyCategoryId: undefined,
    companyName: "my company with a really long name",
    address: undefined,
    email: "<EMAIL>",
    phoneNumber: "01255 283747 / 07826 372388",
    code: undefined,
    avatarId: undefined,
    discriminator: "agency", // | "client" | "individual";
    addressedLocationId: undefined,
    mobileNumber: undefined,
    isUser: false,
    archived: null
    //userLastLoggedIn: null
};

export const mockProfessionalDtos: Individual[] = [professional1];
export const mockAgencyDtos: Agency[] = [agency1];
