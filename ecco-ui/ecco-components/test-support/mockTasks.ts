import {TaskDto} from "ecco-dto";

const mockTaskDto: TaskDto = {
    links: [],
    assignedTo: "bob uncle",
    isAvailable: true,
    isCompleted: false,
    taskDefinitionHandle: "linear-workflow:-:1", // service type 1
    taskName: "support plan",
    taskHandle: "999-27", // support plan
    dueDate: null,
    endTime: null
};

export function mockTaskPromise(handle: string) {
    return Promise.resolve(mockTaskDto);
}
