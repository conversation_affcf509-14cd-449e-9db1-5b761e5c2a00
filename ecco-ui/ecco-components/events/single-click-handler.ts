import {Mouse<PERSON>ventHandler} from "react";

/** Adapts a MouseEventHandler so that it will ignore all but the first click
 * if the user clicks multiple times in quick succession.
 *
 * Events where no click is involved (such as mousemove) are passed through
 * unmodified. */
export function singleClickHandler(f: MouseEventHandler): MouseEventHandler {
    return e => {
        // MouseEvent.detail = successive click count
        if (e.nativeEvent.detail <= 1) {
            f(e);
        }
    }
}