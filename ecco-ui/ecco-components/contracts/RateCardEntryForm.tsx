import {CommandQueue, RateCardEntryCommand} from "ecco-commands";
import {RateCardDto, RateCardEntryDto} from "ecco-dto";
import * as React from "react";
import {FC} from "react";
import {CommandForm, CommandSubform, ModalCommandForm} from "../cmd-queue/CommandForm";
import {useServicesContext} from "../ServicesContext";
import {RateCardEntry} from "./RateCardEntry";
import {Nullable, SelectListOption} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EccoAPI} from "../EccoAPI";

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    rateCard: RateCardDto;
    rateCardEntry?: RateCardEntryDto | undefined; // absent on new
    chargeTypesFixedTemporal: SelectListOption[];
    setShow: (show: boolean) => void;
}
interface Props extends ModalProps {
    readOnly: boolean;
}

interface State {
    rateCardEntry: RateCardEntryDto;
}

export class RateCardEntryForm extends CommandSubform<Props & LocalProps, State> {
    constructor(props: Props & LocalProps) {
        super(props);
        this.state = {
            rateCardEntry: props.rateCardEntry || ({} as RateCardEntryDto)
        };
    }

    getErrors(): string[] {
        const errors: string[] = [];
        //const rateCardEntry = this.state.rateCardEntry;

        return errors;
    }

    emitChangesTo(cmdQ: CommandQueue) {
        const rateCardEntryIn = this.props.rateCardEntry || ({} as RateCardEntryDto);
        const rateCardEntry = this.state.rateCardEntry;

        let cmd: RateCardEntryCommand = new RateCardEntryCommand(
            this.props.rateCardEntry ? "update" : "add",
            Uuid.randomV4(),
            undefined,
            this.props.rateCard.rateCardId,
            this.props.rateCardEntry?.rateCardEntryId
        );
        cmd.changeChargeCategoryId(
            rateCardEntryIn.matchingChargeCategoryId,
            rateCardEntry.matchingChargeCategoryId
        )
            .changeChargeTypeFixedTemporal(
                rateCardEntryIn.chargeTypeFixedTemporal,
                rateCardEntry.chargeTypeFixedTemporal
            )
            .changeUnitMeasurement(
                rateCardEntryIn.unitMeasurementId,
                rateCardEntry.unitMeasurementId
            )
            .changeUnitCharge(rateCardEntryIn.unitCharge, rateCardEntry.unitCharge)
            .changeUnitsToRepeatFor(
                rateCardEntryIn.unitsToRepeatFor,
                rateCardEntry.unitsToRepeatFor
            )
            .changeFixedCharge(rateCardEntryIn.fixedCharge, rateCardEntry.fixedCharge)
            .changeDefaultEntry(rateCardEntryIn.defaultEntry, rateCardEntry.defaultEntry)
            .changeDisabledEntry(rateCardEntryIn.disabled, rateCardEntry.disabled)
            .changeUnits(rateCardEntryIn.units, rateCardEntry.units);

        if (rateCardEntryIn.chargeTypeFixedTemporal != rateCardEntry.chargeTypeFixedTemporal) {
            if (rateCardEntry.chargeTypeFixedTemporal == "TEMPORAL") {
                cmd.changeFixedCharge(rateCardEntryIn.fixedCharge, null);
            }
            if (rateCardEntry.chargeTypeFixedTemporal == "FIXED") {
                cmd.changeUnitCharge(rateCardEntryIn.unitCharge, null);
                cmd.changeUnits(rateCardEntryIn.units, null);
            }
        }

        if (cmd.hasChanges()) {
            cmdQ.addCommand(cmd);
        }
    }

    private updateRateCardEntry = (rateCardEntry: Partial<Nullable<RateCardEntryDto>>) => {
        this.setState({rateCardEntry: rateCardEntry as RateCardEntryDto});
    };

    override render() {
        return (
            <RateCardEntry
                rateCard={this.props.rateCard}
                rateCardEntry={this.state.rateCardEntry}
                chargeTypesFixedTemporal={this.props.chargeTypesFixedTemporal}
                onChangeRateCardEntry={this.updateRateCardEntry}
            />
        );
    }
}

export const RateCardEntryFormModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title={"charge"}
            action={props.rateCardEntry ? "update" : "save"}
            maxWidth="sm"
        >
            {form => (
                <RateCardEntryForm
                    {...props}
                    readOnly={!eccoAPI.sessionData.hasRoleReferralEdit()}
                    services={eccoAPI}
                    commandForm={form}
                />
            )}
        </ModalCommandForm>
    );
};
