import {AdminMode} from "@eccosolutions/ecco-common";

export function adminModeEnabled() {
    return sessionStorage["admin-mode"] == "y";
}

const button = document.querySelector(".menu-action-edit-mode i");
let enabled = adminModeEnabled();

function updateIcon() {
    if (enabled) {
        button?.classList.add("fa-spin");
    } else {
        button?.classList.remove("fa-spin");
    }
}

const handler = () => {
    enabled = !enabled;
    sessionStorage["admin-mode"] = enabled ? "y" : "n";
    AdminMode.bus.fire(new AdminMode(enabled));
    updateIcon();
};

// HACK because if JQuery e.g. via Bootstrap.js is loaded, it will eat the clicks on the button
// @ts-ignore
if (self.jQuery) {
    // @ts-ignore
    self.jQuery(".menu-action-edit-mode").click(handler);
} else {
    button?.addEventListener("click", handler);
}

updateIcon();
