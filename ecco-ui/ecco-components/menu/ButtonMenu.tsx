import {<PERSON>ton, ButtonGroup, Menu, MenuItem} from "@eccosolutions/ecco-mui";
import {useId} from "@reach/auto-id";
import * as React from "react";
import {FC, ReactElement, useRef, useState} from "react";
import {ArrowDropDownIcon} from "@eccosolutions/ecco-mui-controls";
import {singleClickHandler} from "../events/single-click-handler";

interface Props {
    id?: string | undefined;
    options: (string | ReactElement)[];
    onSelection: (selectedIndex: number) => void;
    onClick: (selectedIndex: number) => void;
    selectedIndex: number | null;
}

/**
 * A button with a dropdown that allows a selection to be made and then acted on.  Can default
 * to a given value where there is a good default (e.g. only one perfect match option)
 */
export const ButtonMenu: FC<Props> = props => {
    const id = useId(props.id);
    const {children, options, onClick, onSelection, selectedIndex} = props;

    const [open, setOpen] = useState(false);
    const anchorRef = useRef<HTMLDivElement>(null);

    const menuId = `${id}-menu`;

    const handleItemClick = (index: number) => singleClickHandler(() => {
        setOpen(false);
        onSelection(index);
    });

    const handleButtonClick = singleClickHandler(e => {
        if (selectedIndex == null) {
            // if nothing selected then pop open
            handleToggle(e);
        } else {
            onClick(selectedIndex);
        }
    })

    const handleToggle = singleClickHandler(() => {
        setOpen((prevOpen) => !prevOpen);
    });

    const handleClose = singleClickHandler(() => {
        setOpen(false);
    });

    return <>
        <ButtonGroup variant="contained" color="primary" ref={anchorRef}>
            <Button onClick={handleButtonClick}>{children}</Button>
            <Button
                color="primary"
                size="small"
                aria-controls={menuId}
                aria-expanded={open ? 'true' : undefined}
                aria-label="choose from list"
                aria-haspopup="menu"
                onClick={handleToggle}
            >
                <ArrowDropDownIcon />
            </Button>
        </ButtonGroup>
        <Menu id={menuId} keepMounted open={open} anchorEl={anchorRef.current} onClose={handleClose}>
            {options.length == 0 && <MenuItem key={-1} disabled={true}>no options</MenuItem>}
            {options.map((option, index) => (
                <MenuItem
                    key={index}
                    selected={index === selectedIndex}
                    onClick={handleItemClick(index)}
                >
                    {option}
                </MenuItem>
            ))}
        </Menu>
    </>;
};