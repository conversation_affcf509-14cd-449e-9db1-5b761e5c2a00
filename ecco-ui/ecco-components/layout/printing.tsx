import * as React from "react";
import {Component, ReactElement, ReactNode, useCallback, useEffect, useRef, useState} from "react";
import {useReactToPrint} from "react-to-print";
import Print from "@material-ui/icons/Print";

export {default as Print} from "@material-ui/icons/Print";

/**
 * Show a Printer icon which when clicks sends only the child content of this Component to a printer
 */
export const PrintIcon = ({printView}: {printView?: ReactNode | undefined}) => {
    const contentRef = useRef<HTMLDivElement | null>(null);

    const content = React.useCallback(() => {
        return contentRef.current;
    }, [contentRef.current]);

    useEffect(() => {}, [contentRef.current]);

    const handlePrint = useReactToPrint({
        content: content
    });

    // NOTE: We may end up duplicating data that is also shown, so be careful and be aware of possible duplicate
    // elements.  We could use onBeforeGetContent to change the content before showing it (e.g. to re-render cards
    // as read only.
    return (
        <>
            <div ref={contentRef} style={{display: "none"}}>
                {printView}
            </div>
            <Print onClick={handlePrint} />
        </>
    );
};

// noinspection JSCommentMatchesSignature
/**
 * Allows content to be supplied for printing that requires async operations.
 * getContent() is called when the button is clicked, and it expects a Promise for a component (e.g. one lazy loaded).
 * This component itself may have async operations such as fetch(), but needs to be mounted so that they trigger.
 * If there are async operations, then false can initially be supplied to <code>ready</code>, which is updated to true
 * when the component has finished loading.
 * @param ready Optional boolean that is true only when the component is fully rendered.  If omitted, it's assumed
 * that the component is fully rendered in the state returned from getContent() (i.e. it doesn't update further).
 */
export const PrintIconLazy = ({getContent, button, title}: {
    getContent: (onRendered: () => void) => Promise<ReactNode>
    button: ReactElement
    title: string
}) => {
    const [rendered, setRendered] = useState(false);
    const contentRef = useRef<HTMLDivElement | null>(null);

    const onBeforeGetContentResolve = useRef<(() => void) | null>(null);


    const [lazyContent, setLazyContent] = useState<ReactNode | null>();

    const content = useCallback(() => {
        return contentRef.current;
    }, [contentRef.current]);

    useEffect( () => {
                   rendered && onBeforeGetContentResolve.current && onBeforeGetContentResolve.current();
           },
           [lazyContent, rendered]);

    // Promise for when content has loaded. must call onBeforeGetContentResolve.current() when done
    const onBeforeGetContent = () => {
        const promise = new Promise<void>(resolve => {
            onBeforeGetContentResolve.current = resolve;
        })
        setRendered(false);
        return getContent(() => setRendered(true))
            .then(setLazyContent)
            .then(() => promise);
    };

    const handlePrint = useReactToPrint({content, onBeforeGetContent, documentTitle: title});

    return <>
        <div onClick={handlePrint}>
            {button}
        </div>
        <div style={{display: "none"}}>
            <div ref={contentRef}>
                {lazyContent}
            </div>
        </div>
    </>;
};

/**
 * use as e.g.:
 *   const {PrintIcon, printableContentRef} = usePrintIcon();
 *   return <>
 *       <MyMenuBar><PrintIcon/></MyMenuBar>
 *       <MyContent ref={printableContentRef} />
 *    </>
 */
export function usePrintIcon<T extends Element | Component>() {
    const printableContentRef = useRef<T | null>(null);
    const handlePrint = useReactToPrint({content: () => printableContentRef.current});

    return {
        PrintIcon: () => <Print onClick={handlePrint}/>,
        printableContentRef
    };
}
