import {applicationRootPath} from "application-properties";
import * as React from "react";
import {FC, useState} from "react";
import {MenuItem} from "@eccosolutions/ecco-mui";
import {IconMenu} from "@eccosolutions/ecco-mui-controls";

export function getGuidanceUrlCallback(formDefinitionUuid: string) {
    var href = new URL(
        `${applicationRootPath}nav/guidance/${formDefinitionUuid}/printable`,
        location.href
    ).href;
    return () => window.open(href, "_blank");
}

export const GuidanceMenu: FC<{uuid?: string | undefined}> = ({uuid}) => {
    const guidanceFormDefinitionUuid = uuid;
    if (!guidanceFormDefinitionUuid) {
        return null;
    }

    const guidanceCallback = guidanceFormDefinitionUuid
        ? getGuidanceUrlCallback(guidanceFormDefinitionUuid)
        : undefined;

    const [open, setOpen] = useState(false);

    return (
        <span>
            <IconMenu
                id="guidance-menu"
                iconClasses={`fa fa-question-circle`}
                color={undefined}
                open={open}
                onClose={() => setOpen(false)}
                onClick={() => setOpen(true)}
            >
                <MenuItem onClick={guidanceCallback}>
                    <i className="fa fa-question-circle" />
                    &nbsp;help
                </MenuItem>
            </IconMenu>
        </span>
    );
};
GuidanceMenu.displayName = "GuidanceMenu";
