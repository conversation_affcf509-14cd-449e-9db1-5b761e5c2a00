import {Box} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {ReactNode} from "react";

interface TabPanelProps {
    children?: ReactNode | undefined;
    index: any;
    value: any;
}

export function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`wrapped-tabpanel-${index}`}
            aria-labelledby={`wrapped-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box py={2}>
                    {children}
                </Box>
            )}
        </div>
    );
}
