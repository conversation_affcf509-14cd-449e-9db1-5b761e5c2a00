import {ResizeEvent} from "@eccosolutions/ecco-common";
import * as React from "react";
import {FC, MutableRefObject, useEffect, useRef, useState} from "react";

/**
 * Prevent the child component from scrolling left/right while leaving a placeholder behind that is the same height
 * to ensure that content below that does scroll is in the right place vertically.
 * Does this aspect of what banner-titlebar.ts did for JQuery impl.
 *
 * deps - pass these to useLayoutEffect to trigger reposition/size
 */
export const AnchorLeft: FC<{
    deps: any[];
    refCallback?: ((c: MutableRefObject<any>) => void) | undefined;
}> = ({children, deps, refCallback}) => {
    const fixedEl = useRef<HTMLDivElement>(null);
    const [height, setHeight] = useState<number | undefined>();
    const [left, setLeft] = useState(0);

    function onUpdate() {
        if (fixedEl.current) {
            setHeight(fixedEl.current.getBoundingClientRect().height);
            setLeft(
                fixedEl.current.parentElement!.getBoundingClientRect().left -
                    document.body.getBoundingClientRect().left
            );
        }
    }

    // ensure resize when a dependency changes
    // useLayoutEffect is called synchronously after all DOM mutations, but suggestion is useEffect is suitable
    // - although neither actually calls after all painting is finished - see DomElementContainer
    useEffect(() => {
        onUpdate();
    }, deps);

    useEffect(
        () => {
            // call back the ref
            refCallback && refCallback(fixedEl);

            window.addEventListener("scroll", onUpdate);
            window.addEventListener("resize", onUpdate);
            ResizeEvent.bus.addHandler(onUpdate);
            return () => {
                window.removeEventListener("scroll", onUpdate);
                window.removeEventListener("resize", onUpdate);
                ResizeEvent.bus.removeHandler(onUpdate);
            };
        },
        [] // Only want mount/unmount, not update.
    );
    return (
        <>
            <div
                ref={fixedEl}
                style={{
                    position: "fixed",
                    left,
                    zIndex: 11,
                    maxWidth: `calc(100% - ${left}px)`,
                    width: `calc(100% - ${left}px)`
                }}
            >
                {children}
            </div>
            <div className="spacer" style={{height: height!, zIndex: 10}} />
        </>
    );
};