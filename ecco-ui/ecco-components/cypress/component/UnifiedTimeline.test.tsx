import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {UnifiedSupportHistoryState} from "../../service-recipient/unifiedTimelineState";
import {UnifiedTimeline} from "../../service-recipient/UnifiedTimeline";
const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("UnifiedTimeline playground", () => {
    it("render", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <UnifiedTimeline serviceRecipientId={99} />
            </TestServicesContextProvider>
        );
        //cy.pause();
    });
});

describe("UnifiedTimeline tests", () => {
    it("previous visits", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <UnifiedTimeline serviceRecipientId={99} />
            </TestServicesContextProvider>
        );
        //cy.findByText("more").should("exist").click();
        //cy.findByText("Started: Sun, 20 Mar 2022, 09:45").should("exist");
    });
});

function mockSupportHistoryState(
    srId: number = Math.random()
): Partial<UnifiedSupportHistoryState> {
    return {
        id: Uuid.randomV4().toString()
    };
}
