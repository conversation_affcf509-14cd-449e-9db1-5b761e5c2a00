import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {CareVisitRoot} from "../../care/CareVisitRoot";
import {CareVisitSummaryCard} from "../../care/CareVisitSummaryCard";
import {mount} from "cypress/react";
import * as React from "react";
import {careVisitOverrides, mockTransformToCareVisit, mockVisitState} from "./testData";

describe("CareVisitSummary playground", () => {
    it("render", () => {
        mount(
            <TestServicesContextProvider overrides={careVisitOverrides}>
                <CareVisitRoot careVisitInitState={mockVisitState()}>
                    {/* we don't want a EvidencePageCommandForm / CommandFormTestOutput style emitting one batch of commands. because its fluid here */}
                    {/* so here its about testing the reducer, where we've avoided repo calls */}
                    <CareVisitSummaryCard />
                </CareVisitRoot>
            </TestServicesContextProvider>
        );
        //cy.pause();
    });
});

describe("CareVisitSummary tests", () => {
    it("state operations", () => {
        mount(
            <TestServicesContextProvider overrides={careVisitOverrides}>
                <CareVisitRoot careVisitInitState={mockVisitState()}>
                    {/* we don't want a EvidencePageCommandForm / CommandFormTestOutput style emitting one batch of commands. because its fluid here */}
                    {/* so here its about testing the reducer, where we've avoided repo calls */}
                    <CareVisitSummaryCard />
                </CareVisitRoot>
            </TestServicesContextProvider>
        );
        cy.contains("Grace Fields");
        //cy.findByText("client details").should("exist");
        cy.findByText("visit").should("exist").click();

        cy.findByText("planned tasks:").should("exist");
        cy.findByText("Clean bedroom").should("exist");

        cy.findByText("Clean bedroom").click();
        // TODO check the icon/state hasn't changed (testing editable)
        //cy.get(`[aria-label="close"]`).click();

        // START
        cy.findByText("start visit").click();

        cy.findByText("Clean bedroom").click();
        //cy.findByText("Clean bedroom").findByRole('button', {name: "close-icon"}).should("exist");

        cy.findByText("previous visits:").should("exist");
        cy.findByText("load").should("exist").click();
        cy.findByText("more").should("exist").click().click();
        cy.findByText("no more history").should("exist");

        // FINISH
        cy.findByText("finish visit").click();
        //cy.findByText("finish visit").should("exist");

        // click a reason
        cy.get('[name*="stopReasonId"]').should("have.length", 1).parent().click();
        cy.get('[aria-expanded="true"]').should("have.length", 1);
        // TODO check disabled doesn't show (tenant on holiday)
        //cy.findByText("tenant on holiday").should("not.exist");
        cy.get('[id="menu-stopReasonId"]').find("li").should("have.length", 3);
        cy.get('[id="menu-stopReasonId"]').find("li").eq(1).click();

        // click a location
        cy.get('[name*="locationId"]').should("have.length", 1).parent().click();
        cy.get('[aria-expanded="true"]').should("have.length", 1);
        cy.get('[id="menu-locationId"]').find("li").should("have.length", 3);
        cy.get('[id="menu-locationId"]').find("li").eq(1).click();

        // DIALOG - no longer used, see commit to undo
        // enter a note
        // TODO our selectors were really hard to get working - comment.eq(1) should not be required (use .then?)
        //  also the data-test don't always get added? https://github.com/mui/material-ui/issues/5711
        // we needed 'dialog' eq(1) but even then the 'comment' was not liking it!
        // cy.get('[data-test="comment-area"]').should("have.length", 2);
        // cy.get("[role='dialog']").eq(1).find('[data-test="comment-area"]').should("have.length", 1);
        // cy.get("[role='dialog']").eq(1).find('[data-test="comment-area"]').click();
        // cy.get('[name*="comment"]').eq(1).type("comment...");
        //cy.get("[role='dialog']").eq(1).find('[data-test="textarea-comment"]').type("comment...")
        //cy.findByText("Add note").should("exist");
        // save
        // TODO our selectors were really hard to get working - eq(2) should not be required (use .then?)
        // cy.get('[id="action-add"]').eq(1).click();
        //cy.findByText("save").click();

        // NO DIALOG
        cy.get('[name*="comment"]').type("comment...");

        // save
        // prepare to intercept the 'alert' in showErrorAsAlert, because any error we throw causes a popup which cypress consumes
        var alerted: boolean | string = false;
        cy.on("window:alert", msg => (alerted = msg));

        // TODO our selectors were really hard to get working - eq(0) should not be required (use .then?)
        cy.get('[id="action-finish"]')
            .eq(0)
            .click()
            .then(() => expect(alerted).to.be.eq(false));
        //.then( () => expect(alerted).to.match(/Failed to save your changes/));
    });

    // ideally we test transformToCareVisit directly, but need a refactor for that
    it("planned tasks - due only if not saved", () => {
        mount(
            <TestServicesContextProvider overrides={careVisitOverrides}>
                <CareVisitRoot careVisitInitState={mockTransformToCareVisit()}>
                    <CareVisitSummaryCard />
                </CareVisitRoot>
            </TestServicesContextProvider>
        );
        cy.contains("Grace Fields");
        cy.findByText("visit").should("exist").click();

        // verify the task is not shown
        cy.findByText("Not due / unplanned task 1").should("not.exist");

        // verify the task is shown
        // this is an odd test - see commit
        cy.findByText("Not due / unplanned task 2").should("exist");

        // verify the task is shown
        cy.findByText("Not due / unplanned task 3").should("exist");
        // verify the edit still shows it, even though its disabled
        cy.findByText("start visit").click();
        cy.findByText("Not due / unplanned task 3").click();
        //cy.get(".MuiFormGroup-root > :nth-child(4)").should("exist").contains("tenant on holiday");
    });

    it("previous visits", () => {
        mount(
            <TestServicesContextProvider overrides={careVisitOverrides}>
                <CareVisitRoot careVisitInitState={mockTransformToCareVisit()}>
                    <CareVisitSummaryCard />
                </CareVisitRoot>
            </TestServicesContextProvider>
        );
        cy.findByText("visit").should("exist").click();

        cy.findByText("load").should("exist").click();

        // verify the timezone in BST
        // previous visits: 'start2' timestamp is 2022-03-30T10:55:33.718Z - BST begins 2022-03-27 ends 2022-10-30
        cy.findByText("Started: Wed, 30 Mar 2022, 11:55").should("exist");
        // test status show in history
        cy.findByText("Not completed: not complete").should("exist");

        cy.findByText("more").should("exist").click();

        // verify the timezone NOT in BST
        // previous visits: 'start' timestamp is 2022-03-20T09:45:33.718Z - BST begins 2022-03-27 ends 2022-10-30
        cy.findByText("Started: Sun, 20 Mar 2022, 09:45").should("exist");
    });
});
