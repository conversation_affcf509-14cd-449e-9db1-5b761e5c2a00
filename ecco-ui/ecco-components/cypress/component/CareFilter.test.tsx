import {mount} from "cypress/react";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {CareVisitRoot} from "../../care/CareVisitRoot";
import {CareVisitSummaryCard} from "../../care/CareVisitSummaryCard";
import * as React from "react";
import {careVisitOverrides, mockVisitState} from "./testData";
import {
    CareVisitMenu,
    CareVisitMenuContextProvider,
    SearchForm,
    useCareVisitMenuContext
} from "../../care/CareVisitMenu";

const SearchFormWrapper = () => {
    const ctxMenu = useCareVisitMenuContext();
    if (!ctxMenu) {
        return null;
    }

    return (
        <SearchForm
            searchData={ctxMenu.searchData}
            onChange={searchData => {
                ctxMenu.setSearchData(searchData);
            }}
            onClose={searchData => {
                ctxMenu.setSearchData(searchData);
                ctxMenu.setOpenSearch(false);
            }}
        />
    );
};

describe("CareFilter search tests", () => {
    it("state operations", () => {
        mount(
            <TestServicesContextProvider overrides={careVisitOverrides}>
                <CareVisitMenuContextProvider>
                    <SearchFormWrapper />
                </CareVisitMenuContextProvider>
            </TestServicesContextProvider>
        );
        //cy.pause();
    });
});

describe("CareFilter menu tests", () => {
    it("state operations", () => {
        mount(
            <TestServicesContextProvider overrides={careVisitOverrides}>
                <CareVisitMenuContextProvider>
                    <CareVisitMenu key="page-menu" />
                </CareVisitMenuContextProvider>
                <CareVisitRoot careVisitInitState={mockVisitState()}>
                    {/* we don't want a EvidencePageCommandForm / CommandFormTestOutput style emitting one batch of commands. because its fluid here */}
                    {/* so here its about testing the reducer, where we've avoided repo calls */}
                    <CareVisitSummaryCard />
                </CareVisitRoot>
                <CareVisitRoot careVisitInitState={mockVisitState(199, "Daisy Plant")}>
                    {/* we don't want a EvidencePageCommandForm / CommandFormTestOutput style emitting one batch of commands. because its fluid here */}
                    {/* so here its about testing the reducer, where we've avoided repo calls */}
                    <CareVisitSummaryCard />
                </CareVisitRoot>
            </TestServicesContextProvider>
        );
        cy.contains("Grace Fields");
        cy.findAllByText("visit").first().should("exist").click();
        cy.findByText("start visit").click();
        cy.get('[aria-label="back"]').click();
    });
});
