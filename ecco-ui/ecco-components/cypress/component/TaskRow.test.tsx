import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {TaskRow} from "../../tasks/TaskRow";
import {TaskDto, WorkflowDto, WorkflowOperations} from "ecco-dto";
import {WorkflowActiveRecord} from "../../tasks/WorkflowLoader";

const overrides = {
    sessionData: sessionData
} as EccoAPI;

const taskDto: TaskDto = {
    assignedTo: null,
    isAvailable: true,
    isCompleted: false,
    taskDefinitionHandle: "linear-workflow:-:109860:referralAccepted",
    taskName: "referralAccepted",
    taskHandle: "200412-5-c13556ea-b3a0-4131-b436-4475c00a2fa3",
    dueDate: "2024-05-07T00:00:00",
    endTime: null,
    links: [
        {
            rel: "self",
            href: "https://demo.eccosolutions.co.uk/ql/api/tasks/200412-5-c13556ea-b3a0-4131-b436-4475c00a2fa3"
        },
        {
            rel: "edit",
            href: "https://demo.eccosolutions.co.uk/ql/api/tasks/200412-5-c13556ea-b3a0-4131-b436-4475c00a2fa3"
        }
    ]
};

const workflowDto: WorkflowDto = {
    serviceRecipientId: 200412,
    tasks: [taskDto]
};
const workflow: WorkflowOperations = new WorkflowActiveRecord(workflowDto, () => {});

describe("TaskRow tests", () => {
    it("it mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <TaskRow
                    key={"taskName"}
                    sessionData={sessionData}
                    serviceType={sessionData.getServiceTypeById(1)}
                    disabled={false}
                    task={taskDto}
                    workflow={workflow}
                    username={sessionData.getDto().username}
                    title={"title"}
                    subTitle={"workflow-link"}
                    onClick={() => {}}
                    SummaryComponent={props => <div />}
                />
            </TestServicesContextProvider>
        );

        //cy.findByLabelText("completed").should("exist");
    });
});
