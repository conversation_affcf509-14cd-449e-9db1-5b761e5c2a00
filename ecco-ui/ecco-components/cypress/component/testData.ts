import {Uuid} from "@eccosolutions/ecco-crypto";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {CareTask, CareVisitState} from "../../care/careVisitState";
import {EccoAPI, nullTaskIntegrations} from "../../EccoAPI";
import {
    AssociatedContactCommandDto,
    BaseServiceRecipientCommandDto,
    ClientAjaxRepository,
    ClientRepository,
    ContactsAjaxRepository,
    EvidenceGroup,
    GoalUpdateCommandDto,
    Individual,
    ServiceRecipient,
    ServiceRecipientAjaxRepository,
    ServiceRecipientRepository,
    SmartStepStatus,
    WorkEvidenceCommandDto
} from "ecco-dto";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {sessionData} from "../../__tests__/testUtils";
import {
    CommandQueue,
    CommandAjaxRepository,
    CommandRepository,
    GoalUpdateCommand,
    Command,
    MergeableCommand,
    isNewStyleCommand,
    SupportCommentCommand
} from "ecco-commands";
import {generateLoneWorkerUpdate} from "../../care/careVisitReducer";

const previousVisitHistoryLoad = (page: number) => {
    switch (page) {
        case 0:
            return Promise.resolve(previousVisits.slice(0, 4));
        case 1:
            return Promise.resolve(previousVisits.slice(4));
        case 2:
            return Promise.resolve([]);
    }
};

const clientRepository = getFailAllMethodsMock<ClientRepository>(ClientAjaxRepository);
const contactsRepository = getFailAllMethodsMock(ContactsAjaxRepository);
contactsRepository.findOneIndividual = id => Promise.resolve({contactId: id} as Individual);

const serviceRecipientRepository = getFailAllMethodsMock<ServiceRecipientRepository>(
    ServiceRecipientAjaxRepository
);
function mockSrDtoPromise(srId: number) {
    return Promise.resolve({contactId: 5} as ServiceRecipient);
}
serviceRecipientRepository.findOneServiceRecipientById = mockSrDtoPromise;
serviceRecipientRepository.findServiceRecipientTaskCommandsByCreated = (
    serviceRecipientId,
    pageNumber,
    taskName
) => previousVisitHistoryLoad(pageNumber);

const cmdRepo = getFailAllMethodsMock<CommandRepository>(CommandAjaxRepository);

// test that we set the eventStatusId, else throw an error BUT we also need to trap the error alert window - see 'expect(alerted).to.be.eq(false)'
// TODO however, its the loneWorker that creates the comment comment - search 'const loneWorkerUpdate'
cmdRepo.sendCommand = (command: Command | MergeableCommand) => {
    if (isNewStyleCommand(command)) {
        if (command.toCommandDto().commandName == "comment") {
            // this causes an alert with 'failed to save your changes'
            const cmd = (command as SupportCommentCommand).toCommandDto() as WorkEvidenceCommandDto;
            const valid = !!cmd.eventStatusId;
            if (!valid) {
                throw Error("eventStatusId missing from command");
            }
            if (cmd.workUuid != "cb3bed6e-9070-426e-749c-83cbef1fd236") {
                throw Error("workUuid not correct in command");
            }
        }

        if (command.toCommandDto().commandName == "goalUpdate") {
            // this causes an alert with 'failed to save your changes'
            const cmd = (command as GoalUpdateCommand).toCommandDto() as GoalUpdateCommandDto;
            if (cmd.workUuid != "cb3bed6e-9070-426e-749c-83cbef1fd236") {
                throw Error("workUuid not correct in command");
            }
        }
    }
    return Promise.resolve(null);
};

export const careVisitOverrides = {
    contactsRepository,
    clientRepository,
    serviceRecipientRepository,
    getCommandRepository: () => cmdRepo,
    sessionData: sessionData,
    taskIntegrations: nullTaskIntegrations
} as EccoAPI;

function randomNumber() {
    return Math.random();
}

// can't easily use transformToCareVisit when in different repo, so just grab bits that matter for now
// as we'll want to sort it properly
export function mockTransformToCareVisit() {
    const state = mockVisitState();
    const removeUnplannedTasks = true;
    const tasksLoad = state.tasksLoad;
    state.tasksLoad = () =>
        tasksLoad().then(tasks =>
            // see this commit for comments in careSingleVisitDataLoader
            // because this logic was a bit weird, and is now redundant,
            // but we retain the test
            tasks.filter(t => (removeUnplannedTasks ? !!t.taskTime || t.listDefId != null : true))
        );
    return state;
}

export function mockVisitState(
    srId: number = randomNumber(),
    displayName = "Grace Fields"
): Partial<CareVisitState> {
    const eventId = "44c7c0c4-b23d-4418-8ce7-adc97c49b803:20220320T170000";
    const workUuid = Uuid.parse("cb3bed6e-9070-426e-749c-83cbef1fd236");
    return {
        cmdRepo: cmdRepo,
        cmdQueue: new CommandQueue(cmdRepo),
        serviceRecipientId: srId, // testdomcare srId
        stopOptionsOn: true,
        displayName: displayName,
        scheduleInfo: "schedule info",
        taskSummary: "task summary",
        visitDateTime: "18th Mar, 8:00pm",
        resources: {links: []},
        commentForm: {},
        address: "Blah, CB3 3BB",
        tasks: null,
        workLoad: () => Promise.resolve(null),
        tasksLoad: () =>
            Promise.resolve([
                {
                    taskText: "Clean bedroom",
                    taskTime: null,
                    taskDescription: null,
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: null
                },
                {
                    taskText: "Administer medication",
                    taskDescription: "under the armpit",
                    taskTime: EccoDateTime.nowLocalTime().subtractMinutes(20),
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: null
                },
                {
                    taskText: "Administer medication",
                    taskDescription: null,
                    taskTime: EccoDateTime.nowLocalTime().addMinutes(10),
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: null
                },
                {
                    taskText: "Administer medication",
                    taskDescription: null,
                    taskTime: EccoDateTime.nowLocalTime().addMinutes(610),
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: null
                },
                {
                    taskText: "Not due / unplanned task 1",
                    taskDescription: null,
                    taskTime: null,
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: null
                },
                {
                    taskText: "Not due / unplanned task 2",
                    taskDescription: null,
                    taskTime: null,
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: 190 // not complete
                },
                {
                    taskText: "Not due / unplanned task 3",
                    taskDescription: null,
                    taskTime: null,
                    taskInstanceId: Uuid.randomV4().toString(),
                    actionDefId: 1,
                    listDefId: 192 // tenant on holiday, but its disabled so should still show
                }
            ]),
        loneWorkerUpdate: state =>
            generateLoneWorkerUpdate(
                careVisitOverrides.sessionData,
                srId,
                eventId,
                workUuid.toString()
            )(state),
        // see calendarEventCard
        taskUpdateCmd: (
            task: CareTask,
            outcomeId: number | null,
            plannedDateTime: EccoDateTime | null
        ) => {
            return new GoalUpdateCommand(
                "update",
                Uuid.randomV4(),
                workUuid,
                200003,
                "rotaVisit",
                task.actionDefId,
                EvidenceGroup.needs,
                Uuid.parse(task.taskInstanceId),
                null
            )
                .withEventId(eventId)
                .changeStatus(null, SmartStepStatus.AchievedAndStillRelevant)
                .setForceStatusChange()
                .changeStatusChangeReason(task.listDefId, outcomeId)
                .setPlannedDateTime(plannedDateTime);
        }
    };
}

const start: Partial<AssociatedContactCommandDto> = {
    //plannedDurationMins: undefined,
    //location: undefined,

    attendanceStatus: 0,
    commandName: "associatedContact",
    commandUri: "service-recipients/200003/evidence/needs/rotaVisit/contact/1/",
    contactId: 1,
    displayName: "Mickey Mouse",
    eventId: "44c7c0c4-b23d-4418-8ce7-adc97c49b803:20220320T170000",
    latestClientCode: "100003",
    // latestProjectId: "1",
    // latestReferralId: "100003",
    // latestServiceId: "100073",
    serviceRecipientId: 200003,
    timestamp: "2022-03-20T09:45:33.718Z",
    userDisplayName: "sysadmin",
    userName: "sysadmin",
    uuid: "6d9578eb-44cd-4c1d-7fd3-29cc02ce1136",
    workUuid: "cb3bed6e-9070-426e-749c-83cbef1fd236"
};

export const taskComplete: GoalUpdateCommandDto = {
    plannedDateTime: "20220320T150000",
    actionDefId: 2,
    actionInstanceUuid: "df2efebe-2cd3-4c55-778f-2ba30b5aa0e6",
    commandName: "goalUpdate",
    commandUri: "service-recipients/200003/evidence/needs/rotaVisit/goals/100029/",
    displayName: "Mickey Mouse",
    eventId: "44c7c0c4-b23d-4418-8ce7-adc97c49b803:20220320T170000",
    forceStatusChange: true,
    latestClientCode: "100003",
    // latestProjectId: "1",
    // latestReferralId: "10,0003",
    // latestServiceId: "100073",
    operation: "update",
    serviceRecipientId: 200003,
    statusChange: {from: null, to: 5},
    statusChangeReason: {from: null, to: 188},
    timestamp: "2022-03-20T09:45:50.096Z",
    userDisplayName: "sysadmin",
    userName: "sysadmin",
    uuid: "4ce766a8-b0fd-4de3-779a-e1ec9b1b03d6",
    workUuid: "cb3bed6e-9070-426e-749c-83cbef1fd236"
};

const stop: Partial<AssociatedContactCommandDto> = {
    //plannedDurationMins: undefined,
    //location: undefined,

    attendanceStatus: 1,
    commandName: "associatedContact",
    commandUri: "service-recipients/200003/evidence/needs/rotaVisit/contact/1/",
    contactId: 1,
    displayName: "Mickey Mouse",
    eventId: "44c7c0c4-b23d-4418-8ce7-adc97c49b803:20220320T170000",
    latestClientCode: "100003",
    // latestProjectId: "1",
    // latestReferralId: "100003",
    // latestServiceId: "100073",
    serviceRecipientId: 200003,
    timestamp: "2022-03-20T09:45:53.072Z",
    userDisplayName: "sysadmin",
    userName: "sysadmin",
    uuid: "e6294eae-d970-4d54-792f-7ffbbf4c60bf",
    workUuid: "cb3bed6e-9070-426e-749c-83cbef1fd236"
};

const comment: Partial<WorkEvidenceCommandDto> = {
    commandName: "comment",
    commandUri: "service-recipients/200003/evidence/needs/rotaVisit/comments/",
    comment: {from: null, to: "Visit went well."},
    displayName: "Mickey Mouse",
    evidenceGroup: "needs",
    latestClientCode: "100003",
    // latestProjectId: "1",
    // latestReferralId: "100003",
    // latestServiceId: "100073",
    operation: "update",
    serviceRecipientId: 200003,
    taskName: "rotaVisit",
    timestamp: "2022-03-20T09:45:53.198Z",
    userDisplayName: "sysadmin",
    userName: "sysadmin",
    uuid: "b76b6fb0-**************-e80122dc2447",
    workDate: {from: null, to: "2022-03-20T09:45:33.717"},
    workUuid: "cb3bed6e-9070-426e-749c-83cbef1fd236"
};

const start2: Partial<AssociatedContactCommandDto> = {
    ...start,
    timestamp: "2022-03-30T10:55:33.718Z",
    workUuid: "bb3bed6e-9070-426e-749c-83cbef1fd236",
    uuid: Uuid.randomV4().toString()
};
const taskComplete2: Partial<GoalUpdateCommandDto> = {
    ...taskComplete,
    actionInstanceUuid: "ff2efebe-2cd3-4c55-778f-2ba30b5aa0e6",
    timestamp: "2022-03-30T10:55:33.718Z",
    workUuid: "bb3bed6e-9070-426e-749c-83cbef1fd236",
    uuid: Uuid.randomV4().toString(),
    statusChangeReason: {from: null, to: 190}
};
const comment2: Partial<WorkEvidenceCommandDto> = {
    ...comment,
    comment: {from: null, to: "Visit was okay"},
    eventStatusId: {from: null, to: 190},
    timestamp: "2022-03-30T10:55:33.718Z",
    workUuid: "bb3bed6e-9070-426e-749c-83cbef1fd236",
    uuid: Uuid.randomV4().toString()
};
const stop2: Partial<AssociatedContactCommandDto> = {
    ...stop,
    timestamp: "2022-03-30T10:55:33.718Z",
    workUuid: "bb3bed6e-9070-426e-749c-83cbef1fd236",
    uuid: Uuid.randomV4().toString()
};

const previousVisits: BaseServiceRecipientCommandDto[] = [
    start2 as AssociatedContactCommandDto,
    taskComplete2 as GoalUpdateCommandDto,
    stop2 as AssociatedContactCommandDto,
    comment2 as WorkEvidenceCommandDto,
    start as AssociatedContactCommandDto,
    taskComplete as GoalUpdateCommandDto,
    stop as AssociatedContactCommandDto,
    comment as WorkEvidenceCommandDto
];
