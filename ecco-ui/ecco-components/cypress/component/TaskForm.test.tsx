import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {TaskForm} from "../../tasks/TaskForm";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {
    WorkersAjaxRepository,
    WorkersRepository,
    TaskRepository,
    TaskCommandAjaxRepository
} from "ecco-dto";
import {mockWorkersDtoPromise} from "../../test-support/mockWorkers";
import {mockTaskPromise} from "../../test-support/mockTasks";

const workersRepository = getFailAllMethodsMock<WorkersRepository>(WorkersAjaxRepository);
workersRepository.findWorkersWithSameAccess = mockWorkersDtoPromise;
const tasksRepository = getFailAllMethodsMock<TaskRepository>(TaskCommandAjaxRepository);
tasksRepository.getTask = mockTaskPromise;

const overrides = {
    sessionData: sessionData,
    workersRepository: workersRepository,
    tasksRepository: tasksRepository
} as EccoAPI;

describe("TaskForm tests", () => {
    it("it mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <TaskForm taskHandle={"999-27"} show={true} setShow={() => {}} />
            </TestServicesContextProvider>
        );
        cy.findByLabelText("completed").should("exist");
        cy.findByLabelText("completed").should("not.be.disabled");
        // by default we are not a manager, so other fields are disabled
        cy.findByLabelText("due date").should("be.disabled");
    });
});
