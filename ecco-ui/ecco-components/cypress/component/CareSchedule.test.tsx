import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {carer1, carer2, sessionData} from "../../__tests__/testUtils";
import {Command} from "ecco-commands";
import {CommandFormTest, CommandFormTestOutput} from "../../cmd-queue/testUtils";
import {
    Agreement,
    AgreementDto,
    DemandScheduleTaskHandleDto,
    REL_DEMAND_SCHEDULE_TASK_HANDLES,
    RotaAjaxRepository,
    RotaRepository
} from "ecco-rota";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {CommandForm} from "../../cmd-queue/CommandForm";
import {DemandScheduleTaskDto} from "ecco-rota";
import {ScheduleFromIds} from "../../agreements/EditScheduleForm";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {
    ApiClient,
    DemandScheduleDto,
    ServiceRecipient,
    ServiceRecipientAjaxRepository,
    ServiceRecipientRepository,
    SupportAction,
    SupportSmartStepsSnapshotDto,
    SupportSmartStepsSnapshotRepository,
    SupportWorkAjaxRepository,
    WorkersAjaxRepository
} from "ecco-dto";

const sr: ServiceRecipient = {
    /** Indicate the type of service recipient (r / b / w / ct) - see BaseServiceRecipient.getPrefix */
    prefix: "r",
    //parentId?: number
    serviceRecipientId: 99,
    serviceAllocationId: 1,
    serviceTypeId: 1,
    displayName: "bob",
    contactId: 0,
    calendarId: "cal-uuid"
    //parentServiceRecipientId?: number;
};

const srLoad = (srId: number) => {
    return Promise.resolve(sr);
};
const serviceRecipientRepository = getFailAllMethodsMock<ServiceRecipientRepository>(
    ServiceRecipientAjaxRepository
);
serviceRecipientRepository.findOneServiceRecipientById = serviceRecipientId =>
    srLoad(serviceRecipientId);

const rotaRepository = getFailAllMethodsMock<RotaRepository>(RotaAjaxRepository);
rotaRepository.findAgreementByAgreementId = (aId: number) => Promise.resolve(testAgreement);
rotaRepository.findScheduleById = (aId: number, sId: number) => Promise.resolve(testSchedule); // with link of REL_DEMAND_SCHEDULE_TASK_HANDLES

const apiClient = getFailAllMethodsMock<ApiClient>(ApiClient);
apiClient.fetchRelations = (res, rel) => Promise.resolve([testScheduleTaskHandle as any]);

const snapshotRepository =
    getFailAllMethodsMock<SupportSmartStepsSnapshotRepository>(SupportWorkAjaxRepository);
snapshotRepository.findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime = (
    serviceRecipientId: number,
    evidenceGroupKey: string,
    workDate: EccoDateTime
) => Promise.resolve(snapshot);

const workersRepository = getFailAllMethodsMock(WorkersAjaxRepository);
workersRepository.findWorkersWithAccessTo = (
    serviceId: number,
    projectId?: number | undefined,
    role?: string | undefined
) => {
    return Promise.resolve([carer1, carer2]);
};

const overrides = {
    serviceRecipientRepository: serviceRecipientRepository,
    rotaRepository: rotaRepository,
    apiClient: apiClient,
    supportSmartStepsSnapshotRepository: snapshotRepository,
    workersRepository: workersRepository,
    sessionData: sessionData
} as any as EccoAPI;

describe("CareSchedule tests", () => {

    const newTaskCmdRegEx =
        /"operation":"add","taskDefId":1,"taskInstanceId":"(.+)","taskDescription":{"from":null,"to":"my NEW desc"}}]/;

    function addANewTask() {
        // add a new one
        cy.get("#eccotest-add").should("exist").click();
        cy.contains("new task");
        cy.get("#eccotest-category").click().get("li").eq(1).click(); // index 0 is the ListItem of existing tasks
        cy.get('[name*="task-name"]').type("my NEW name");
        cy.get('[name*="task-description"]').type("my NEW desc");
        // we shouldn't see 'delete' on the first render
        cy.get('[name*="task-disable"]').should("not.exist");
        cy.get("#action-add").click();
        cy.get(".MuiListItem-root").should("exist").contains("my NEW name");
    }

    function setApplicableFrom() {
        cy.get('[name*="applicable from"]').type("25/05/2022");
    }

    function deleteTask(index: number) {
        // delete original one
        cy.get(".eccotest-pencil").eq(index).should("exist").click();
        cy.get('[name*="task-disable"]').click();
        cy.get("#action-update").click();
    }

    it("end to end", () => {
        // DEBUG - set this high or null to see the cmd output for longer/forever
        // currently needs to clear to show a command wasn't emitted - see "delete new one" below
        const delayClearOnFlushFinish = 500;

        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/* TEST with data hooks */}
                            <ScheduleFromIds
                                serviceRecipientId={99}
                                agreementId={testAgreement.getAgreementId()}
                                scheduleId={testSchedule.scheduleId}
                                form={form}
                            />
                            {/* TEST with command form
                                <Schedule
                                serviceRecipientId={99}
                                agreement={testAgreement}
                                schedule={testSchedule}
                                tasksDirect={[testScheduleTask]}
                                setShow={(show: boolean) => {}}
                                readOnly={!overrides.sessionData.hasRoleReferralEdit()}
                                sessionData={overrides.sessionData}
                                commandForm={form}
                            />*/}
                            {/* TEST - inner most
                                <AppointmentSchedule
                                schedule={testSchedule}
                                onChangeSchedule={() => {}}
                                agreement={testAgreement}
                                onChangeStaff={() => {}}
                                isSplit={false}
                                numStaff={1}
                        />*/}
                            <CommandFormTestOutput
                                cmdEmitted={cmdEmitted}
                                cmdEmittedDraft={cmdEmittedDraft}
                            />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        cy.get("#mui-component-select-categoryId").should("exist");
        cy.get(".MuiListItem-root").should("exist").contains("my goal");

        addANewTask();

        // edit the new one - to remove the 'my NEW name'
        cy.get(".eccotest-pencil").eq(1).should("exist").click();
        cy.get("#eccotest-category").should("be.disabled");
        cy.get('[name*="task-name"]').clear();
        cy.get("#action-update").click();
        // action1 is the new item's name
        cy.get(".MuiListItem-root").contains("action1").should("exist");
        // my NEW name was removed from the item
        cy.get(".MuiListItem-root").contains("my NEW name").should("not.exist");

        // attempt to submit, but we've not set applicable date
        // so we catch Promise.reject in cmd-queue/CommandForm 'if (errors.length > 0)...'
        // it would be nice if the errors object was in 'err' here, but we find its just Cypress:
        //      "The following error originated from your test code, not from Cypress"
        // see https://javascript.info/promise-error-handling
        // and https://docs.cypress.io/api/events/catalog-of-events#To-conditionally-turn-off-uncaught-exception-handling-unhandled-promise-rejections
        // and https://docs.cypress.io/guides/references/error-messages#Uncaught-exceptions-from-your-application
        /*
        cy.on("uncaught:exception", (err, runnable) => {
            //expect(err.message).to.include('something about the error')
            // return false to prevent the error from failing this test
            return false;
        });
        cy.findByRole("button", {name: "submit"}).click();
        cy.contains("you must set 'applicable from' for all changes except end date");
        setApplicableFrom();
*/

        cy.findByRole("button", {name: "submit"}).click();
        // no changes for initial task - test-uuid
        cy.get("#eccotest-cmdQueue").contains("test-uuid").should("not.exist");
        // has some tasksDirect
        //        cy.get("#eccotest-cmdQueue").contains('applicableFromDate":"2022-05-25","tasksDirect":[{');
        // new task
        cy.get("#eccotest-cmdQueue").contains(newTaskCmdRegEx);

        // NB testing the hooks (ScheduleFromIds) means the submit refreshes the original data, and the new one goes
        // so we change to deleting the original one
        deleteTask(0);
        // action1 we still see it, but the opacity is low
        cy.get(".MuiListItem-root").eq(0).should("have.css", "opacity", "0.75");

        cy.findByRole("button", {name: "submit"}).click();
        // DELAY here - need a delay by to ensure the old cmds are cleared
        // ...except cypress waits for us
        cy.get("#eccotest-cmdQueue").contains(newTaskCmdRegEx).should("not.exist");
    });

    it("add then transient delete", () => {
        const delayClearOnFlushFinish = undefined;

        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/* TEST with data hooks */}
                            <ScheduleFromIds
                                serviceRecipientId={99}
                                agreementId={testAgreement.getAgreementId()}
                                scheduleId={testSchedule.scheduleId}
                                form={form}
                            />
                            <CommandFormTestOutput
                                cmdEmitted={cmdEmitted}
                                cmdEmittedDraft={cmdEmittedDraft}
                            />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );

        addANewTask();

        //        setApplicableFrom();

        deleteTask(1);
        cy.get(".MuiListItem-root").eq(1).should("have.css", "opacity", "0.75");

        cy.findByRole("button", {name: "submit"}).click();
        cy.get("#eccotest-cmdQueue")
            .contains(/"operation":"add","taskDefId"/)
            .should("not.exist");
    });
});

const supportAction: SupportAction = {
    id: 101812,
    actionInstanceUuid: "test-uuid",
    name: "smart step",
    goalName: "my goal",
    goalPlan: "my goal description",
    status: 1,
    statusChange: true,
    actionId: 1,
    actionGroupId: 83,
    outcomeId: 83
} as SupportAction;

const snapshot: SupportSmartStepsSnapshotDto = {
    serviceRecipientId: null,
    evidenceGroupKey: null,
    parentId: null,
    staff: null,
    snapshotPeriod: null,
    referralSummary: null,
    latestFlags: [],
    latestActions: [supportAction]
};

const testScheduleTaskHandle: DemandScheduleTaskHandleDto = {
    taskInstanceId: supportAction.actionInstanceUuid
};

// this is now unused, its created from loading the snapshot
const testScheduleTask: DemandScheduleTaskDto = {
    taskDefId: 1,
    taskDefName: "smart step",
    taskInstanceId: "test-uuid",
    taskText: "my goal",
    taskDescription: "my goal description"
};

const testSchedule: DemandScheduleDto = {
    links: [{rel: REL_DEMAND_SCHEDULE_TASK_HANDLES, href: "something"}],
    eventRef: "e-ref",
    agreementId: 99,
    scheduleId: 99,
    serviceRecipientId: 99,
    serviceRecipientName: "client name",
    adHoc: false,
    parentScheduleId: null,
    previousScheduleId: null,
    childScheduleIds: [],
    durationMins: 7,
    start: EccoDate.todayLocalTime().subtractDays(5).formatIso8601(),
    //applicableFrom?: string
    time: "07:00",
    end: null,
    intervalType: "WK",
    intervalFrequency: 1,
    calendarDays: [1, 2, 3, 4],
    title: "my sched",
    categoryId: 1,
    rateCardId: null,
    //rateCardName?: string;
    //parameters?: DemandParametersDto;
    appointments: []
};

const testAgreement = new Agreement({
    start: EccoDate.todayLocalTime().subtractMonths(1).formatIso8601(),
    end: EccoDate.todayLocalTime().addMonths(1).formatIso8601(),
    demandSchedules: [testSchedule]
} as AgreementDto);
