import {MUIDataTable, Typography} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, ReactNode} from "react";
import {MUIDataTableColumnDef, MUIDataTableOptions} from "mui-datatables";

// from MuiDataTable.data
export type DataType = Array<object | number[] | string[]>;

const DataSelectedToolbar: FC<{
    toolbar: (selectedData: () => any[]) => ReactNode;
    selectedRows: {
        data: Array<{index: number; dataIndex: number}>;
        lookup: {[key: number]: boolean};
    };
    displayData: Array<{data: any[]; dataIndex: number}>;
    setSelectedRows: (rows: number[]) => void;
}> = props => {
    const extractSelectedData = () => {
        // NB could do with being the other way around - selecting displayData and matching
        const dataIndexes = props.selectedRows.data.map(d => d.dataIndex);
        return props.displayData
            .filter(d => dataIndexes.indexOf(d.dataIndex) > -1)
            .map(d => d.data);
    };

    return <>{props.toolbar(extractSelectedData)}</>;
};

// see https://github.com/gregnb/mui-datatables#demo
export const DataTable: FC<{
    title: string;
    columns: MUIDataTableColumnDef[];
    data: DataType;
    toolbar: (selectedData: () => Array<any[]>) => ReactNode;
}> = props => {
    const options: MUIDataTableOptions = {
        filter: true,
        filterType: "multiselect",
        responsive: "standard",
        setFilterChipProps: (_colIndex, _colName, _data) => {
            //console.log(colIndex, colName, data);
            return {
                color: "primary",
                variant: "outlined",
                className: "testClass123"
            };
        },
        rowsPerPage: 25,
        rowsPerPageOptions: [25, 50, 150],
        customToolbarSelect: (selectedRows, displayData, setSelectedRows) => (
            <DataSelectedToolbar
                selectedRows={selectedRows}
                displayData={displayData}
                setSelectedRows={setSelectedRows}
                toolbar={props.toolbar}
            />
        )
    };

    return (
        <div>
            <MUIDataTable
                options={options}
                columns={props.columns}
                title={
                    <Typography variant="h6">
                        {props.title}
                        {/*{!resourceList && <CircularProgress size={24} style={{marginLeft: 15, position: 'relative', top: 4}}/>}*/}
                    </Typography>
                }
                data={props.data} /* : [["Loading..."]]}*/
                /*options={tableOptions}*/
            />
        </div>
    );
};
