import {ScheduleRowProps} from "../rota/SchedulerView";
import {EccoDate, EccoDateTime, NumberToObjectMap} from "@eccosolutions/ecco-common";
import {RotaRepository} from "ecco-rota";
import {EventSnapshotDto, frequencyDaysDisplay, frequencyTypeDisplay, SessionData} from "ecco-dto";
import {summariseAddress} from "../address/AddressLocationForm";
import {RangeLookup} from "ecco-dto";

/*
useCareOrEventCard (for one appointment) loads additional data:
- work item
- additionalStaff on the event (using AdditionalStaff)
- visitDetail for extra info such as key worker and schedule info
- current eventStatus (lone worker for start/stop timer)
- directTaskHandles for the tasks to show/complete on the visit

useRota (for a lot of appointments) loads many events:
- resources / demand which loads calendar events - (findRecurrencesFromCalendar / findRecurrences from demandScheduleHandle calendars)
- but it has passed through RotaDecorator (from recurrenceDecorated) to get a small amount of additional data (eg latest address)
- the attendees on the event (using attendeeOwner - NB this doesn't need to load AdditionalStaff since that will show as a separate apt)

EvidenceView (for a lot of appointments, with data) loads many events with additional data:
 - getDataByDate loads getUninvoicedLines (ie unverified) which loads
    - the rota resources (planned date)
        - where each resource loads the schedule for rate card info (appointmentToLine)
    - the work in range +-1 (completed date) (addEvidenceToInvoiceLine - DODGY for +-1)
    - already saved invoice lines so we can exclude them (filterUnverifiedEvidence)
 - EvidenceVerificationRow loads for each row
    - supportWork
    - referral

TODO performance of a month of rota resources to find "unrecorded work is time consuming", and doesn't even include dropped (need to load rota demand)
    we could:
        - 1) retain the snapshot for the duration (and missed/dropped visits for longer?)
            - ensure we can re-create the snapshot (similar to rota page, or uninvoiced now - with rota demand also)
        - 2) re-use the rota page and 'verify what is seen' (and nicely cached)? but only a week at a time, and still not ideal for scheduler
        - 3) somehow trigger uninvoiced to do the same job
*/

export function loadSchedule(
    sessionData: SessionData,
    repo: RotaRepository,
    startDate: EccoDate | undefined,
    endDate: EccoDate
) {
    return repo
        .findEventSnapshotsFromToUtc(startDate?.toDateTimeMidnight(), endDate.toDateTimeMidnight())
        .then(data => convertToSchedule(data, sessionData));
}

function convertToSchedule(data: EventSnapshotDto[], sessionData: SessionData) {
    const fmtDate = (dte: string | undefined) =>
        dte && EccoDateTime.parseIso8601Utc(dte).toEccoDate().formatIso8601();
    //const toFormatShort = EccoDateTime.iso8601UtcToFormatShort;

    const calculation = new Calculation(sessionData);

    return data.map(e => {
        const r: ScheduleRowProps = {
            eventUid: e.eventUid,
            demandScheduleId: e.demandScheduleId,
            srDisplayName: `${e.demandContact?.firstName} ${e.demandContact?.lastName}`,
            srAddress: summariseAddress(e.demandContact?.address), // the location is overwritten from ServiceRecipientEventDecorator as csv
            srId: e.serviceRecipientId!,
            srAllocationId: e.serviceAllocationId,
            aptTitle: null, // TODO e.title,
            aptTypeId: e.demandScheduleDto?.categoryId,
            aptTypeName:
                (e.demandScheduleDto?.categoryId &&
                    sessionData.getAppointmentTypeById(e.demandScheduleDto.categoryId).name) ||
                null,
            plannedStartDateTime: fmtDate(e.plannedStartInstant), // ISO8601 local-datetime
            taskSummary: e.demandScheduleDto?.parameters?.tasks,
            frequencyType: frequencyTypeDisplay(
                e.demandScheduleDto?.intervalType,
                e.demandScheduleDto?.intervalFrequency
            ),
            frequencyDays: frequencyDaysDisplay(
                e.demandScheduleDto?.intervalType,
                e.demandScheduleDto?.calendarDays
            ),
            plannedEndDateTime: fmtDate(e.plannedEndInstant),
            plannedResource:
                e.resourceContact && `${e.resourceContact.firstName} ${e.resourceContact.lastName}`,
            workDateTime: fmtDate(e.startInstant), // ISO8601 local-datetime (extended, zoneless - so 7am BST is stored as 7am). NB workdate saved from changeWorkDate(null, state.timerStartedAt?.formatIso8601())
            workAuthorDisplayName: null,
            dueData: null
        };

        if (r.aptTypeId) {
            r.dueData = calculation.calculateTextAndValue(e, r);
        }

        return r;
    });
}

/**
 * CRITICAL: 24 HOURS 90% / 48 HOURS 10% (risk to life, eg battery failure, lost pendant)
 * URGENT: 5 DAYS 90% / 10 DAYS 10%
 * NB We use rangeLookups where the 'range' is days and 'lookupText' is the colour
 * aptType critical:
 *      '{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-1", "lookupText": "yellow"},{"range": "2-", "lookupText": "red"}]}}'
 * aptType urgent:
 *      {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-5", "lookupText": "yellow"},{"range": "6-", "lookupText": "red"}]}
 * NB Also see Calculation in QuestionGroupControls
 */
class Calculation {

    private calcsByAptTypeId: NumberToObjectMap<Map<number, string>> = {};

    constructor(private sessionData: SessionData) {

        sessionData.getAppointmentTypes().forEach(a => {
            const alg = a.parameters?.algorithm;
            if (alg) {
                this.calcsByAptTypeId[a.id] = this.constructRangeLookups(alg.rangeLookups);
            }
        });

    }

    private constructRangeLookups(ranges: RangeLookup[]) {
        const lookupTextOrdered = new Map<number, string>();
        ranges.forEach(r => {
            const fromTo = r.range.split('-');
            if (Number(fromTo[1]) > 0) {
                // add the lower bound - special case as might not start at zero
                if (lookupTextOrdered.size == 0 && Number(fromTo[0]) > 0) {
                    lookupTextOrdered.set(Number(fromTo[0])-1, '-');
                }
                lookupTextOrdered.set(Number(fromTo[1]), r.lookupText);
            }
        })
        return lookupTextOrdered;
    }

    public calculateTextAndValue(e: EventSnapshotDto, r: ScheduleRowProps) {
        const nullRange = {text: "", value: ""};
        // we use the schedule start, so only ad-hoc works for overdue
        // if (!e.demandScheduleDto.adHoc) {
        //     return nullRange;
        // }
        // if no type, then we can't get the timeframes
        if (!r.aptTypeId) {
            return nullRange;
        }
        // if no algorithm, then we can't get the timeframes
        const aptType = this.sessionData.getAppointmentTypeById(r.aptTypeId);
        if (!aptType.parameters?.algorithm?.enabled) {
            return nullRange;
        }
        const ranges = aptType.parameters?.algorithm.rangeLookups;
        if (!ranges) {
            return nullRange;
        }

        const start = EccoDate.parseIso8601(e.demandScheduleDto.start).toDateTimeMidnight();
        const end = e.endInstant
            ? EccoDateTime.parseIso8601Utc(e.endInstant)
            : EccoDate.todayLocalTime().toDateTimeMidnight();
        const daysDiff = end.subtractDateTime(start, true).getDays();

        const colour = this.lookupValue(r.aptTypeId, daysDiff);
        return {text: `${daysDiff}`, value: `${colour}`};
    }

    public lookupValue(aptTypeId: number, candidate: number) {
        const range = this.calcsByAptTypeId[aptTypeId]
        if (range) {
            let desc = null;
            for (let [key, value] of range) {
                if (candidate <= key && !desc) {
                    desc = value;
                }
            }
            return desc;
        }
        return "";
    }

}

/* when we have invoiceRepository.findAllUninvoicedLines, as per EvidenceView - but we now use the snapshot table
function convertToSchedule(data: InvoiceLineDto[]) {
    return data.map(e => {
        const r: ScheduleRowProps = {
            srDisplayName: null,
            srAddress: null, //e.location, // the location is overwritten from ServiceRecipientEventDecorator as csv
            srId: e.serviceRecipientId,
            aptTitle: e.description,
            aptType: e.type, // TODO
            plannedStartDateTime: e.plannedDate, // ISO8601 local-datetime
            plannedEndDateTime: undefined, // e.end,
            // from the rota... see ClientSalesInvoiceDetailResourceLineAssembler appointmentToLine and withWork
            plannedResource: e.plannedResourceName,
            workDateTime: e.workDate, // ISO8601 local-datetime (extended, zoneless - so 7am BST is stored as 7am). NB workdate saved from changeWorkDate(null, state.timerStartedAt?.formatIso8601())
            workAuthorDisplayName: null //e.
        };
        return r;
    });
}*/

/*
export function loadSchedule(repo: InvoicesRepository, startDate: EccoDate, endDate: EccoDate) {

    const services = useServicesContext();
    const [data, setData] = useState<ScheduleRowProps[] | null>(null);
    const [referrals, setReferrals] = useState<ReferralSummaryDto[] | null>(null);

    useEffect(() => {
        services.invoicesRepository
            .findAllUninvoicedLines(startDate!, endDate!, null)
            .then(data => {
                convertToSchedule(data);
            });
    }, [startDate, endDate]);
    return {data};

    // useEffect(() => {
    //     const singlePromise = (ids: number[]) =>
    //         services.referralRepository.findAllReferralSummaryByServiceRecipientId(ids)
    //             .then(refSums => setReferrals(refSums));
    //     const srQ = loadChunksAndAssign(referrals, "srId", singlePromise);
    //     srQ.then(r => setReferrals(r))
    // }, [data])
}
function convertToSchedule(data: EventResourceDto[]) {
    return data.map(e => {
        const r: ScheduleRowProps = {
            srDisplayName: null,
            srAddress: e.location, // the location is overwritten from ServiceRecipientEventDecorator as csv
            srId: e.serviceRecipientId,
            aptTitle: e.title,
            aptType: e.eventType, // TODO
            plannedStartDateTime: e.start, // ISO8601 local-datetime
            plannedEndDateTime: e.end,

            // from the rota... see ClientSalesInvoiceDetailResourceLineAssembler appointmentToLine and withWork
            // TODO what about attendees?
            plannedResource: null,
            workDateTime: undefined, // ISO8601 local-datetime (extended, zoneless - so 7am BST is stored as 7am). NB workdate saved from changeWorkDate(null, state.timerStartedAt?.formatIso8601())
            workAuthorDisplayName: null
        };
        return r;
    });
}
*/

/*
function findAllReferralSummaryByServiceRecipientIdChunks(serviceRecipientIds: number[] | string[]): Promise<ReferralSummaryDto[]> {
    const singlePromise = (ids: (string | number)[]) =>
        this.apiClient.get<ReferralSummaryDto[]>("referrals/byServiceRecipients/summary/", {
            query: {ids: ids.join(",")}
        });
    return loadChunks<(number | string), ReferralSummaryDto>(serviceRecipientIds, singlePromise);
}
*/
