/*
import {useMemo} from "react";
import {usePromise, useServicesContext} from "ecco-components";
*/
import {Uuid} from "@eccosolutions/ecco-crypto";
import {NumberToObjectMap, StringToObjectMap} from "@eccosolutions/ecco-common";
import {
    ClientWithContactUpdateCommand,
    CommandQueue,
    CommentCommandBuilder,
    RiskCommentCommand,
    SupportCommentCommand
} from "ecco-commands";
import {
    Client,
    EvidenceDef,
    EvidenceGroup,
    FlagArea,
    FlagEvidenceDto,
    ReferralWithEntities,
    RiskFlagsSnapshotDto,
    ServiceRecipientWithEntities
} from "ecco-dto";
import services = require("ecco-offline-data");
import {
    ServiceRecipientWithEntitiesContext,
    useCurrentServiceRecipientWithEntities
} from "./serviceRecipientHooks";
import {usePromise} from "./entityLoadHooks";
import {showNotification} from "ecco-components-core";

//
export function useExternalSync() {
    const {resolved: sre} = useCurrentServiceRecipientWithEntities();
    const {resolved, error, loading} = usePromise(
        () =>
            ExternalSync.syncFromExternalFile(sre).then(messages => {
                messages.forEach(status => {
                    if (status.code == "failed") {
                        showNotification("warning", status.message);
                    }
                    if (status.code == "changes") {
                        showNotification("info", status.message);
                    }
                });
            }),
        [sre?.serviceRecipient?.serviceRecipientId]
    );
    return {messages: resolved, error, loading};
}

class BackingData {
    constructor(public sr: ServiceRecipientWithEntities, public client: Client) {
    }
}

export class ExternalSync {
    constructor(private data: BackingData) {}

    public static syncFromExternalFile(
        context: ServiceRecipientWithEntitiesContext | null
    ): Promise<{code: string; message: string}[]> {
        if (
            !context ||
            !(context.serviceRecipient && context.client && !!context.client.externalSystemRef)
        ) {
            return Promise.resolve([{code: "", message: ""}]);
        }
        const data = new BackingData(context.serviceRecipient!, context.client!);
        const cls = new ExternalSync(data);
        return cls.sync();
    }

    public static syncReferralFromExternalFile(
        referral: ReferralWithEntities,
        client: Client
    ): Promise<{code: string; message: string}[]> {
        if (!(referral || client)) {
            return Promise.resolve([{code: "", message: ""}]);
        }
        const data = new BackingData(referral, client);
        const cls = new ExternalSync(data);
        return cls.sync();
    }

    // TODO probably should be a post to trigger sync server side, and give response/refresh to user
    public sync(): Promise<{code: string; message: string}[]> {
        let clientQ = Promise.resolve({code: "notLinked", message: "not linked"});
        let flagsQ = Promise.resolve({code: "notLinked", message: "not linked"});
        if (this.data.client.externalSystemRef != null) {
            clientQ = this.syncClientFromExternalFile();
            flagsQ = this.syncFlagsFromExternalFile();
        }
        return clientQ.then(c => flagsQ.then(f => [c, f]));
    }

    private syncClientFromExternalFile(): Promise<{code: string; message: string}> {
        return services
            .getClientRepository()
            .findOneExternalClientBySourceAndRef(
                this.data.client.externalSystemSource,
                this.data.client.externalSystemRef
            )
            .then(externalClient => {
                const commandQueue = new CommandQueue();

                // TODO sync address change !!
                //const cmd = new AddressHistoryCommand(Uuid.randomV4(), "remove", serviceRecipientId!, deletingItem); // NOTE: This needs a contactId variant if srId is null

                const cmd = new ClientWithContactUpdateCommand(
                    this.data.sr.serviceRecipientId,
                    undefined,
                    true
                );
                cmd.change(this.data.client, externalClient);
                const hasChanges = cmd.hasChanges();
                if (hasChanges) {
                    commandQueue.addCommand(cmd);
                }

                return commandQueue
                    .flushCommands(false)
                    .then(() =>
                        hasChanges
                            ? {code: "changes", message: "changes received"}
                            : {code: "noChange", message: "no changes"}
                    )
                    .catch(_e => {
                        //showErrorAsAlert(e);
                        return {code: "failed", message: "changes failed"};
                    });
            });
    }

    private syncFlagsFromExternalFile(): Promise<{code: string; message: string}> {
        const flagRiskSnapshotQ: Promise<RiskFlagsSnapshotDto> = services
            .getRiskWorkRepository()
            .findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(
                this.data.sr.serviceRecipientId
            );
        const flagSnapshotsQ = services
            .getServiceRecipientRepository()
            .fetchStatusAreaEvidenceFlags(
                this.data.sr.features,
                this.data.sr.serviceRecipientId,
                this.data.sr.features.getServiceTypeByServiceCategorisationId(
                    this.data.sr.serviceAllocationId
                )
            );
        const externalFlagsQ = services
            .getServiceRecipientRepository()
            .findExternalFlagsBySourceAndRef(
                this.data.client.externalSystemSource,
                this.data.client.externalSystemRef
            );

        return flagRiskSnapshotQ.then(flagRiskSnapshot =>
            flagSnapshotsQ.then(flagSnapshots =>
                externalFlagsQ.then(externalFlagsOn => {
                    const commandQueue = new CommandQueue();

                    // mash all the internal flag ids together into flagIdsChanged
                    // we do this (rather than keep separately in risk/qnr etc) because the snapshot is only for data saved, so if
                    // the external source has a new flag - we still need to lookup the correct evidence page, so may do for all
                    // NB we ASSUME here that the flags don't cross over different evidence pages because that distinction is lost between systems anyway
                    const flagIdsChanged = this.getFlagIdsChanged(
                        flagRiskSnapshot,
                        flagSnapshots,
                        externalFlagsOn
                    );

                    // organise the flag changes by evidence pages, to create the command
                    const taskNameWithFlagIdsChanged =
                        this.getTaskNameWithFlagIdsChanged(flagIdsChanged);

                    // create a cmd for each task with changed flags
                    const workUuid = Uuid.randomV4();
                    let hasChanges = false;
                    for (let taskName in taskNameWithFlagIdsChanged) {
                        const serviceType =
                            this.data.sr.features.getServiceTypeByServiceCategorisationId(
                                this.data.sr.serviceAllocationId
                            );
                        const evidenceDef = EvidenceDef.fromTaskName(
                            this.data.sr.features,
                            serviceType,
                            taskName
                        );

                        // see EvidenceCommentForm.emitChangesTo
                        let builder: CommentCommandBuilder;
                        let from: number[];
                        if (evidenceDef.getEvidenceGroup() == EvidenceGroup.threat) {
                            builder = RiskCommentCommand.create(
                                false,
                                workUuid,
                                this.data.sr.serviceRecipientId,
                                evidenceDef.getEvidenceGroup(),
                                taskName,
                                true
                            );
                            // from - the flagIds that are on
                            from = flagRiskSnapshot.latestFlags
                                .filter(fs => fs.value)
                                .map(fs => fs.flagId);
                        } else {
                            builder = SupportCommentCommand.create(
                                false,
                                workUuid,
                                this.data.sr.serviceRecipientId,
                                evidenceDef.getEvidenceGroup(),
                                taskName,
                                true
                            );
                            const flagArea = flagSnapshots
                                .filter(fs => fs.evidenceDef.getTaskName() == taskName)
                                .pop();
                            from = flagArea
                                ? flagArea.flagSnapshots.filter(fs => fs.value).map(fs => fs.flagId)
                                : [];
                        }

                        // the cmd only wants ON flags - it works out the addedRemoved itself
                        const toFlagsOn = taskNameWithFlagIdsChanged[taskName]!.filter(
                            f => flagIdsChanged[f]
                        );

                        if (Object.keys(flagIdsChanged).length > 0) {
                            builder.changeFlags(from, toFlagsOn);
                            const cmd = builder.build();
                            if (cmd.hasChanges()) {
                                hasChanges = true;
                                commandQueue.addCommand(cmd);
                            }
                        }
                    }

                    return commandQueue
                        .flushCommands(false)
                        .then(() =>
                            hasChanges
                                ? {code: "changes", message: "changes received"}
                                : {code: "noChange", message: "no changes"}
                        )
                        .catch(_e => {
                            //showErrorAsAlert(e);
                            return {code: "failed", message: "changes failed"};
                        });
                })
            )
        );
    }

    private getTaskNameWithFlagIdsChanged(flagIdsChanged: NumberToObjectMap<boolean>) {
        const taskNameWithFlagIds: StringToObjectMap<number[]> = this.getFlagIdsByEvidencePage();
        const taskNameWithFlagIdsChanged: StringToObjectMap<number[]> = {};
        const flagIdsChangedIds: number[] = [];
        for (let flagId in flagIdsChanged) {
            flagIdsChangedIds.push(Number(flagId));
        }
        for (let taskName in taskNameWithFlagIds) {
            const flagIdsForTask = taskNameWithFlagIds[taskName];
            taskNameWithFlagIdsChanged[taskName] = flagIdsForTask!.filter(
                f => flagIdsChangedIds.indexOf(f) > -1
            );
        }
        return taskNameWithFlagIdsChanged;
    }

    private getFlagIdsChanged(
        flagRiskSnapshot: RiskFlagsSnapshotDto,
        flagSnapshots: FlagArea[],
        externalFlagsOn: FlagEvidenceDto[]
    ): NumberToObjectMap<boolean> {
        let flagIdsOnInternal: number[] = [];
        flagIdsOnInternal = flagIdsOnInternal.concat(
            flagRiskSnapshot.latestFlags?.filter(f => f.value).map(f => f.flagId)
        );
        flagIdsOnInternal = flagIdsOnInternal.concat(
            flagSnapshots
                .map(s => s.flagSnapshots)
                .reduce((r, x) => r.concat(x), []) // flatMap
                .filter(f => f.value)
                .map(f => f.flagId)
        );

        const flagIdsOnExternal = externalFlagsOn.filter(f => f.value).map(f => f.flagId);

        // calculate intermediate data of flags turned on/off
        const flagIdsTurnedOn = flagIdsOnExternal.filter(f => flagIdsOnInternal.indexOf(f) == -1);
        const flagIdsTurnedOff = flagIdsOnInternal.filter(f => flagIdsOnExternal.indexOf(f) == -1);
        const flagIdsChanged: NumberToObjectMap<boolean> = {};
        flagIdsTurnedOn.forEach(f => {
            flagIdsChanged[f] = true;
        });
        flagIdsTurnedOff.forEach(f => {
            flagIdsChanged[f] = false;
        });

        // ql has a number of flags pointing to one ecco flag
        // any of the collective flags want to trigger a target ecco flag, but ecco only triggers one ql flag
        // but if turn ecco's flag off and sync, we don't want already-on collective ql flags to re-trigger ecco
        // so, we need to track the details of each flag to manage this - and not just the collective result
        // which we can do via a 1-1 mapping - meaning all the ql flags are saved as snapshots in ecco (history / audits etc)
        // it's just the collective flag changes that we need to manage here - just new ones, because we don't even need to
        // handle the edge case when all the collective flags are off because we can rely on the 1-1 target ecco flag
        // NB the externalSource property in the command prevents us then triggering ql updates, which is good

        // handle any collective flag 'on' changes
        // TODO if we did this server-side we would probably use the mapping file for this intermediate/extra mapping
        const sessionData = this.data.sr.features;
        const flagIdsToTurnOn: number[] = flagIdsTurnedOn.map(
            fIdOn =>
                sessionData.getListDefinitionEntryById(fIdOn).getDto().metadata?.proxyForId || fIdOn
        );

        // turn on the targeted/proxied flags from collective flags
        flagIdsToTurnOn.forEach(fId => {
            flagIdsChanged[fId] = true;
        });

        return flagIdsChanged;
    }

    private getFlagIdsByEvidencePage(): StringToObjectMap<number[]> {
        const taskNameWithFlagListDefs: StringToObjectMap<number[]> = {};
        const sessionData = this.data.sr.features;
        const tasks = sessionData
            .getServiceTypeByServiceCategorisationId(this.data.sr.serviceAllocationId)
            .getTaskDefinitionEntries();
        tasks.forEach(t => {
            const flagIdListDefs = sessionData
                .getServiceTypeByServiceCategorisationId(this.data.sr.serviceAllocationId)
                // NB disabled are filtered out
                .getFlagsByListNameOrId(sessionData, t.getName())
                .map(ld => ld.getId());
            if (flagIdListDefs.length > 0) {
                taskNameWithFlagListDefs[t.getName()] = flagIdListDefs;
            }
        });
        return taskNameWithFlagListDefs;
    }
}
