import * as React from "react";
import {createContext, FC, ReactChild, ReactElement, ReactNode, useContext} from "react";
import {
    EccoAPI,
    PageComponentFactory,
    PageComponentProps
} from "./EccoAPI";
import {Messages} from "ecco-dto";

export const ServicesContext = createContext<EccoAPI | null>(null);
export default ServicesContext;

/** @deprecated prefer useServicesContext() */
export function withServicesContext(children: (value: EccoAPI | null) => ReactNode) {
    return <ServicesContext.Consumer>
        {data => children(data)}
    </ServicesContext.Consumer>
}

/**
 * You can usually assert that this isn't null by doing useServicesContext(), and this should be replaced
 * with a hook for useFetch style behaviour combined with Suspense.
 * A good way to have got a configured context is via mountWithServices() or RouteManagedPage
 */
export function useServicesContext(): EccoAPI {
    const context = useContext(ServicesContext);
    if (!context) {
        throw new Error("You must wrap the component with a <ServicesContextProvider>");
    }
    return context;
}


export function usePageComponentRegistry() {
    const {pageComponentRegistry} = useServicesContext();
    return pageComponentRegistry;
}

/**
 * @param key key (usually a router sub-path) to use to look up the component
 * @param srId to be passed in props to the component
 * @param preview to be passed in props to the component
 * @param eventId to be passed in props to the component
 * @return A JSX element with that renders the relevant component with the supplied props
 */
export function usePageComponentLookup(
    srId: number,
    preview: boolean,
    eventId?: string | undefined
): (key: string) => PageComponentFactory {
    const props: PageComponentProps = {srId, preview, eventId};
    const registry = usePageComponentRegistry();
    return key => {
        const L = lookup(key);
        return () => <L {...props} />;
    };
    function lookup(key: string): PageComponentFactory {
        let C = registry.lookup(key);
        if (!C) {
            C = () => {
                console.error(`Could not find ${key} in registry`, registry);
                return (
                    <>
                        <p>
                            No registered page component found for '{key}' in registry. See browser
                            console for details
                        </p>
                    </>
                );
            };
        }
        return C;
    }
}

export const WithMessages: FC<{renderer: (messages: Messages) => ReactChild}> = ({renderer}) => {
    const messages = useServicesContext().sessionData.getMessages()
    return <>{renderer(messages)}</>
}

export function withMessages(renderer: (messages: Messages) => ReactChild): ReactElement {
    return <WithMessages renderer={renderer}/>;
}