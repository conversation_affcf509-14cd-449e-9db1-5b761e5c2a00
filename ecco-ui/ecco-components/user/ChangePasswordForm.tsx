import * as React from "react";
import {FC, useState} from "react";
import {EccoV3Modal} from "ecco-components-core";
import {Grid, Typography} from "@eccosolutions/ecco-mui";
import {passwordInput} from "ecco-components-core";

interface FormData {
    changePassword: string;
    confirmChangePassword: string;
}

export const ChangePasswordForm: FC<{onChange: (newPassword: string | null) => void}> = ({onChange}) => {

    const [data, setData] = useState<FormData>({} as FormData);
    const invalid =
        data.changePassword &&
        data.confirmChangePassword &&
        data.changePassword !== data.confirmChangePassword;

    return (
        <EccoV3Modal
            title="update password"
            show={true}
            onCancel={() => onChange(null)}
            onSave={() => {
                onChange(data.changePassword);
            }}
            action="save"
            saveEnabled={
                data.changePassword != null &&
                data.changePassword.length > 0 &&
                data.changePassword == data.confirmChangePassword
            }
        >
            <Grid container>
                <Grid item xs={12}>
                    {passwordInput(
                        "changePassword",
                        "new password",
                        setData,
                        data,
                        "perHApz a PHr4se?",
                        undefined,
                        undefined,
                        true
                    )}
                </Grid>
                <Grid item xs={12}>
                    {passwordInput(
                        "confirmChangePassword",
                        "confirm password",
                        setData,
                        data,
                        "must match",
                        undefined,
                        undefined,
                        true
                    )}
                </Grid>
                {invalid && (
                    <Grid item xs={12}>
                        <Typography variant="subtitle1" color="error">
                            passwords must match
                        </Typography>
                    </Grid>
                )}
            </Grid>
        </EccoV3Modal>
    );
}
