import * as React from "react"
import {FC} from "react"

import geek from "../images/S1_geek_V2.png";
import {Dialog} from "@eccosolutions/ecco-mui";
import {EccoTheme} from "ecco-components-core";
import {applicationRootPath} from "application-properties";

const textWords = <>
    <div style={{marginBottom: 20}}>Before you go...</div>
        Thank you
        <br/>for everything
        <br/>you do.
    </>;

export const GeekyBlokeSays: FC = props => (
    <div style={{padding: 16}}>
        <img alt="Image of geeky posh bloke" src={geek} />
        <div
            style={{
                fontSize: 28,
                lineHeight: "1.2em",
                fontFamily: "amatic sc,cursive",
                color: "#01416F",
                position: "relative",
                top: -268,
                left: 286,
                width: 230,
                height: 8
            }}
        >
            {props.children}
        </div>
    </div>
);

export const LoggedOut: FC = () => <EccoTheme prefix="out">
    <Dialog
        open={true}
        onClose={() => window.location.href = `${applicationRootPath}nav/r/welcome/`}
    >
        <GeekyBlokeSays>{textWords}</GeekyBlokeSays>
    </Dialog>
</EccoTheme>;