import type {
    AddressedLocationRepository,
    AddressHistoryRepository,
    ApiClient,
    BuildingRepository,
    CalendarRepository,
    ChartRepository,
    ClientRepository,
    ContactsAjaxRepository,
    ContractRepository,
    FinanceRepository,
    FormEvidenceRepository,
    InvoicesRepository,
    ReferralRepository,
    RiskEvidenceRepository,
    SessionData,
    SignatureRepository,
    SupportSmartStepsSnapshotRepository,
    SupportWorkRepository,
    TaskRepository,
    UserRepository,
    WorkersRepository,
    ServiceRecipientRepository,
    QuestionnaireWorkRepository,
    BaseServiceRecipientCommandDto,
    IncidentRepository,
    RepairRepository,
    NotificationRepository,
    ManagedVoidRepository
} from "ecco-dto";
import {setGlobalApiClient, TaskDto} from "ecco-dto";
import type {RotaRepository} from "ecco-rota";
import {assertNotNull, setGlobalCommandRepository} from "ecco-commands";
import type {CommandRepository} from "ecco-commands";
import {EvidenceRepositoryEffectsWrapper} from "ecco-offline-data";
import {
    ServiceRecipientContext,
    ServiceRecipientWithEntitiesContext
} from "./data/serviceRecipientHooks";
import * as React from "react";
import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {ComponentType} from "react";
import {CommandViewHandler} from "./service-recipient/AuditHistoryPaged";

export interface TaskIntegrations {
    handleTaskClick: (
        srId: number,
        task: TaskDto,
        srContext: ServiceRecipientWithEntitiesContext,
        setActiveTask: (
            value: ((prevState: TaskDto | null) => TaskDto | null) | TaskDto | null
        ) => void
    ) => void;
    formForTask: (
        context: ServiceRecipientContext,
        task: TaskDto,
        onSaved: () => void
    ) => JSX.Element;
    registerIntegrationHooks: () => void;
}

export const nullTaskIntegrations = {
    handleTaskClick: (
        _srId: number,
        _task: TaskDto,
        _srContext: ServiceRecipientWithEntitiesContext,
        _setActiveTask: (
            value: ((prevState: TaskDto | null) => TaskDto | null) | TaskDto | null
        ) => void
    ) => {},
    formForTask: (_context: ServiceRecipientContext, _task: TaskDto, _onSaved: () => void) => (
        <h3>not available in app yet</h3>
    ),
    registerIntegrationHooks: () => {}
};


export const nullAuditHistoryIntegrations = {
    handleCommand: () => {
        // command: BaseServiceRecipientCommandDto, sessionData: SessionData
        return {} as CommandViewHandler<any>;
    },
    componentFactory: () => {
        // sessionData: SessionData
        return {} as AuditHistoryItemControl;
    }
};

export interface AuditHistoryItemControl {
    processHandler(handler: CommandViewHandler<any>): void;
    domElement(): HTMLElement;
}
export interface AuditHistoryIntegrations {
    handleCommand: (
        command: BaseServiceRecipientCommandDto,
        sessionData: SessionData
    ) => CommandViewHandler<any>;
    componentFactory: (sessionData: SessionData) => AuditHistoryItemControl;
}

export type ComponentFactory<T> = ComponentType<T>;

export interface ComponentRegistry<T> {
    configure: (map: StringToObjectMap<ComponentFactory<T>>) => void;
    /** Lookup the relevant component */
    lookup: (key: string) => ComponentFactory<T>;
}

export class ConfigurableComponentRegistry<T> implements ComponentRegistry<T> {
    public registry = new Map<string, ComponentFactory<T>>();

    constructor(map?: StringToObjectMap<ComponentFactory<T>> | undefined) {
        if (map) this.configure(map);
    }
    configure(map: StringToObjectMap<ComponentFactory<T>>): void {
        this.registry = new Map(Object.entries(map));
    }

    lookup(key: string): ComponentFactory<T> {
        console.assert(this.registry.size > 0, "No components have been registered");
        return this.registry.get(key)!;
    }
}

export interface PageComponentProps {
    srId: number;
    preview: boolean;
    eventId: string | undefined;
}
export type PageComponentRegistry = ComponentRegistry<PageComponentProps>;
export type PageComponentFactory = ComponentType<PageComponentProps>;
export class ConfigurablePageComponentRegistry extends ConfigurableComponentRegistry<PageComponentProps> {}

export interface EccoAPI {
    baseURI: string;
    apiClient: ApiClient;
    sessionData: SessionData; // This is null while loading
    pageComponentRegistry: PageComponentRegistry;
    auditHistoryIntegrations: AuditHistoryIntegrations;
    taskIntegrations: TaskIntegrations;
    getAddressRepository: () => AddressedLocationRepository;
    addressHistoryRepository: AddressHistoryRepository;
    getBuildingRepository: () => BuildingRepository;
    calendarRepository: CalendarRepository;
    chartRepository: ChartRepository;
    clientRepository: ClientRepository;
    getCommandRepository: () => CommandRepository;
    contactsRepository: ContactsAjaxRepository;
    contractRepository: ContractRepository;
    getEvidenceEffectsRepository: () => EvidenceRepositoryEffectsWrapper;
    financeRepository: FinanceRepository;
    formEvidenceRepository: FormEvidenceRepository;
    invoicesRepository: InvoicesRepository;
    incidentsRepository: IncidentRepository;
    managedVoidsRepository: ManagedVoidRepository;
    repairsRepository: RepairRepository;
    notificationRepository: NotificationRepository;
    questionnaireSnapshotRepository: QuestionnaireWorkRepository;
    referralRepository: () => ReferralRepository;
    riskWorkRepository: RiskEvidenceRepository;
    rotaRepository: RotaRepository;
    getSignatureRepository: () => SignatureRepository;
    supportWorkRepository: SupportWorkRepository;
    supportSmartStepsSnapshotRepository: SupportSmartStepsSnapshotRepository;
    tasksRepository: TaskRepository;
    workersRepository: WorkersRepository;
    userRepository: UserRepository;
    serviceRecipientRepository: ServiceRecipientRepository;
}

let globalEccoAPI: EccoAPI | null = null;

export function setGlobalEccoAPI(eccoAPI: EccoAPI) {
    assertNotNull(eccoAPI.apiClient);

    globalEccoAPI = eccoAPI;
    setGlobalCommandRepository(eccoAPI.getCommandRepository);
    setGlobalApiClient(eccoAPI.apiClient);
}

export function hasGlobalEccoAPI() {
    return !!globalEccoAPI;
}
export function getGlobalEccoAPI() {
    assertNotNull(
        globalEccoAPI,
        "Please refresh the browser page and ignore the following :-)  (Developers! You cannot call getGlobalEccoAPI() before setGlobalEccoAPI() has been called. DON'T call at global scope)"
    );
    // for above. Ensure that getGlobalEccoAPI() is only called within a function or class, not for assigning to a const at module scope
    // as that will cause it to be called before ServicesContextProvider has had chance to call setGlobalEccoAPI()
    assertNotNull(globalEccoAPI!.apiClient);
    return globalEccoAPI!;
}
