// From https://gist.github.com/Danziger/336e75b6675223ad805a88c2dfdcfd4a
import {EffectCallback, MutableRefObject, useEffect, useRef} from 'react';

/**
 * Use setInterval with <PERSON><PERSON> in a declarative way.
 *
 * @see https://stackoverflow.com/a/59274004/3723993
 * @see https://overreacted.io/making-setinterval-declarative-with-react-hooks/
 */
export function useInterval(callback: EffectCallback, delaySecs: number, start = true): MutableRefObject<number | null> {
    const intervalRef = useRef<number | null>(null);
    const callbackRef = useRef(callback);

    // Remember the latest callback:
    //
    // Without this, if you change the callback, when setInterval ticks again, it
    // will still call your old callback.
    //
    // If you add `callback` to useEffect's deps, it will work fine but the
    // interval will be reset.
    useEffect(() => {
        callbackRef.current = callback;
    }, [callback]);

    // Set up the interval
    useEffect(() => {
        if (start) {
            intervalRef.current = window.setInterval(() => callbackRef.current(), delaySecs * 1000);

            // Clear interval if the components is unmounted or the delay changes:
            return () => window.clearInterval(intervalRef.current || 0);
        }
        return undefined
    }, [delaySecs, start]);

    // In case you want to manually clear the interval from the consuming component...:
    return intervalRef;
}