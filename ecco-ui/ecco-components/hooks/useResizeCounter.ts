import {useEffect, useState} from "react";
import {useCounter} from "../data/entityLoadHooks";

export function useResizeCounter(target: Element | null): number {
    const [counter, inc] = useCounter();
    const [observer] = useState(() => new ResizeObserver(inc));

    useEffect(() => {
        if (target != null) {
            observer.observe(target);
        }
        return () => {
            if (target != null) {
                observer.unobserve(target);
            }
        };
    }, [target]);

    return counter;
}