import {Button} from "@eccosolutions/ecco-mui";
import {Card} from "@eccosolutions/ecco-mui";
import {CardActions} from "@eccosolutions/ecco-mui";
import {CardContent} from "@eccosolutions/ecco-mui";
import {Container} from "@eccosolutions/ecco-mui";
import {Grid} from "@eccosolutions/ecco-mui";
import {Building} from "ecco-dto";
import * as React from "react";
import {FC, useState} from "react";
import {useBuildingCareRuns} from "../data/entityLoadHooks";
import {LoadingSpinner} from "../Loading";
import {Typography} from "@eccosolutions/ecco-mui";
import {CareRunEditor} from "./BuildingForm";
import {CommandForm} from "../cmd-queue/CommandForm";

export const CareRunList: FC<{buildingId: number}> = ({buildingId}) => {
    const {careruns, loading} = useBuildingCareRuns(buildingId);
    const [manageSrId, setManageSrId] = useState<number | null>(null);
    // const history = useHistory();

    const renderRow = (carerun: Building) => {
        const opacity = carerun.disabled ? 0.7 : 1;
        return (
            <>
                <Grid item>
                    <Card style={{opacity: opacity}}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                {carerun.name}
                            </Typography>
                            id: {carerun.buildingId}
                        </CardContent>
                        <CardActions>
                            <Button
                                size="small"
                                color="primary"
                                onClick={() => setManageSrId(carerun.serviceRecipientId)}
                            >
                                manage
                            </Button>
                        </CardActions>
                    </Card>
                </Grid>
                {manageSrId && (
                    <CommandForm
                        onCancel={() => () => setManageSrId(null)}
                        onFinished={() => () => setManageSrId(null)}
                    >
                        <CareRunEditor
                            serviceRecipientId={manageSrId}
                            modalHide={() => setManageSrId(null)}
                        />
                    </CommandForm>
                )}
            </>
        );
    };

    return (
        <Container maxWidth={"sm"}>
            {loading && <LoadingSpinner />}
            <Grid container spacing={1} direction="column">
                {careruns && careruns.map(renderRow)}
            </Grid>
        </Container>
    );
};
