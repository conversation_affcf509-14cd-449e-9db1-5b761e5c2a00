import * as React from "react";
import {FC, useState, useEffect, useMemo, useCallback} from "react";
import {
    Box,
    Button,
    Card,
    CardContent,
    CircularProgress,
    IconButton,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    ListItemSecondaryAction,
    Typography,
    Link
} from "@eccosolutions/ecco-mui";
import AddIcon from "@material-ui/icons/Add";
import EditIcon from "@material-ui/icons/Edit";
import HomeIcon from "@material-ui/icons/Home";
import {AdminMode} from "@eccosolutions/ecco-common";
import {
    Building,
    SessionData,
    LIST_DEF_IDS,
    adminModeEnabled,
    BuildingAjaxRepository
} from "ecco-dto";

import {useServicesContext} from "../ServicesContext";
import {BuildingEditor} from "./BuildingForm";
import {applicationRootPath} from "application-properties";
import {CommandForm} from "../cmd-queue/CommandForm";

type ListEntry = {building: Building; features: SessionData};

interface BuildingListItemProps {
    entry: ListEntry;
    onEdit: (srId: number) => void;
}

const BuildingListItem: FC<BuildingListItemProps> = ({entry, onEdit}) => {
    const [adminMode, setAdminMode] = useState(adminModeEnabled());

    useEffect(() => {
        const handler = (event?: AdminMode | undefined) => {
            setAdminMode(event?.enabled || false);
        };
        AdminMode.bus.addHandler(handler);
        return () => AdminMode.bus.removeHandler(handler);
    }, []);

    const handleEditClick = () => {
        onEdit(entry.building.serviceRecipientId);
    };

    const buildingUrl = `${applicationRootPath}nav/r/main/bldg/${entry.building.serviceRecipientId}/`;

    // Create rota links similar to the original rotaLinks function
    const rotaMenuItems = useMemo(() => {
        const items: React.ReactNode[] = [];

        if (entry.features.hasTopLevelGroup("rota", false)) {
            if (entry.features.isEnabled("rota.services") && entry.building.calendarId) {
                const svccats = entry.features
                    .getRestrictedServiceCategorisations()
                    .filter(sc => sc.buildingId === entry.building.buildingId)
                    .map(sc => sc.id);

                if (svccats.length > 0) {
                    const svccatCsv = svccats.join(",");
                    items.push(
                        <Button
                            key="rota"
                            size="small"
                            component="a"
                            href={`${applicationRootPath}nav/w/welcome/rota/week/workers:all/svccats:${svccatCsv}`}
                        >
                            rota
                        </Button>
                    );

                    if (entry.features.isEnabled("rota.shifts")) {
                        items.push(
                            <Button
                                key="run-builder"
                                size="small"
                                component="a"
                                href={`${applicationRootPath}nav/w/welcome/rota/week/careruns:all/svccats:${svccatCsv}`}
                            >
                                run builder
                            </Button>
                        );
                    }
                }
            }

            if (
                entry.features.isEnabled("buildings.overview.tabs.agreements") &&
                entry.building.calendarId
            ) {
                items.push(
                    <Button
                        key="rota-alt"
                        size="small"
                        component="a"
                        href={`${applicationRootPath}nav/w/welcome/buildings/${entry.building.buildingId}/rota/week`}
                    >
                        rota
                    </Button>
                );

                if (entry.features.isEnabled("rota.shifts")) {
                    items.push(
                        <Button
                            key="run-builder-alt"
                            size="small"
                            component="a"
                            href={`${applicationRootPath}nav/w/welcome/buildings/${entry.building.buildingId}/runs/day`}
                        >
                            run builder
                        </Button>
                    );
                }
            }
        }

        return items;
    }, [entry.building, entry.features]);

    return (
        <ListItem>
            <ListItemIcon>
                <HomeIcon />
            </ListItemIcon>
            <ListItemText
                primary={
                    <Link href={buildingUrl} underline="hover">
                        {entry.building.name}
                    </Link>
                }
                secondary={<Box>{rotaMenuItems}</Box>}
            />
            {adminMode && (
                <ListItemSecondaryAction>
                    <IconButton edge="end" onClick={handleEditClick} size="small">
                        <EditIcon />
                    </IconButton>
                </ListItemSecondaryAction>
            )}
        </ListItem>
    );
};

interface BuildingsListControlProps {
    srId?: number | null;
    showRuns?: boolean;
}

/** Match either runs or anything but runs depending on showRuns */
function omitOrIncludeRuns(showRuns: boolean) {
    return (building: Building) =>
        building.resourceTypeId === LIST_DEF_IDS.CARERUN_RESOURCETYPE_ID ? showRuns : !showRuns;
}

export const BuildingsListControl: FC<BuildingsListControlProps> = ({
    srId = null,
    showRuns = false
}) => {
    const {getBuildingRepository, sessionData} = useServicesContext();
    const [buildings, setBuildings] = useState<Building[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [adminMode, setAdminMode] = useState(adminModeEnabled());
    const [showEditor, setShowEditor] = useState(false);
    const [editingServiceRecipientId, setEditingServiceRecipientId] = useState<number | undefined>(
        undefined
    );
    const [newParentBuildingId, setNewParentBuildingId] = useState<number | undefined>(undefined);

    useEffect(() => {
        const handler = (event?: AdminMode | undefined) => {
            setAdminMode(event?.enabled || false);
        };
        AdminMode.bus.addHandler(handler);
        return () => AdminMode.bus.removeHandler(handler);
    }, []);

    const loadData = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            // Load buildings
            let buildingsData: Building[];
            if (srId) {
                buildingsData = await (
                    getBuildingRepository() as BuildingAjaxRepository
                ).findAllBuildingsOf(srId);
                buildingsData = buildingsData
                    .filter(b => !b.disabled)
                    .filter(omitOrIncludeRuns(showRuns))
                    .sort((a, b) => a.name?.localeCompare(b.name || "") || 0);
            } else {
                const params = new URLSearchParams(window.location.search);
                buildingsData = await getBuildingRepository().findAllBuildings({
                    resourceType: params.get("resourceType") ?? undefined,
                    showChildren: (params.get("showChildren") as "true" | "false") ?? undefined
                });
                buildingsData = buildingsData
                    .filter(b => !b.disabled)
                    .sort((a, b) => a.name?.localeCompare(b.name || "") || 0);
            }

            setBuildings(buildingsData);
        } catch (err) {
            setError(err instanceof Error ? err.message : "Failed to load buildings");
        } finally {
            setLoading(false);
        }
    }, [srId, showRuns, getBuildingRepository]);

    useEffect(() => {
        loadData();
    }, [loadData]);

    const handleAddNew = () => {
        setEditingServiceRecipientId(undefined);
        setNewParentBuildingId(srId || undefined);
        setShowEditor(true);
    };

    const handleEdit = (srId: number) => {
        setEditingServiceRecipientId(srId);
        setNewParentBuildingId(undefined);
        setShowEditor(true);
    };

    const handleCloseEditor = (load = true) => {
        setShowEditor(false);
        setEditingServiceRecipientId(undefined);
        setNewParentBuildingId(undefined);
        // Reload the buildings list after editing
        if (load) {
            loadData();
        }
    };

    const listEntries: ListEntry[] = useMemo(() => {
        if (!sessionData || !buildings) return [];
        return buildings.map(building => ({building, features: sessionData}));
    }, [buildings, sessionData]);

    const addButtonText = srId == null ? "new building" : "new unit";
    const noEntriesText = "no buildings defined";

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box p={2}>
                <Typography color="error">Error: {error}</Typography>
                <Button onClick={() => window.location.reload()}>Retry</Button>
            </Box>
        );
    }

    return (
        <>
            <Card>
                <CardContent>
                    {adminMode && (
                        <Box mb={2}>
                            <Button
                                startIcon={<AddIcon />}
                                onClick={handleAddNew}
                                variant="outlined"
                                size="small"
                            >
                                {addButtonText}
                            </Button>
                        </Box>
                    )}

                    {listEntries.length === 0 ? (
                        <Typography color="textSecondary">{noEntriesText}</Typography>
                    ) : (
                        <List>
                            {listEntries.map(entry => (
                                <BuildingListItem
                                    key={entry.building.buildingId}
                                    entry={entry}
                                    onEdit={handleEdit}
                                />
                            ))}
                        </List>
                    )}
                </CardContent>
            </Card>

            {showEditor && (
                <CommandForm
                    onCancel={() => handleCloseEditor(false)}
                    onFinished={handleCloseEditor}
                >
                    <BuildingEditor
                        serviceRecipientId={editingServiceRecipientId}
                        newParentBuildingId={newParentBuildingId}
                        modal={true}
                        modalHide={() => handleCloseEditor(false)}
                    />
                </CommandForm>
            )}
        </>
    );
};
