import {InvoiceDto, InvoiceLineDto, InvoiceStatus, RateCardDto} from "ecco-dto";
import {groupBy} from "../../data/entityLoaders";

export function getInvoiceRows(
    rateCards: RateCardDto[],
    invoicesData: InvoiceDto[]
): TempInvoiceDto[] {
    if (!invoicesData) {
        return [];
    }

    const invoicesPerRateCard = invoicesData
        .map(i => {
            if (!i.lines) {
                return [i];
            }
            const lines = i.lines!;
            const invPerRateCard: TempInvoiceDto[] = [];
            const linesPerRateCard = groupBy(lines, l => l.rateCardId);
            for (const [key, value] of linesPerRateCard.entries()) {
                const nextInv = {...i} as TempInvoiceDto;
                nextInv.rateCardId = key;
                nextInv.rateCardName = rateCards?.find(r => r.rateCardId == key)?.name || "";
                // set the invoice reference for the payroll table to lookup the rate card easily
                value.forEach(l => (l.invoice = nextInv));
                nextInv.lines = value;
                invPerRateCard.push(nextInv);
            }

            return invPerRateCard;
        })
        .reduce((r, x) => r.concat(x), []); // flatMap

    return invoicesPerRateCard.map(i => {
        const totalActualMins = i.lines
            ? i.lines.reduce((total, line) => total + (line.workMinutesSpent || 0), 0)
            : null;
        const totalPlannedMins = i.lines
            ? i.lines.reduce((total, line) => total + (line.plannedMinutes || 0), 0)
            : null;
        const t = i as TempInvoiceDto;
        t.totalActualMins = totalActualMins || 0;
        t.totalPlannedMins = totalPlannedMins || 0;
        return t;
    });
}

export function getPayrollRows(invoiceData: InvoiceDto[]): TempPayrollDto[] {
    // flatten the lines to then re-group as resources
    const lines = invoiceData
        .filter(i => i.lines)
        .map(i => i.lines!)
        .reduce((r, x) => r.concat(x), []); // flatMap
    const linesPerResource = groupBy(lines, l => l.plannedResourceCalendarId);

    const payPerRateCardResource: TempPayrollDto[] = [];
    for (const [_key, value] of linesPerResource.entries()) {
        const linesPerRate = groupBy(value, l => l.rateCardId);
        for (const [_keyR, valueR] of linesPerRate.entries()) {
            payPerRateCardResource.push(calculatePayrollItem(valueR));
        }
    }
    return payPerRateCardResource;
}

export interface ExportInvoiceDto
    extends Omit<TempInvoiceDto, "lines" | "serviceRecipientId" | "links"> {}
export function invoiceToExport(data: TempInvoiceDto[]): ExportInvoiceDto[] {
    return data.map(r => {
        return (({serviceRecipientId, lines, links, ...rest}) => rest)(r) as ExportInvoiceDto;
    });
}

export interface ExportPayrollDto extends Omit<TempPayrollDto, "relatedInvoiceLines" | "links"> {}
export function payrollToExport(data: TempPayrollDto[]): ExportPayrollDto[] {
    return data.map(r => {
        return (({relatedInvoiceLines, ...rest}) => rest)(r) as ExportPayrollDto;
    });
}

/*function groupInvoiceLineByResource(data: InvoiceLineDto[]) {
    return data.reduce((acc: StringToObjectMap<InvoiceLineDto[]>, current: InvoiceLineDto) => {
        if (!acc[current.plannedResourceCalendarId || ""]) {
            acc[current.plannedResourceCalendarId || ""] = [];
        }
        acc[current.plannedResourceCalendarId || ""].push(current);
        return acc;
    }, {});
}*/

// given some evidence data, generate the line items and invoice.
function calculatePayrollItem(invoiceData: InvoiceLineDto[]): TempPayrollDto {
    // COPIED from InvoicesList
    const totalActualMins = invoiceData
        ? invoiceData.reduce((total, line) => total + (line.workMinutesSpent || 0), 0)
        : null;
    const totalPlannedMins = invoiceData
        ? invoiceData.reduce((total, line) => total + (line.plannedMinutes || 0), 0)
        : null;

    // since we are grouped by resource, just find the first name
    const workerName = invoiceData?.length > 0 ? invoiceData[0]!.plannedResourceName : "";
    return {
        payrollId: 0,
        paid: false,
        status: "draft",
        name: workerName || "",
        rateCardId: invoiceData[0]!.rateCardId, // each invoice is grouped in getInvoiceRows by rateCardId, so just pick the first
        rateCardName: (invoiceData[0]!.invoice as TempInvoiceDto)?.rateCardName,
        totalActualMins: totalActualMins || 0,
        totalPlannedMins: totalPlannedMins || 0,
        amount: 0, // demo data did calculate this based on rate card etc, which should be server side (see commit)
        relatedInvoiceLines: invoiceData
    };
}

export interface TempInvoiceDto extends InvoiceDto {
    /**
     * Populated client-side as additional information
     * See InvoiceList 'invoice.client'
     */
    client: string;

    /**
     * Calculated client-side by grouping the lines by rateCardId.
     *
     * FIXME this is kind of contrary to the InvoiceDto from the 'amount' in InvoiceDto
     *  in that totalValue is a row per rateCardId on the client side but the 'amount' is
     *  from the server for the entire invoice
     */
    rateCardId: number;
    rateCardName: string;

    totalActualMins: number;
    totalPlannedMins: number;
}

export interface TempInvoiceLineDto extends InvoiceLineDto {
    reverseCharge: boolean;
    verified: boolean;
}

// represents the server side object - which is nothing!
export interface PayrollItemDto {
}
export interface TempPayrollDto extends PayrollItemDto {
    payrollId: number;
    paid: boolean;
    status: "draft" | "final";
    rateCardId: number;
    rateCardName: string;
    name: string;
    totalActualMins: number;
    totalPlannedMins: number;
    amount: number; // matches the name on InvoiceDto
    relatedInvoiceLines: InvoiceLineDto[];
}


// **************************
// ***** DEMO DATA / FN *****
// **************************

// Entirely contrieved for demoware. An enum to represent possible rates
enum Rates {
    Standard = 1,
    Double = 2
}

// Entirely contrieved for demoware. Data generated from attach screenshot to Jira ticket DEV-1257
const data = [
    {
        workUuid: '1bd6a425-1000-44f2-615d-c5808d25a0e4',
        createdDate: new Date(1571318304038),
        eventPlannedDate: new Date(1571318304038),
        workDate: new Date(1571318304038),
        rId: 10000,
        client: 'client 1',
        worker: 'worker 1',
        eventPlannedTime: 30,
        time: 30,
        minsTravel: 30,
        mileageTo: 3,
        mileageDuring: 3,
        signatureId: '123',
        task: 'rota visit',
        type: '-',
        locationId: 12,
        eventId: 'get up',
        authorDisplayName: 'Author',
        rateName: Rates.Standard,
        invoiceId: '123',
        verified: true,
        reverseCharge: false
    },
    {
        workUuid: '6bd6a425-1000-44f2-915d-c5808d25a0e4',
        createdDate: new Date(1571318304038),
        eventPlannedDate: new Date(1571318304038),
        workDate: new Date(1571318304038),
        rId: 10001,
        client: 'client 1',
        worker: 'worker 2',
        eventPlannedTime: 30,
        time: 30,
        minsTravel: 30,
        mileageTo: 3,
        mileageDuring: 3,
        signatureId: '123',
        task: 'rota visit',
        type: '-',
        locationId: 12,
        eventId: 'get up',
        authorDisplayName: 'Author',
        rateName: Rates.Standard,
        invoiceId: null,
        verified: true,
        reverseCharge: false
    },
    {
        workUuid: '5bd6a425-1000-44f2-915d-c5808d25a0e4',
        createdDate: new Date(1571318304038),
        eventPlannedDate: new Date(1571318304038),
        workDate: new Date(1571318304038),
        rId: 10002,
        client: 'client 2',
        worker: 'worker 1',
        eventPlannedTime: 30,
        time: 30,
        minsTravel: 30,
        mileageTo: 3,
        mileageDuring: 3,
        signatureId: '123',
        task: 'rota visit',
        type: '-',
        locationId: 12,
        eventId: 'get up',
        authorDisplayName: 'Author',
        rateName: Rates.Standard,
        invoiceId: null,
        verified: false,
        reverseCharge: false
    },
    {
        workUuid: '2ba6a425-1000-44f2-915d-c5808d25a0e4',
        createdDate: new Date(1571318304038),
        eventPlannedDate: new Date(1571318304038),
        workDate: new Date(1571318304038),
        rId: 10003,
        client: 'client 1',
        worker: 'worker 3',
        eventPlannedTime: 30,
        time: 30,
        minsTravel: 30,
        mileageTo: 3,
        mileageDuring: 3,
        signatureId: '123',
        task: 'rota visit',
        type: '-',
        locationId: 'location id',
        eventId: 'get up',
        authorDisplayName: 'Author',
        rateName: Rates.Standard,
        invoiceId: null,
        verified: false,
        reverseCharge: false
    }
];

// Grab what you can from the evidence.
// If nothing is found in local storage, it will pull a fresh copy from data variable above.
export function getDemoEvidenceData() {
    const evidence = localStorage.getItem("evidence");
    if (evidence === null) {
        localStorage.setItem("evidence", JSON.stringify(data));
        generateDemoInvoiceData(data);
        generateDemoPayrollData(data);
    }
    const results = evidence ? JSON.parse(evidence) : data;
    return Promise.resolve(results);
}

// take the evidence data, and generate invoice data, then store into local storage.
export function generateDemoInvoiceData(evidenceData: any) {
    const groupedData = evidenceData.reduce((acc: any, current: InvoiceLineDto) => {
        if (!acc[current.serviceRecipientId]) {
            acc[current.serviceRecipientId] = [];
        }
        acc[current.serviceRecipientId].push(current);
        return acc;
    }, []);
    const invoiceData = Object.keys(groupedData).map(serviceRecipientId =>
        calculateDemoInvoiceItem(groupedData[serviceRecipientId], serviceRecipientId)
    );
    localStorage.setItem("invoiceData", JSON.stringify(invoiceData));
}

// take the evidence data, and generate payroll data, then store into local storage.
export function generateDemoPayrollData(evidenceData: any) {
    const groupedData = evidenceData.reduce((acc: any, current: any) => {
        if (!acc[current.worker]) {
            acc[current.worker] = [];
        }
        acc[current.worker].push(current);
        return acc;
    }, []);
    const payrollData = Object.keys(groupedData).map(group =>
        calculatePayrollItem(groupedData[group])
    );
    localStorage.setItem("payrollData", JSON.stringify(payrollData));
}

// given some evidence data, generate the line items and invoice. // TODO: Review web API scenarios around invoicing and match those
export function calculateDemoInvoiceItem(
    invoiceData: InvoiceLineDto[],
    invoiceId: string
): TempInvoiceDto {
    // sorry about the reduce.
    const totals = invoiceData.reduce<{totalMins: number; totalValue: number}>(
        (acc: any, current: any) => {
            if (current.verified && !current.reverseCharge) {
                acc.totalMins = acc.totalMins + current.time;
                acc.totalValue = 0;
                //acc.totalValue = acc.totalValue + shiftInHours * (50 * current.rateName);
            }
            return acc;
        },
        {totalMins: 0, totalValue: 0}
    );
    return {
        invoiceId: invoiceId, // faked id.  we'll want a real one from the server
        posted: false,
        status: InvoiceStatus.DRAFT,
        name: invoiceId.toString(), // FIXME: obviously wrong
        client: invoiceId.toString(),
        totalMins: totals.totalMins,
        totalValue: totals.totalValue,
        lines: invoiceData
    } as unknown as TempInvoiceDto;
}

// Simply take evidence data and throw it into local storage.
export function updateDemoEvidenceData(evidenceData: any) {
    localStorage.setItem('evidence', JSON.stringify(evidenceData))
}

// take some invoice and persist to local storage
export function updateDemoInvoiceData(invoiceData: any) {
    localStorage.setItem("invoiceData", JSON.stringify(invoiceData));
}

// take some payroll data and persist to local storage
export function updateDemoPayrollData(payrollData: any) {
    localStorage.setItem("payrollData", JSON.stringify(payrollData));
}

// get the invoice data from local storage
export function getDemoInvoiceData(): Promise<TempInvoiceLineDto[]> {
    const invoiceData = localStorage.getItem("invoiceData") || "";
    return Promise.resolve(JSON.parse(invoiceData));
}

// get the invoice data from local storage
export function getDemoPayrollData(): Promise<TempPayrollDto[]> {
    const payrollData = localStorage.getItem("payrollData") || "";
    return Promise.resolve(JSON.parse(payrollData));
}
