import {EccoDate} from "@eccosolutions/ecco-common";

import {
    Button,
    createStyles,
    green,
    Grid,
    IconButton,
    makeStyles,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TablePagination,
    TableRow,
    Theme
} from "@eccosolutions/ecco-mui";
import {DateRangePickerMoment} from "@eccosolutions/ecco-mui-controls";
import ExpandMore from "@material-ui/icons/ExpandMore";
import {findIndexFn} from "@softwareventures/array";
import {InvoiceDto, InvoiceLineDto, InvoiceStatus, ReferralSummaryDto} from "ecco-dto";
import moment = require("moment");
import {Moment} from "moment";
import * as React from "react";
import {ChangeEvent, useEffect, useState} from "react";
import {useServicesContext} from "../../ServicesContext";
import {
    calculateDemoInvoiceItem,
    getInvoiceRows,
    invoiceToExport,
    TempInvoiceDto,
    TempInvoiceLineDto,
    updateDemoInvoiceData
} from "../service/dataservice";
import AlertDialog from "./AlertDialog";
import FeedbackMessage from "./FeedbackMessage";
import NestedInvoiceTable from "./NestedInvoiceTable";
import {useAppBarOptions} from "../../AppBarBase";
import {LoadingSpinner} from "../../Loading";
import {ExportExcel} from "../../table/ExportExcel";

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        dateFilterElement: {
            marginLeft: 20,
            marginTop: 20
        },
        button: {
            marginLeft: 20,
            marginTop: 10,
            marginRight: 20
        },
        table: {
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        },
        nestedRow: {
            backgroundColor: "#eee"
        },
        verifiedRow: {
            backgroundColor: green[200]
        }
    })
);

function InvoicesTableRow(props: {
    invoice: TempInvoiceDto;
    checked: boolean;
    onChange: () => boolean;
    onToggleFinalise: () => Promise<void>;
    onClickExpander: () => void;
    expanded: boolean;
    update: (invoice: TempInvoiceDto) => void;
    reverseLineItem: (lineItem: TempInvoiceLineDto, invoiceId: number) => Promise<void>;
}) {
    const {
        onClickExpander,
        expanded,
        reverseLineItem,
        invoice
    } = props;
    const classes = useStyles();

    const [referral, setReferral] = useState<ReferralSummaryDto | null>(null);
    const services = useServicesContext();

    function getRelatedData(invoice: InvoiceDto) {
        return services
            .referralRepository()
            .findOneReferralSummaryByServiceRecipientIdUsingDto(invoice.serviceRecipientId)
            .then(referral => setReferral(referral));
    }

    useEffect(() => {
        getRelatedData(invoice);
    }, [invoice.serviceRecipientId]);

    useEffect(() => {
        if (referral) {
            invoice.client = referral.clientDisplayName;
            props.update(invoice);
        }
    }, [referral]);

    return (
        <>
            <TableRow
                className={invoice.status === InvoiceStatus.POSTED ? classes.verifiedRow : ""}
            >
                <TableCell padding="checkbox" size="small">
                    {/*<Checkbox
                        checked={checked}
                        disabled={invoice.status === InvoiceStatus.POSTED}
                        onChange={onChange}
                    />*/}
                </TableCell>
                <TableCell>{invoice.invoiceId}</TableCell>
                <TableCell>{invoice.client}</TableCell>
                <TableCell>{invoice.totalPlannedMins ? invoice.totalPlannedMins : "-"}</TableCell>
                <TableCell>{invoice.totalActualMins ? invoice.totalActualMins : "-"}</TableCell>
                <TableCell>£{invoice.amount}</TableCell>
                <TableCell>{invoice.rateCardName}</TableCell>
                <TableCell>{invoice.status.toString()}</TableCell>
                <TableCell>{invoice.status === InvoiceStatus.POSTED ? "yes" : "no"}</TableCell>
                {/*<TableCell>
                     comment out 'finalise' or 'move to draft'
                    <Button
                    variant="contained"
                    color={invoice.status === InvoiceStatus.DRAFT ? "primary" : "secondary"}
                    onClick={onToggleFinalise}
                    disabled={invoice.status == InvoiceStatus.POSTED}
                >
                    {invoice.status === InvoiceStatus.DRAFT ? "finalise" : "move to draft"}
                </Button>
                </TableCell>*/}
                <TableCell>
                    <IconButton onClick={onClickExpander}>
                        <ExpandMore />
                    </IconButton>
                </TableCell>
            </TableRow>
            {expanded && (
                <TableRow className={classes.nestedRow}>
                    <TableCell colSpan={12}>
                        <NestedInvoiceTable
                            lines={invoice.lines || []}
                            reverseLineItem={reverseLineItem}
                        />
                    </TableCell>
                </TableRow>
            )}
        </>
    );
}

function InvoicesList() {
    const classes = useStyles();
    const [loading, setLoading] = useState<boolean>(false);
    const [loadAvailable, setLoadAvailable] = useState<boolean>(false);
    const [rows, setRows] = useState<TempInvoiceDto[]>([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(50);
    const [selectedInvoices, setSelectedInvoices] = useState<InvoiceDto[]>([]);
    const [feedbackMessage, setFeedbackMessage] = useState<String | null>(null);
    const [startDate, setStartDate] = useState<Moment | null>(moment().startOf("month"));
    // separate start date to be sure of the date for the export (ie the user doens't change it)
    const [startDateData, setStartDateData] = useState<string | null>(null);
    const [expandedRows, setExpandedRows] = useState<any>([]);
    const [endDate, setEndDate] = useState<Moment | null>(moment().endOf("month"));
    const [showPostedDialog, setShowPostedDialog] = useState(false);

    const services = useServicesContext();
    useAppBarOptions("manage invoices");

    // page has been changed. This may need to be a remote call in the real version
    function handleChangePage(_event: React.MouseEvent<HTMLButtonElement> | null, p: number) {
        setPage(p);
    }

    // number of items per page has changed. This may need to be a remote call in the real version
    function handleChangeRowsPerPage(event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) {
        setRowsPerPage(parseInt(event.target.value));
        setPage(0);
    }

    function getInvoices(startDate: Moment, endDate: Moment): Promise<void> {
        const start = EccoDate.fromMoment(startDate);
        const end = EccoDate.fromMoment(endDate);
        setLoading(true);
        return services.contractRepository.findAllRateCards().then(rates => {
            return services.invoicesRepository.findAllInvoices(start, end).then(data => {
                setRows(getInvoiceRows(rates, data));
                setLoading(false);
                setStartDateData(startDate.format("YYYY-MM-DD"));
            });
        });
    }

    function getDataByDate(): Promise<void> {
        if (startDate && endDate) {
            return getInvoices(startDate, endDate);
        }
        return Promise.resolve();
    }

    function handleExpand(index: number): void {
        const foundIndex = expandedRows.indexOf(index);
        if (foundIndex === -1) {
            setExpandedRows([...expandedRows, index]);
            return;
        }
        setExpandedRows([
            ...expandedRows.slice(0, foundIndex),
            ...expandedRows.slice(foundIndex + 1, expandedRows.length)
        ]);
    }

    function isExpanded(index: number): boolean {
        const foundIndex = expandedRows.indexOf(index);
        return foundIndex > -1;
    }

    function changeDemoInvoiceStatus(invoiceId: any) {
        const foundIndex = findIndexFn((r: any) => r.invoiceId === invoiceId)(rows)!!;
        const invoice: TempInvoiceDto = {...rows[foundIndex]!};
        invoice.status =
            invoice.status === InvoiceStatus.FINAL ? InvoiceStatus.DRAFT : InvoiceStatus.FINAL;
        setRows([
            ...rows.slice(0, foundIndex),
            invoice,
            ...rows.slice(foundIndex + 1, rows.length)
        ]);
        updateDemoInvoiceData(rows);
        return Promise.resolve();
    }

    function getCurrentPage() {
        return rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    }

    function demoPostSelectedInvoices() {
        const invoicesToProcess = [...rows];
        // no selected items, please bail
        if (selectedInvoices.length === 0) {
            return;
        }
        selectedInvoices.forEach((s: any) => {
            const index = findIndexFn((r: any) => r.invoiceId === s.invoiceId)(invoicesToProcess);
            invoicesToProcess[index!]!.status === InvoiceStatus.POSTED;
        });

        // simulate a backend update.
        updateDemoInvoiceData(invoicesToProcess);
        setFeedbackMessage("Invoices have been marked as posted");
        setSelectedInvoices([]);
        setShowPostedDialog(false);
    }

    /*function openShowDialog() {
        setShowPostedDialog(true);
    }*/

    // function selectableInvoicesForPosting(): InvoiceDto[] {
    //     return rows.filter(r => r.status === InvoiceStatus.FINAL);
    // }

    // // handles a select all checkbox action
    // function onSelectedAll(_event: ChangeEvent<HTMLInputElement>, checked: boolean) {
    //     // if checks, grab the relevant subset and add to selected items
    //     if (checked) {
    //         setSelectedInvoices(selectableInvoicesForPosting());
    //         return;
    //     }
    //     // has been unchecked so empty it.
    //     setSelectedInvoices([]);
    // }

    // handles single selection
    function onSelectSingle(invoice: InvoiceDto) {
        // this shouldn't happen so get out of here is status is not final
        if (invoice.status !== InvoiceStatus.FINAL) {
            return true;
        }
        // find if the item is already selected or not.
        const index = findIndexFn((si: any) => si.invoiceId === invoice.invoiceId)(
            selectedInvoices
        );
        // not selected so simply push and return
        if (index === -1 || index === null) {
            setSelectedInvoices([...selectedInvoices, invoice]);
            return true;
        }
        // destructure... sorry. Basically, reshuffle the array and remove the found index
        setSelectedInvoices([
            ...selectedInvoices.slice(0, index),
            ...selectedInvoices.slice(index + 1, selectedInvoices.length)
        ]);
        return true;
    }

    function updateInvoice(invoice: TempInvoiceDto) {
        const foundIndex = rows.findIndex(r => r.invoiceId === invoice.invoiceId);

        const newRows = [
            ...rows.slice(0, foundIndex),
            invoice,
            ...rows.slice(foundIndex + 1, rows.length)
        ];
        setRows(newRows);
    }

    // reverses the charge of a given line item, it is assumed the item exists.
    function demoReverseLineItem(lineItem: TempInvoiceLineDto, invoiceId: number) {
        const foundIndex = rows.findIndex(r => r.invoiceId === invoiceId);
        const invoice = rows[foundIndex]!;
        const lineItemIndex = findIndexFn((d: InvoiceLineDto) => d.workUuid === lineItem.workUuid)(
            invoice.lines!!
        )!!;
        (invoice.lines!![lineItemIndex] as any).reverseCharge = !(
            invoice.lines!![lineItemIndex] as any
        ).reverseCharge;
        // destructure... sorry. remove the item from the array and insert a modified invoice in its place
        const newRows = [
            ...rows.slice(0, foundIndex),
            calculateDemoInvoiceItem(invoice.lines!!, invoiceId.toString()),
            ...rows.slice(foundIndex + 1, rows.length)
        ];
        setRows(newRows);
        updateDemoInvoiceData(newRows);
        return Promise.resolve();
    }

    // check if an item is selected
    function isSelected(invoice: InvoiceDto) {
        return selectedInvoices.some((s: InvoiceDto) => s.invoiceId === invoice.invoiceId);
    }

    useEffect(() => {
        if (startDate && endDate && !loading) {
            setLoadAvailable(true);
        } else {
            setLoadAvailable(false);
        }
    }, [startDate, endDate, loading]);

    if (loading) {
        return (
            <Grid container={true} direction={"row"} justify={"center"} alignItems={"center"}>
                <LoadingSpinner />
            </Grid>
        );
    }

    return (
        <Grid container={true} direction={"row"} justify={"flex-start"}>
            <Grid item={true} lg={6} md={6} xs={12}>
                <Grid container={true}>
                    <Grid item={true} xs={8}>
                        <DateRangePickerMoment
                            classes={classes.dateFilterElement}
                            start={startDate}
                            onStartChange={setStartDate}
                            end={endDate}
                            onEndChange={setEndDate}
                        />
                    </Grid>
                    <Grid item={true} xs={12}>
                        <Button
                            className={classes.button}
                            variant="contained"
                            color="primary"
                            disabled={!loadAvailable}
                            onClick={getDataByDate}
                        >
                            load
                        </Button>
                    </Grid>
                </Grid>
            </Grid>

            <Grid item={true} xs={12}>
                <Grid container={true} direction="column" justify="flex-end" alignItems="flex-end">
                    {/*<Button
                        variant="contained"
                        color="primary"
                        disabled={selectedInvoices.length === 0}
                        className={classes.button}
                        onClick={openShowDialog}
                    >
                        {`post ${
                            selectedInvoices.length > 0 ? `(${selectedInvoices.length})` : ""
                        }`}
                    </Button>*/}
                    <Button
                        variant="contained"
                        color="primary"
                        disabled={rows.length == 0}
                        className={classes.button}
                    >
                        <ExportExcel
                            data={invoiceToExport(rows)}
                            fileName={`ecco-invoice_${startDateData}`}
                        />
                    </Button>
                </Grid>
            </Grid>

            <Grid item={true} xs={12}>
                <div className={classes.tableWrapper}>
                    <Table className={classes.table}>
                        <TableHead>
                            <TableRow>
                                <TableCell padding="checkbox" size="small">
                                    {/*<Checkbox
                                        indeterminate={
                                            selectedInvoices.length > 0 &&
                                            selectedInvoices.length <
                                                selectableInvoicesForPosting().length
                                        }
                                        checked={
                                            selectedInvoices.length > 0 &&
                                            selectedInvoices.length ===
                                                selectableInvoicesForPosting().length
                                        }
                                        onChange={onSelectedAll}
                                        disabled={selectableInvoicesForPosting().length === 0}
                                    />*/}
                                </TableCell>
                                <TableCell>invoice-id</TableCell>
                                <TableCell>client</TableCell>
                                <TableCell>total mins (p)</TableCell>
                                <TableCell>total mins (a)</TableCell>
                                <TableCell>total value</TableCell>
                                <TableCell>rate card</TableCell>
                                <TableCell>status</TableCell>
                                <TableCell>posted</TableCell>
                                {/*<TableCell>actions</TableCell>*/}
                                <TableCell></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {getCurrentPage().map((row, index: number) => (
                                <InvoicesTableRow
                                    key={row.invoiceId}
                                    invoice={row}
                                    update={i => updateInvoice(i)}
                                    checked={isSelected(row)}
                                    onChange={() => onSelectSingle(row)}
                                    onToggleFinalise={() => changeDemoInvoiceStatus(row.invoiceId)}
                                    onClickExpander={() => handleExpand(index)}
                                    expanded={isExpanded(index)}
                                    reverseLineItem={demoReverseLineItem}
                                />
                            ))}
                        </TableBody>
                    </Table>
                    <TablePagination
                        rowsPerPageOptions={[10, 25, 50, 100]}
                        component="div"
                        count={rows.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        backIconButtonProps={{
                            "aria-label": "Previous Page"
                        }}
                        nextIconButtonProps={{
                            "aria-label": "Next Page"
                        }}
                        onChangePage={handleChangePage}
                        onChangeRowsPerPage={handleChangeRowsPerPage}
                    />
                </div>
                {feedbackMessage && (
                    <FeedbackMessage
                        onClose={() => setFeedbackMessage(null)}
                        feedbackMessage={feedbackMessage}
                        type="success"
                    />
                )}
                {showPostedDialog && (
                    <AlertDialog
                        onConfirm={demoPostSelectedInvoices}
                        title="Marking as posted"
                        content="Please be aware this action cannot be done"
                        cancelContent="cancel"
                        confirmContent="confirm"
                    />
                )}
            </Grid>
        </Grid>
    );
}
export default InvoicesList;
