import {EccoDate, EccoDateTime, Duration} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    Autocomplete,
    Button,
    Checkbox,
    createStyles,
    green,
    Grid,
    makeStyles,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TablePagination,
    TableRow,
    TextField,
    Theme
} from "@eccosolutions/ecco-mui";
import {DatePickerMoment} from "@eccosolutions/ecco-mui-controls";
import {
    CreateInvoiceCommand,
    CreateInvoiceLineCommand,
    sequentialMapAll,
    CommentCommands
} from "ecco-commands";
import {
    EventResourceDto,
    EvidenceGroup,
    InvoiceDto,
    InvoiceLineDto,
    InvoiceStatus,
    ReferralSummaryDto,
    SessionData,
    SupportWork
} from "ecco-dto";
import moment = require("moment");
import {Moment} from "moment";
import * as React from "react";
import {ChangeEvent, ReactElement, useEffect, useState} from "react";
import {useServicesContext} from "../../ServicesContext";

import EditEvidence, {EditEvidenceDto} from "./EditEvidence";
import {useAppBarOptions} from "../../AppBarBase";
import {EccoV3Modal} from "ecco-components-core";
import {CareVisitHistoryController} from "../../care/CareVisitHistory";
import {useBuildings} from "../../data/entityLoadHooks";
import {numberFromHtmlInput} from "ecco-components-core";
import {LoadingSpinner} from "../../Loading";
import {useDebounce} from "ecco-components-core";
import {Notifications} from "ecco-components-core";

function disableSelect(evidence: InvoiceLineDto) {
    return evidence.invoiceId != null || !evidence.workUuid; // stop verifying non-work (see commit msg)
}

const toFormatShort = EccoDateTime.iso8601ToFormatShort;

// planned: local-datetime
// actual: workDate - This is stored as zoneless time in database (so 7am BST is stored as 7am)
//      as below, "actual - the workdate uses the timer start - search changeWorkDate(null, state.timerStartedAt?.formatIso8601())"
//      which uses nowLocalTime in the careVisitReducer - local time as reported by the browser
export const relativeShort = (planned: string | undefined, actual: string) => {
    const diff = getDurationBetweenStarts(planned, actual);
    if (!diff) {
        const a = EccoDateTime.parseIso8601(actual!);
        return a.formatShort();
    }

    const a = EccoDateTime.parseIso8601(actual);
    const diffSign = diff.inMinutes() == 0 ? 0 : diff.inMinutes() < 0 ? -1 : 1;
    const useRelative = diff.inHours() * diffSign < 12;
    if (useRelative) {
        const hrs = diff.getHours() * diffSign;
        const mins = diff.getMinutes() * diffSign;
        const outActual = `${a.toEccoTime().formatHoursMinutes()} `;
        const outSign = diffSign == 0 ? "" : diffSign == -1 ? " early" : " late";
        const outHrs = hrs > 0 ? `${hrs}hr ` : "";
        return `${outActual} (${outHrs}${mins}m${outSign})`;
    } else {
        return a.formatShort();
    }
};

function getDurationBetweenStarts(
    planned: string | undefined,
    actual: string | undefined
): Duration | null {
    if (!planned || !actual) {
        return null;
    }
    const p = EccoDateTime.parseIso8601(planned);
    const a = EccoDateTime.parseIso8601(actual);
    return a.subtractDateTime(p, true);
}

export const MainRows = (props: {
    line: InvoiceLineDto;
    thresholdMins: number;
    noColourCode: boolean;
    referral: ReferralSummaryDto | null;
    calendarEvent: EventResourceDto | null;
    plannedResource: string | undefined;
    evidence: SupportWork | null;
    sessionData: SessionData;
}) => {
    const {line, noColourCode, thresholdMins, referral, calendarEvent, plannedResource, evidence} =
        props;

    let minsSpentDiff: number | null = null;
    let minsSpentSign: number = 1;
    let minsSpentCode = "";
    if (
        (line.plannedMinutes || line.plannedMinutes === 0) &&
        (line.workMinutesSpent || line.workMinutesSpent === 0)
    ) {
        minsSpentDiff = line.plannedMinutes - line.workMinutesSpent;
        minsSpentSign = minsSpentDiff > 0 ? 1 : -1;
        // thresholdMins is either side of the visit
        minsSpentCode =
            line.workMinutesSpent <= 2
                ? colourTried
                : minsSpentDiff * minsSpentSign <= thresholdMins
                ? colourVerified
                : colourWarn;
    } else {
        minsSpentCode = colourMissed;
    }
    if (noColourCode) {
        minsSpentCode = "";
    }

    const hasRecoded = line.workMinutesSpent || line.workMinutesSpent === 0;

    return (
        <>
            <TableCell>
                {referral?.clientDisplayName}
                <br />
                <small>{referral?.referralId}</small>
            </TableCell>
            <TableCell>{calendarEvent?.title}</TableCell>
            <TableCell>
                {/*planned*/}
                {calendarEvent && `${toFormatShort(calendarEvent?.start!)} with ${plannedResource}`}
            </TableCell>
            <TableCell>
                {/*actual - the workdate uses the timer start - search changeWorkDate(null, state.timerStartedAt?.formatIso8601()) */}
                {evidence &&
                    `${relativeShort(calendarEvent?.start, evidence.workDate)} with ${
                        evidence?.authorDisplayName
                    }`}
            </TableCell>
            <TableCell style={{backgroundColor: minsSpentCode}}>
                {/*p / a*/}
                {`${line.plannedMinutes} / ${hasRecoded ? line.workMinutesSpent : "none"} `}
                {minsSpentDiff != null
                    ? `(${minsSpentDiff * minsSpentSign} mins ${
                          minsSpentDiff == 0 ? "" : minsSpentSign == -1 ? "over" : "under"
                      })`
                    : null}
            </TableCell>
            <TableCell>
                {/*reason*/}
                {evidence &&
                    calendarEvent?.eventStatusId &&
                    `${props.sessionData
                        .getListDefinitionEntryById(calendarEvent.eventStatusId)
                        .getDisplayName()}`}
            </TableCell>
            <TableCell>{evidence?.comment}</TableCell>
        </>
    );
};

/**
 * loadTimestamp is the epoch millis at which we last loaded the parent view - such that a reload updates all data
 */
function EvidenceVerificationRow(props: {
    line: InvoiceLineDto;
    classes: any;
    checked: boolean;
    onChange: () => boolean;
    onClickEdit: (
        line: InvoiceLineDto,
        workEvidence: SupportWork | null,
        calendarEvent: EventResourceDto | null
    ) => void;
    onClickHistory: (serviceRecipientId: number) => void;
    sessionData: SessionData;
    loadTimestamp: number | null;
    thresholdMins: number;
}) {
    const [evidence, setWorkEvidence] = useState<SupportWork | null>(null);
    const [calendarEvent, setCalendarEvent] = useState<EventResourceDto | null>(null);
    const [referral, setReferral] = useState<ReferralSummaryDto | null>(null);

    const services = useServicesContext();

    const {line, onClickHistory, thresholdMins, loadTimestamp} = props;

    function loadEventData(eventId: string) {
        return services.calendarRepository
            .fetchEventsById([eventId])
            .then(events => setCalendarEvent(events[0]!));
    }

    function getRelatedData(line: InvoiceLineDto) {
        const getWork = line.workUuid
            ? services.supportWorkRepository
                  .findOneSupportWorkByWorkUuid(
                      line.serviceRecipientId,
                      EvidenceGroup.needs,
                      line.workUuid
                  )
                  .then(work => {
                      setWorkEvidence(work);
                  })
            : Promise.resolve();
        return getWork
            .then(() => {
                if (line.eventId) {
                    loadEventData(line.eventId);
                }
            })
            .then(() =>
                services
                    .referralRepository()
                    .findOneReferralSummaryByServiceRecipientIdUsingDto(line.serviceRecipientId)
            )
            .then(referral => setReferral(referral));
    }

    useEffect(() => {
        getRelatedData(line);
    }, [line.workUuid, evidence?.eventId, loadTimestamp]);

    return (
        <TableRow className={line.invoiceId != null ? props.classes.verifiedRow : ""}>
            <TableCell padding="checkbox" size="small">
                <Checkbox
                    checked={props.checked}
                    disabled={disableSelect(line)}
                    onChange={props.onChange}
                />
            </TableCell>
            <TableCell component="th" scope="row" padding="none">
                {(line.workUuid ? `w:${line.workUuid}` : `e:${line.eventId!}`).substr(0, 8) + "..."}
            </TableCell>
            <MainRows
                line={line}
                noColourCode={false}
                thresholdMins={thresholdMins}
                evidence={evidence}
                calendarEvent={calendarEvent}
                plannedResource={line.plannedResourceName}
                referral={referral}
                sessionData={props.sessionData}
            />
            {/*TODO: Feature toggle this as summary <TableCell>{evidence?.minsTravel}</TableCell>*/}
            {/*<TableCell>{evidence?.mileageTo}</TableCell>*/}
            {/*<TableCell>{evidence?.mileageDuring}</TableCell>*/}
            {/*{pageHasWork && <TableCell>{evidence?.signatureId ? "Yes" : "No"}</TableCell>}*/}
            {/*{pageHasWork && <TableCell>{"rota visit"}</TableCell>}*/}
            {/*<TableCell>{line.type}</TableCell>*/}
            {/*{pageHasWork && <TableCell>{evidence?.locationId}</TableCell>}*/}
            {/*<TableCell>{line.invoiceId}</TableCell>*/}
            <TableCell>
                {/* TODO RE-INTRODUCE TO EDIT EVIDENCE */}
                {/*<Button
                    color="secondary"
                    disabled={disableEdit(line)}
                    onClick={() => onClickEdit(line, evidence, calendarEvent)}
                >
                    edit
                </Button>*/}
                <Button color="secondary" onClick={() => onClickHistory(line.serviceRecipientId)}>
                    history
                </Button>
            </TableCell>
        </TableRow>
    );
}

function isSameLine(r: InvoiceLineDto, s: InvoiceLineDto) {
    return r.workUuid === s.workUuid && r.eventId === s.eventId;
}

const SelectThreshold = (props: {
    threshold: number;
    change: (threshold: number | null) => void;
}) => (
    <>
        <TextField
            name={`threshold`}
            label={"p/a (mins)"}
            style={{width: "75px"}}
            type={"number"}
            onChange={event => props.change(numberFromHtmlInput(event.target, 0))}
            value={props.threshold}
        />
    </>
);

function EvidenceView() {
    const classes = useStyles();
    const [rows, setRows] = useState<InvoiceLineDto[]>([]);
    const [page, setPage] = useState(0);
    const delayedPage = useDebounce(page, 750); // NOTE: must not be a fresh object each render else re-renders repeatedly
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [editingEvidence, setEditingEvidence] = useState<ReactElement | null>(null);
    const [selectedItems, setSelectedItems] = useState<InvoiceLineDto[]>([]);
    const reportingDurationDays = 0;
    const [startDate, setStartDate] = useState<Moment | null>(moment());
    const [loadedTimestamp, setLoadedTimestamp] = useState<number | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [showHistoryForSrId, setShowHistoryForSrId] = useState<number | null>(null);
    const [loadAvailable, setLoadAvailable] = useState<boolean>(false);
    const [buildingId, setBuildingId] = useState<number | null>(null);

    const endDate = startDate && moment(startDate).add(reportingDurationDays, "days");

    const initialThreshold = 5;
    const [selectThresholdMins, setSelectThresholdMins] = useState<number>(initialThreshold);

    const services = useServicesContext();
    useAppBarOptions("visit verification and billing");

    const wholeRota = services.sessionData.isEnabled("rota.whole-org.enable");

    function getOrCreateDraftInvoiceForItem(line: InvoiceLineDto): Promise<InvoiceDto> {
        return services.invoicesRepository
            .findAllInvoicesByServiceRecipientId(line.serviceRecipientId)
            .then(invoices => {
                // Select earliest draft
                const workDate = line.workDate || line.plannedDate!;
                const matches = invoices
                    .filter(
                        // keep invoices where the invoice date is later
                        invoice =>
                            invoice.status == InvoiceStatus.DRAFT &&
                            EccoDate.parseIso8601FromDateTime(workDate).earlierThanOrEqual(
                                EccoDate.parseIso8601(invoice.invoiceDate)!
                            )
                    )
                    .sort((a, b) => a.invoiceDate.localeCompare(b.invoiceDate));

                if (matches.length > 0) {
                    return matches[0]!;
                }
                const monthEnd = EccoDate.fromMoment(moment().endOf("month"));
                const newInvoiceCmd = new CreateInvoiceCommand(
                    "add",
                    line.serviceRecipientId
                ).changeInvoiceDate(null, monthEnd);
                return services.getCommandRepository().exchangeCommand<InvoiceDto>(newInvoiceCmd);
            });
    }

    // page has been changed. This may need to be a remote call in the real version
    function handleChangePage(_event: React.MouseEvent<HTMLButtonElement> | null, p: number) {
        setPage(p);
    }

    // number of items per page has changed. This may need to be a remote call in the real version
    function handleChangeRowsPerPage(event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) {
        setRowsPerPage(parseInt(event.target.value));
        setPage(0);
    }

    // takes a piece of evidence to update, and updates local storage
    function updateEvidence(line: InvoiceLineDto, form: EditEvidenceDto): Promise<void> {
        const builder = line.workUuid
            ? CommentCommands.create(
                  true,
                  Uuid.parse(line.workUuid),
                  line.serviceRecipientId,
                  EvidenceGroup.needs,
                  "verifyWork"
              )
            : CommentCommands.create(
                  false,
                  Uuid.randomV4(),
                  line.serviceRecipientId,
                  EvidenceGroup.needs,
                  "verifyWork"
              ).changeEventId(null, line.eventId!);
        const cmd = builder.changeEventStatusRateId(null, form.eventsStatusRateId!).build();
        return services
            .getCommandRepository()
            .sendCommand(cmd)
            .then(() => {
                Notifications.add("update", "evidence has been updated");

                // TODO: sort out stuff below - I think we should call verify above, and remove stuff below,
                //   but for now we perhaps need to include the rate in InvoiceLineDto from the server .. or reload the work
                // const predicate = (r: InvoiceLineDto) => r.workUuid === line.workUuid;
                // const newRows = replaceItemBy(rows, predicate, line);
                // setRows(newRows);
                getDataByDate();
            });
    }

    // returns a subset of evidence which are selectable.
    function selectableRows() {
        return rows.filter((r: InvoiceLineDto) => r.invoiceId === null && r.workUuid); // stop verifying non-work (see commit msg)
    }

    function onSelectedAll(event: ChangeEvent<HTMLInputElement>, checked: boolean) {
        if (checked) {
            setSelectedItems(selectableRows());
            return;
        }
        setSelectedItems([]);
    }

    function onSelectSingle(evidence: InvoiceLineDto) {
        if (evidence.invoiceId) {
            // So doesn't toggle if invoiceId is set - can verify, but have to use button to unverify
            return true;
        }
        const index = selectedItems.findIndex((si: InvoiceLineDto) => isSameLine(si, evidence));
        if (index === -1) {
            setSelectedItems([...selectedItems, evidence]);
            return true;
        }
        setSelectedItems([
            ...selectedItems.slice(0, index),
            ...selectedItems.slice(index + 1, selectedItems.length)
        ]);
        return true;
    }

    /** Ensure there is an invoice for this line item, but reject if there are pending invoices that pre-date this item */
    function getOrCreateDraftInvoice(lineItem: InvoiceLineDto): Promise<InvoiceDto> {
        return getOrCreateDraftInvoiceForItem(lineItem);
    }

    /** Add the specified line line to the invoice */
    function addLineItemToInvoice(line: InvoiceLineDto, invoice: InvoiceDto): Promise<any> {
        // TODO: Also create work entry via updateEvidence if workUuid is null
        const cmd = new CreateInvoiceLineCommand("add", invoice.invoiceId)
            .changeWorkUuid(null, line.workUuid!)
            .changeEventId(null, line.eventId!)
            // hand back to the server what was given
            // not ideal but simpler for now - see RotaActivityInvoiceController#addLineToInvoice
            .changePlannedResourceCalendarId(null, line.plannedResourceCalendarId!)
            .changeRateCardId(null, line.rateCardId)
            .changeAmount(null, line.netAmount)
            .changeTaxRate(null, line.taxRate)
            .changeDescription(null, line.description); // Note: planned,actual mins/date are populated on back end from work/event
        return services.getCommandRepository().sendCommand(cmd);
    }

    const findInvoiceAndAdd = (s: InvoiceLineDto): Promise<void> => {
        const index = rows.findIndex(r => isSameLine(r, s));
        return index != -1
            ? getOrCreateDraftInvoice(s).then(invoice => addLineItemToInvoice(s, invoice))
            : Promise.resolve();
    };

    function selectThresholdItems() {
        const newSelected = selectableRows().filter(r => {
            const diff =
                r.plannedMinutes && r.workMinutesSpent
                    ? r.plannedMinutes - r.workMinutesSpent
                    : null;
            return diff == null ? false : diff <= selectThresholdMins;
        });
        setSelectedItems(newSelected);
    }

    function verifySelectedItems(): Promise<void> {
        // no selected items, please bail
        if (selectedItems.length === 0) {
            return Promise.resolve();
        }
        // Order by workDate so we apply them earliest first
        const sorted = selectedItems.sort((a, b) => {
            return a.workDate
                ? a.workDate.localeCompare(b.workDate!) // localeCompare(null) returns -1
                : a.plannedDate!.localeCompare(b.plannedDate!);
        });

        return sequentialMapAll(sorted, findInvoiceAndAdd).then(() => {
            const selectedItemsEventIds = selectedItems.map(i => i.eventId);
            const newRows = rows.filter(i => selectedItemsEventIds.indexOf(i.eventId) == -1);
            setRows(newRows);
            Notifications.add("verified", "evidence has been verified");
            setSelectedItems([]);
        });
    }

    function getUninvoicedLines(
        startDate: Moment,
        endDate: Moment,
        buildingId: number | null
    ): Promise<void> {
        const start = EccoDate.fromMoment(startDate);
        const end = EccoDate.fromMoment(endDate);
        return services!.invoicesRepository
            .findAllUninvoicedLines(start, end, buildingId)
            .then(data => {
                setRows(data);
                setLoaded();
            });
    }

    function setLoaded() {
        setLoading(false);
        setLoadedTimestamp(Date.now());
    }

    function getDataByDate() {
        setLoading(true);
        return getUninvoicedLines(startDate!, endDate!, buildingId).then(() => true);
    }

    useEffect(() => {
        if (startDate && (wholeRota || buildingId)) {
            setLoadAvailable(true);
        } else {
            setLoadAvailable(false);
        }
    }, [startDate, wholeRota, buildingId]);
    useEffect(() => {
        if (loadAvailable && loadedTimestamp) {
            setLoadAvailable(false);
        }
    }, [loadedTimestamp]);

    const CareHistoryModal = showHistoryForSrId ? (
        <EccoV3Modal
            title={""} /*InvoiceLineDto to include serviceRecipientName ?*/
            saveEnabled={false}
            show={true}
            children={<CareVisitHistoryController srId={showHistoryForSrId} />}
            action={"close"}
            onCancel={() => setShowHistoryForSrId(null)}
        />
    ) : null;

    const {buildings} = useBuildings();
    const currBld = buildings && buildings.filter(b => b.buildingId == buildingId)?.pop();
    const BuildingSelector = !wholeRota ? (
        <Autocomplete
            id={"eccotest-bldg"}
            renderInput={params => (
                // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                <TextField {...params} label={"building"} variant="outlined" required={true} />
            )}
            getOptionLabel={option => option!.name || ""}
            getOptionSelected={(a, b) => a!.buildingId == b?.buildingId}
            options={buildings || []}
            value={currBld}
            onChange={(_event, obj) => {
                setBuildingId(obj?.buildingId || null);
            }}
            /*style={{width: 270}}*/
        />
    ) : null;

    const currentPageRows = rows.slice(
        delayedPage * rowsPerPage,
        delayedPage * rowsPerPage + rowsPerPage
    );

    if (loading) {
        return (
            <Grid container={true} direction={"row"} justify={"center"} alignItems={"center"}>
                <LoadingSpinner />
            </Grid>
        );
    }

    return (
        <>
            {CareHistoryModal}
            <Grid container={true} direction={"row"} justify={"flex-start"}>
                <Grid item={true} lg={6} md={6} xs={12} className={classes.filterElement}>
                    <Grid container={true}>
                        <Grid item={true} xs={8}>
                            <DatePickerMoment value={startDate} onChange={setStartDate} />
                        </Grid>
                        <Grid item={true} xs={8}>
                            {BuildingSelector}
                        </Grid>
                        <Grid item={true} xs={12}>
                            <Button
                                variant="contained"
                                color="primary"
                                disabled={!loadAvailable}
                                onClick={getDataByDate}
                            >
                                load
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>

                <Grid item={true} xs={12}>
                    <Grid
                        container={true}
                        direction={"row"}
                        justify={"flex-end"}
                        alignItems={"center"}
                    >
                        <SelectThreshold
                            threshold={selectThresholdMins}
                            change={val => setSelectThresholdMins(val || initialThreshold)}
                        />
                        <Button
                            variant="contained"
                            color="primary"
                            disabled={!loadedTimestamp || rows.length == 0}
                            className={classes.button}
                            onClick={selectThresholdItems}
                        >
                            {`Select`}
                        </Button>
                        <Button
                            variant="contained"
                            color="primary"
                            disabled={selectedItems.length === 0}
                            className={classes.button}
                            onClick={verifySelectedItems}
                        >
                            {`Verify ${selectedItems.length || ""}`}
                        </Button>
                    </Grid>
                </Grid>

                <Grid item={true} xs={12}>
                    <div className={classes.tableWrapper}>
                        <Table className={classes.table}>
                            <TableHead>
                                <TableRow>
                                    <TableCell padding="checkbox" size="small">
                                        <Checkbox
                                            indeterminate={
                                                selectedItems.length > 0 &&
                                                selectedItems.length < selectableRows().length
                                            }
                                            checked={
                                                selectedItems.length > 0 &&
                                                selectedItems.length === selectableRows().length
                                            }
                                            onChange={onSelectedAll}
                                            disabled={selectableRows().length === 0}
                                        />
                                    </TableCell>
                                    <TableCell>id</TableCell>
                                    <TableCell>client</TableCell>
                                    <TableCell>title</TableCell>
                                    <TableCell>planned (p)</TableCell>
                                    <TableCell>actual (a)</TableCell>
                                    <TableCell>p / a (mins)</TableCell>
                                    <TableCell>reason</TableCell>
                                    <TableCell>comment</TableCell>
                                    {/*<TableCell>travel time (mins)</TableCell>*/}
                                    {/*<TableCell>mileage to/from</TableCell>*/}
                                    {/*<TableCell>mileage during visit</TableCell>*/}
                                    {/*{pageHasWork && <TableCell>signed</TableCell>}*/}
                                    {/*<TableCell>task</TableCell>*/}
                                    {/*{pageHasWork && <TableCell>type</TableCell>}*/}
                                    {/*{pageHasWork && <TableCell>location</TableCell>}*/}
                                    {/*<TableCell>rate</TableCell>*/}
                                    {/*<TableCell>invoice</TableCell>*/}
                                    <TableCell></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {currentPageRows.map((row: InvoiceLineDto) => {
                                    const isSelected =
                                        selectedItems.findIndex((s: InvoiceLineDto) =>
                                            isSameLine(s, row)
                                        ) > -1;
                                    return (
                                        <EvidenceVerificationRow
                                            key={row.workUuid || row.eventId!}
                                            line={row}
                                            classes={classes}
                                            checked={isSelected}
                                            onChange={() => onSelectSingle(row)}
                                            onClickHistory={srId => setShowHistoryForSrId(srId)}
                                            onClickEdit={(_line, _work, event) =>
                                                setEditingEvidence(
                                                    <EditEvidence
                                                        sessionData={services.sessionData}
                                                        evidence={{
                                                            eventsStatusRateId:
                                                                event?.eventStatusRateId
                                                        }}
                                                        onSuccess={form =>
                                                            updateEvidence(row, form)
                                                        }
                                                        saveText="verify"
                                                        onClose={() => setEditingEvidence(null)}
                                                    />
                                                )
                                            }
                                            sessionData={services.sessionData}
                                            thresholdMins={selectThresholdMins}
                                            loadTimestamp={loadedTimestamp}
                                        />
                                    );
                                })}
                            </TableBody>
                        </Table>
                        <TablePagination
                            rowsPerPageOptions={[10, 25, 50, 100]}
                            component="div"
                            count={rows.length}
                            rowsPerPage={rowsPerPage}
                            page={page}
                            backIconButtonProps={{
                                "aria-label": "Previous Page"
                            }}
                            nextIconButtonProps={{
                                "aria-label": "Next Page"
                            }}
                            onChangePage={handleChangePage}
                            onChangeRowsPerPage={handleChangeRowsPerPage}
                        />
                        {editingEvidence}
                    </div>
                </Grid>
            </Grid>
        </>
    );
}

const colourVerified = green[200];
const colourWarn = "#F0E68C";
// colourTried like missed (as nothing likely done), be reflects the person may not have been in
// perhaps we should use the actual event status code, but the threshold still warrants a different colour
const colourTried = "#B1344D";
const colourMissed = "#DC143C";

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        filterElement: {
            margin: 20
        },
        button: {
            margin: 20
        },
        table: {
            verticalAlign: "top",
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        },
        verifiedRow: {
            backgroundColor: colourVerified
        }
    })
);

export default EvidenceView

;
