
/**
 * Splices a replacement element into an array
 * @param items initial array of items
 * @param predicate matcher for the item to replace (first match)
 * @param replacement item to insert instead of matched element
 * @returns new array with replacement spliced in
 */
export function replaceItemBy<T>(items: T[], predicate: (i: T) => boolean, replacement: T): T[] {
    const foundIndex = items.findIndex(predicate);
    if (!foundIndex) throw new Error("Cannot find a match to replace");
    return [...items.slice(0, foundIndex), replacement, ...items.slice(foundIndex + 1, items.length)]
}

export default replaceItemBy;
