import moment = require("moment");
import * as React from "react";
import {ChangeEvent, useEffect, useState} from "react";
import {
    Button,
    Grid,
    IconButton, makeStyles,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TablePagination,
    TableRow, Theme, createStyles
} from "@eccosolutions/ecco-mui";
import {green, grey} from '@eccosolutions/ecco-mui';
import ExpandMore from '@material-ui/icons/ExpandMore';
import {DateRangePickerMoment} from "@eccosolutions/ecco-mui-controls";
import {findIndexFn} from "@softwareventures/array";
import {Moment} from 'moment';
import {
    getInvoiceRows,
    getPayrollRows,
    payrollToExport,
    TempPayrollDto,
    updateDemoPayrollData
} from "../service/dataservice";

import AlertDialog from './AlertDialog';
import FeedbackMessage from './FeedbackMessage';
import {useAppBarOptions} from "../../AppBarBase";
import {ExportExcel} from "../../table/ExportExcel";
import {LoadingSpinner} from "../../Loading";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useServicesContext} from "../../ServicesContext";
import {InvoiceDto} from "ecco-dto";
import NestedInvoiceTable from "./NestedInvoiceTable";

function PayrollView() {
    const classes = useStyles();
    const [loading, setLoading] = useState<boolean>(false);
    const [loadAvailable, setLoadAvailable] = useState<boolean>(false);
    const [rows, setRows] = useState<TempPayrollDto[]>([]);
    const [invoiceRows, setInvoiceRows] = useState<InvoiceDto[]>([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(50);
    const [selectedPayrollItems, setSelectedPayrollItems] = useState<any>([]);
    const [feedbackMessage, setFeedbackMessage] = useState<String | null>(null);
    const [startDate, setStartDate] = useState<Moment | null>(moment().startOf("month"));
    // separate start date to be sure of the date for the export (ie the user doens't change it)
    const [startDateData, setStartDateData] = useState<string | null>(null);
    const [expandedRows, setExpandedRows] = useState<any>([]);
    const [endDate, setEndDate] = useState<Moment | null>(moment().endOf("month"));
    const [showPostedDialog, setShowPostedDialog] = useState(false);
    useAppBarOptions("manage payroll data");
    const services = useServicesContext();

    // page has been changed. This may need to be a remote call in the real version
    function handleChangePage(_event: React.MouseEvent<HTMLButtonElement> | null, p: number) {
        setPage(p);
    }

    // number of items per page has changed. This may need to be a remote call in the real version
    function handleChangeRowsPerPage(event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) {
        setRowsPerPage(parseInt(event.target.value));
        setPage(0);
    }

    function getDataByDate(): Promise<void> {
        if (startDate && endDate) {
            return getInvoices(startDate, endDate);
        }
        return Promise.resolve();
        //return getDemoData();
    }
    /*function getDemoData() {
        return getDemoEvidenceData().then(d =>
            getDemoPayrollData().then((data: any) => setRows(data))
        );
    }*/

    function getInvoices(startDate: Moment, endDate: Moment): Promise<void> {
        const start = EccoDate.fromMoment(startDate);
        const end = EccoDate.fromMoment(endDate);
        setLoading(true);
        return services.contractRepository.findAllRateCards().then(rates => {
            return services.invoicesRepository.findAllInvoices(start, end).then(data => {
                setInvoiceRows(getInvoiceRows(rates, data));
                setLoading(false);
                setStartDateData(startDate.format("YYYY-MM-DD"));
            });
        });
    }

    function handleExpand(index: number) {
        const foundIndex = expandedRows.indexOf(index);
        if (foundIndex === -1) {
            setExpandedRows([...expandedRows, index]);
            return;
        }
        setExpandedRows([
            ...expandedRows.slice(0, foundIndex),
            ...expandedRows.slice(foundIndex + 1, expandedRows.length)
        ]);
    }

    function isExpanded(index: number): boolean {
        const foundIndex = expandedRows.indexOf(index);
        return foundIndex > -1;
    }

    /*function changeDemoPayrollStatus(payrollId: any) {
        const foundIndex = findIndexFn((r: any) => r.payrollId === payrollId)(rows)!!;
        const payrollItem = {...rows[foundIndex]};
        payrollItem.status = payrollItem.status === "final" ? "draft" : "final";
        setRows([
            ...rows.slice(0, foundIndex),
            payrollItem,
            ...rows.slice(foundIndex + 1, rows.length)
        ]);
        return updateDemoPayrollData(rows);
    }*/

    function getCurrentPage() {
        return rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    }

    // TODO: get it done
    function demoPaySelectedPayrollItems() {
        const payrollItemsToProcess = [...rows];
        // no selected items, please bail
        if (selectedPayrollItems.length === 0) {
            return;
        }
        selectedPayrollItems.forEach((s: any) => {
            const index = findIndexFn((r: any) => r.payrollId === s.payrollId)(
                payrollItemsToProcess
            );
            payrollItemsToProcess[index!]!.paid = true;
        });
        setRows(payrollItemsToProcess);
        // simulate a backend update.
        updateDemoPayrollData(payrollItemsToProcess);
        setFeedbackMessage("Payroll items have been marked as paid");
        setSelectedPayrollItems([]);
        setShowPostedDialog(false);
    }

    /*function openShowDialog() {
        setShowPostedDialog(true);
    }*/

    // function selectablePayrollItems() {
    //     return rows.filter((r: any) => r.status === "final" && !r.paid);
    // }

    /*function onSelectedAll(event: ChangeEvent<HTMLInputElement>, checked: boolean) {
        if (checked) {
            setSelectedPayrollItems(selectablePayrollItems());
            return;
        }
        setSelectedPayrollItems([]);
    }*/

    /*function onSelectSingle(payrollItem: any) {
        if (payrollItem.status !== "final") {
            return true;
        }
        const index = selectedPayrollItems.findIndex(
            (si: any) => si.payrollId === payrollItem.payrollId
        );
        if (index === -1) {
            setSelectedPayrollItems([...selectedPayrollItems, payrollItem]);
            return true;
        }
        setSelectedPayrollItems([
            ...selectedPayrollItems.slice(0, index),
            ...selectedPayrollItems.slice(index + 1, selectedPayrollItems.length)
        ]);
        return true;
    }*/

    /*function isSelected(payroll: any) {
        return selectedPayrollItems.some((s: any) => s.payrollId === payroll.payrollId);
    }*/

    useEffect(() => {
        if (startDate && endDate && !loading) {
            setLoadAvailable(true);
        } else {
            setLoadAvailable(false);
        }
    }, [startDate, endDate, loading]);

    useEffect(() => {
        if (invoiceRows) {
            const payrollData = getPayrollRows(invoiceRows);
            setRows(payrollData);
        } else {
            setRows([]);
        }
    }, [invoiceRows]);

    if (loading) {
        return (
            <Grid container={true} direction={"row"} justify={"center"} alignItems={"center"}>
                <LoadingSpinner />
            </Grid>
        );
    }

    return (
        <Grid container={true} direction={"row"} justify={"flex-start"}>
            <Grid item={true} lg={6} md={6} xs={12}>
                <Grid container={true}>
                    <Grid item={true} xs={8}>
                        <DateRangePickerMoment
                            classes={classes.dateFilterElement}
                            start={startDate}
                            onStartChange={setStartDate}
                            end={endDate}
                            onEndChange={setEndDate}
                        />
                    </Grid>
                    <Grid item={true} xs={12}>
                        <Button
                            className={classes.button}
                            variant="contained"
                            color="primary"
                            disabled={!loadAvailable}
                            onClick={getDataByDate}
                        >
                            load
                        </Button>
                    </Grid>
                </Grid>
            </Grid>

            <Grid item={true} xs={12}>
                <Grid container={true} direction="column" justify="flex-end" alignItems="flex-end">
                    {/*<Button
                        variant="contained"
                        color="primary"
                        disabled={selectedPayrollItems.length === 0}
                        className={classes.button}
                        onClick={openShowDialog}
                    >
                        {`paid ${
                            selectedPayrollItems.length > 0
                                ? `(${selectedPayrollItems.length})`
                                : ""
                        }`}
                    </Button>*/}
                    <Button
                        variant="contained"
                        color="primary"
                        disabled={rows.length == 0}
                        className={classes.button}
                    >
                        <ExportExcel
                            data={payrollToExport(rows)}
                            fileName={`ecco-payroll_${startDateData}`}
                        />
                    </Button>
                </Grid>
            </Grid>

            <Grid item={true} xs={12}>
                <div className={classes.tableWrapper}>
                    <Table className={classes.table}>
                        <TableHead>
                            <TableRow>
                                {/*<TableCell padding="checkbox" size="small">
                                    <Checkbox
                                        indeterminate={
                                            selectedPayrollItems.length > 0 &&
                                            selectedPayrollItems.length <
                                                selectablePayrollItems().length
                                        }
                                        checked={
                                            selectedPayrollItems.length > 0 &&
                                            selectedPayrollItems.length ===
                                                selectablePayrollItems().length
                                        }
                                        onChange={onSelectedAll}
                                        disabled={selectablePayrollItems().length === 0}
                                    />
                                </TableCell>*/}
                                <TableCell>payroll-id</TableCell>
                                <TableCell>staff</TableCell>
                                <TableCell>rate card</TableCell>
                                <TableCell>total mins (p)</TableCell>
                                <TableCell>total mins (a)</TableCell>
                                <TableCell>total value</TableCell>
                                <TableCell>status</TableCell>
                                <TableCell>paid</TableCell>
                                {/*<TableCell>actions</TableCell>*/}
                                <TableCell></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {getCurrentPage().map((row: TempPayrollDto, index: number) => (
                                <React.Fragment key={row.payrollId}>
                                    <TableRow
                                        key={row.payrollId}
                                        className={row.paid ? classes.verifiedRow : ""}
                                    >
                                        {/*<TableCell padding="checkbox" size="small">
                                            <Checkbox
                                                checked={isSelected(row)}
                                                disabled={row.status !== "final" || row.paid}
                                                onChange={() => onSelectSingle(row)}
                                            />
                                        </TableCell>*/}
                                        <TableCell>-</TableCell>
                                        <TableCell>{row.name}</TableCell>
                                        <TableCell>{row.rateCardName}</TableCell>
                                        <TableCell>{row.totalPlannedMins}</TableCell>
                                        <TableCell>{row.totalActualMins}</TableCell>
                                        <TableCell>£{row.amount}</TableCell>
                                        <TableCell>{row.status.toUpperCase()}</TableCell>
                                        <TableCell>{row.paid ? "yes" : "no"}</TableCell>
                                        {/*<TableCell>
                                            <Button
                                                variant="contained"
                                                color={
                                                    row.status === "draft" ? "primary" : "secondary"
                                                }
                                                onClick={() =>
                                                    changeDemoPayrollStatus(row.payrollId)
                                                }
                                                disabled={row.paid}
                                            >
                                                {row.status === "draft"
                                                    ? "finalise"
                                                    : "move to draft"}
                                            </Button>
                                        </TableCell>*/}
                                        <TableCell>
                                            <IconButton onClick={() => handleExpand(index)}>
                                                <ExpandMore />
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>

                                    {isExpanded(index) && (
                                        <TableRow key={row.payrollId}>
                                            <TableCell colSpan={12}>
                                                <NestedInvoiceTable
                                                    lines={row.relatedInvoiceLines}
                                                    reverseLineItem={(_l, _i) => Promise.resolve()}
                                                />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </React.Fragment>
                            ))}
                        </TableBody>
                    </Table>
                    <TablePagination
                        rowsPerPageOptions={[10, 25, 50, 100]}
                        component="div"
                        count={rows.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        backIconButtonProps={{
                            "aria-label": "Previous Page"
                        }}
                        nextIconButtonProps={{
                            "aria-label": "Next Page"
                        }}
                        onChangePage={handleChangePage}
                        onChangeRowsPerPage={handleChangeRowsPerPage}
                    />
                </div>
                {feedbackMessage && (
                    <FeedbackMessage
                        onClose={() => setFeedbackMessage(null)}
                        feedbackMessage={feedbackMessage}
                        type="success"
                    />
                )}
                {showPostedDialog && (
                    <AlertDialog
                        onConfirm={demoPaySelectedPayrollItems}
                        title="Marking as paid"
                        content="Please be aware this action cannot be done"
                        cancelContent="cancel"
                        confirmContent="confirm"
                    />
                )}
            </Grid>
        </Grid>
    );
}

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        dateFilterElement: {
            marginLeft: 20,
            marginTop: 20
        },
        button: {
            marginLeft: 20,
            marginTop: 10,
            marginRight: 20
        },
        table: {
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        },
        nestedRow: {
            backgroundColor: "#eee"
        },
        verifiedRow: {
            backgroundColor: green[200]
        },
        unverifiedLineItem: {
            backgroundColor: grey[200]
        }
    })
);

export default PayrollView;
