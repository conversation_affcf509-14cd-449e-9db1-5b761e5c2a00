import * as files from "./files";

/** Utility for resizing and re-encoding images. */
export class ImageResizer {

    /** @param dimensions The maximum dimensions of the resized image.
     * @param imageEncoding The image encoding options to pass to
     *                      canvas.toDataURL() to encode the resized image
     *                      See the documentation for canvas.toDataURL().
     *                      The default is to encode as image/jpeg with
     *                      quality 0.98. */
    constructor(private dimensions: { maxWidth?: number; maxHeight?: number; },
            private imageEncoding: { type: string; encoderOptions?: any } = {type: "image/jpeg"}) {

        if (this.imageEncoding.encoderOptions == null
                && this.imageEncoding.type == "image/jpeg") {
            this.imageEncoding.encoderOptions = 0.98;
        }
    }

    /** True if resizing and re-encoding images is supported on the current
     * browser with the specified image encoding options. */
    public supported(): boolean {
        if (!files.supported.readImage) {
            return false;
        }

        const canvas = document.createElement("canvas");

        if (!canvas.getContext || !canvas.getContext("2d")) {
            return false;
        }

        return canvas.toDataURL(this.imageEncoding.type)
                .indexOf('data:') == 0;
    }

    /** Resizes and re-encodes the image represented by the specified file.
     *
     * If the read operation is aborted, the promise will be rejected with an
     * instance of Aborted.
     *
     * If the read operation fails with an error, or the file is not a valid image,
     * the promise will be rejected with an instance of Error. */
    public resizeFile(file: Blob): Promise<Blob> {
        return files.readImage(file)
                .then((image: HTMLImageElement) => this.resizeImage(image));
    }

    /** Resizes and re-encodes the specified image.
     *
     * The image must have fully loaded before calling this method (the
     * 'load' event must have fired). */
    public resizeImage(image: HTMLImageElement): Blob {
        let width = image.width;
        let height = image.height;

        if (this.dimensions.maxWidth != null && width > this.dimensions.maxWidth) {
            width = this.dimensions.maxWidth;
            height = image.height * this.dimensions.maxWidth / image.width;
        }

        if (this.dimensions.maxHeight != null && height > this.dimensions.maxHeight) {
            width = image.width * this.dimensions.maxHeight / image.height;
            height = this.dimensions.maxHeight;
        }

        const canvas = document.createElement("canvas");
        canvas.setAttribute("width", String(width) + "px");
        canvas.setAttribute("height", String(height) + "px");

        const context = canvas.getContext("2d")!;
        context.drawImage(image, 0, 0, image.width, image.height, 0, 0, width, height);

        const resultDataUrl = canvas.toDataURL(this.imageEncoding.type, this.imageEncoding.encoderOptions);

        return files.dataUrlToBlob(resultDataUrl);
    }
}