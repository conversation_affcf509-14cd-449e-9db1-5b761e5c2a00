import * as React from "react";
import {FC} from "react";
import "../styles/avatar-control.css";
import {withServicesContext} from "../ServicesContext";
import {AvatarProps, BaseAvatarControl} from "./BaseAvatarControl";
import {EccoAPI} from "../EccoAPI";

class _AvatarControl extends BaseAvatarControl<AvatarProps & {services: EccoAPI}> {
    override render() {
        const imageOverlay = (
            <div
                className="image-overlay"
                style={{position: "absolute", overflow: "hidden"}}
                onMouseLeave={this.disabled ? undefined : () => this.setState({mouseOver: false})}
                onClick={e => this.onClickOverlay(e)}
            >
                <div className="placeholder" style={{cursor: "pointer"}}>
                    <h2 className="fa fa-times-circle"></h2>
                    <p className="prompt">Remove photo</p>
                </div>
            </div>
        );

        const imageThumbnail = this.state.imageUrl ? (
            <img
                className="image-thumbnail"
                src={this.state.imageUrl}
                alt="photo"
                onMouseEnter={this.disabled ? undefined : () => this.setState({mouseOver: true})}
            />
        ) : null;

        const prompt = (
            <p className="prompt" title={this.state.errorReason || "Click or drop an image"}>
                {this.state.errorText || "Add a photo"}
            </p>
        );

        const fileInput = (
            <input
                ref={this.fileInputRef}
                type="file"
                accept="image/jpeg"
                style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    margin: 0,
                    border: "10000px solid transparent",
                    opacity: 0,
                    cursor: "pointer"
                }}
                onClick={() => this.onFileInputClick()}
                onChange={() => this.onFileChange()}
            />
        );

        const placeholder = this.disabled ? (
            <i className="fa fa-5x fa-user" color="white" />
        ) : (
            <>
                <h2 className="fa fa-camera" />
                {prompt}
            </>
        );

        const imageDrop = (
            <div className="image-drop" style={{position: "relative", overflow: "hidden"}}>
                <div className="placeholder">{placeholder}</div>
                {this.disabled ? null : fileInput}
            </div>
        );

        return (
            <div className="avatar-control thumbnail" style={{position: "relative"}}>
                {this.state.mouseOver ? imageOverlay : null}
                {imageThumbnail}
                {this.state.imageUrl ? null : imageDrop}
            </div>
        );
    }
}

export const AvatarControl: FC<AvatarProps> = props =>
    withServicesContext(services =>
        services ? <_AvatarControl {...props} services={services} /> : null
    );

export default AvatarControl;
