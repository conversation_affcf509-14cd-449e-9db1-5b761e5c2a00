.avatar-control .image-drop,
.avatar-control .image-overlay {
  min-height: 100px;
  text-align: center;
}
.avatar-control .image-drop {
  border: 1px solid #ddd;
  background-color: #eee;
}
.avatar-control .image-overlay {
  z-index: 1;
  margin: -4px;
  width: 100%;
  height: 100%;
  background: #eeeeee; /* Fallback solid colour */
  background: rgba(238, 238, 238, 0.75);
}
.avatar-control .image-drop > .placeholder,
.avatar-control .image-overlay > .placeholder {
  padding: 40px 10px;
}

.avatar-control.thumbnail {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);
}
.avatar-control.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 20px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: border 0.2s ease-in-out;
}
