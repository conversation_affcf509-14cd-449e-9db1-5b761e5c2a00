import * as React from "react";
import {ClassAttributes} from "react";
import {Uuid} from "@eccosolutions/ecco-crypto";

import {Address, SessionData} from "ecco-dto";
import {AddressHistoryCommand, CommandQueue, CommandSource} from "ecco-commands";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {
    CommandForm,
    CommandSubform,
    CommandSubformProps,
    withCommandForm
} from "../../cmd-queue/CommandForm";
import {possiblyModalForm} from "ecco-components-core";
import {AsyncServiceRecipientWithEntities} from "../../data/serviceRecipientHooks";
import {AddressLocation} from "../../address/AddressLocationForm";
import {ServiceRecipientWithEntitiesContext} from "../../data/serviceRecipientHooks";

export const ClientAddressLocationEditor = (props: {
    serviceRecipientId: number;
    formRef: (c: ClientAddressLocation) => void;
}) =>
    withCommandForm(commandForm =>
        possiblyModalForm(
            "address",
            true,
            true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm(),
            false, // TODO could emitChangesTo and see if there are any commands
            false,
            getClientAddressLocationSubform(props.serviceRecipientId, props.formRef, commandForm)
        )
    );

/**
 *
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
export function getClientAddressLocationSubform(
    serviceRecipientId: number,
    formRef: (c: ClientAddressLocation) => void,
    commandForm: CommandForm
) {
    return (
        <AsyncServiceRecipientWithEntities.Resolved>
            {(workflowContext: ServiceRecipientWithEntitiesContext) => {
                const client = workflowContext.client!;
                return (
                    <ClientAddressLocation
                        ref={formRef}
                        sessionData={workflowContext.serviceRecipient.features}
                        serviceRecipientId={serviceRecipientId}
                        contactId={client.contactId}
                        buildingLocationId={client.residenceId}
                        addressLocationId={client.addressedLocationId}
                        legacyAddress={client.address}
                        commandForm={commandForm}
                    />
                );
            }}
        </AsyncServiceRecipientWithEntities.Resolved>
    );
}

interface CmdProps extends ClassAttributes<ClientAddressLocation> {
    sessionData: SessionData;
    serviceRecipientId: number;
    /** If contactId is present it will be the target, otherwise the address of the service recipient will be updated */
    contactId?: number | undefined;
    buildingLocationId?: number | null | undefined; // hasAddressChange is when buildingId OR addressId changes
    addressLocationId?: number | null | undefined;
    legacyAddress?: Address | undefined; // a non-addressLocationId address (typically set directly via jsp)
}

interface State {}

/**
 * Shows the 'address' section under some details, like client/staff/building (eg ClientDetailForm)
 */
export class ClientAddressLocation extends CommandSubform<CmdProps, State> implements CommandSource {
    private addressLocation: AddressLocation | null = null;
    private cmdUuid = Uuid.randomV4();
    private noopChange = () => {}; // TODO do as per IndividualDetailModal.tsx
    //private buildingRepository: BuildingAjaxRepository;

    constructor(props: CmdProps & CommandSubformProps) {
        super(props);
        // const apiClient = getGlobalApiClient(); // should be set in ServicesContextProvider as that provides the apiClient that we load the parent resources from
        //this.buildingRepository = new BuildingAjaxRepository(apiClient);
    }

    emitChangesTo(commandQueue: CommandQueue) {
        // only submit is there is a change - part-fulfilled data should not create a command
        // only submit is there is a change - part-fulfilled data should not create a command
        if (this.addressLocation && this.addressLocation.hasAddressChange()) {
            const addressLocationId = this.addressLocation.getAddressLocationId();
            const validFromDteTime =
                this.addressLocation.getValidFrom()?.toDateTimeMidnight() || null;
            const cmd = this.queueAddressLocationCommand(
                commandQueue,
                addressLocationId,
                this.addressLocation.getBuildingLocationId(),
                validFromDteTime
            );
            commandQueue.addCommand(cmd);
        }
    }

    getErrors(): string[] {
        if (this.addressLocation) {
            if (this.addressLocation.hasAddressChange() && !this.addressLocation.getValidFrom()) {
                return ["'valid from' is required"];
            }
        }
        return [];
    }

    protected queueAddressLocationCommand(
        _commandQueue: CommandQueue,
        addressLocationId: number | null,
        buildingId: number | null,
        validFrom: EccoDateTime | null
    ) {
        // contactId is supplied on each address history change
        const cmd = new AddressHistoryCommand(
            this.cmdUuid,
            "add",
            this.props.serviceRecipientId,
            null,
            this.props.contactId
        );
        if (addressLocationId) {
            cmd.changeAddressLocation(this.props.addressLocationId || null, addressLocationId);
        }
        if (buildingId) {
            cmd.changeBuildingLocation(this.props.buildingLocationId || null, buildingId);
        }
        // at point of submission we require a validFrom
        cmd.changeValidFrom(null, validFrom!);
        return cmd;
    }

    override render() {
        return (
            <AddressLocation
                serviceRecipientId={this.props.serviceRecipientId}
                contactId={this.props.contactId}
                showValidFrom
                displayAddress={this.props.legacyAddress || null}
                showBuildings={this.props.sessionData.isEnabled("menu.buildings")}
                addressLocationId={this.props.addressLocationId}
                buildingLocationId={this.props.buildingLocationId}
                ref={c => (this.addressLocation = c)}
                handleAddressValidChange={this.noopChange}
            />
        );
    }
}
