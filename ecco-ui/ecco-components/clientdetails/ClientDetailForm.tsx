import * as React from "react";
import {ClassAttributes, FC} from "react";
import {EccoDate, isEmpty, SelectListOption} from "@eccosolutions/ecco-common";
import {Grid} from "@eccosolutions/ecco-mui";
import {CommandQueue, CommandSource, ClientWithContactUpdateCommand} from "ecco-commands";
import {
    Client,
    ClientDetailAbstractSecretFields,
    ClientSecretFields,
    SessionData,
    SessionDataGlobal,
    TaskNames
} from "ecco-dto";
import {
    datePickerIso8601Input,
    dateTimeIso8601Input,
    dropdownList,
    emailInput,
    phoneInput,
    possiblyModalForm,
    textInput
} from "ecco-components-core";
import {
    CommandForm,
    CommandSubform,
    CommandSubformProps,
    withCommandForm
} from "../cmd-queue/CommandForm";
import {AsyncSessionData} from "../AsyncSessionData";
import {ClientAddressLocation} from "./components/ClientAddressLocationForm";
import TaskSummary from "../tasks/TaskSummary";
import {
    AsyncServiceRecipientWithEntities,
    ServiceRecipientWithEntitiesContext,
    WithLatestCommands
} from "../data/serviceRecipientHooks";
import {calcPatternValidationState} from "ecco-components-core";

/**
 * Command-based editing of a client
 */
export const ClientDetailEditor = (props: {
    serviceRecipientId: number;
    showCode: boolean;
    taskName: string;
    taskHandle?: string | undefined;
    formRef: (c: ClientDetail) => void;
}) =>
    withCommandForm(commandForm =>
        possiblyModalForm(
            "client details",
            true,
            true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm(),
            // NB this doesn't work dynamically as it needs to be a proper component
            // it worked until contactWithClient2, where it exposed that commandForm wasn't registered at the calculation
            false, // commandForm.getErrors().length > 0,
            false,
            getClientDetailSubform(
                props.serviceRecipientId,
                props.showCode,
                props.taskName,
                props.taskHandle,
                props.formRef,
                commandForm
            )
        )
    );

/**
 *
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
export function getClientDetailSubform(
    serviceRecipientId: number,
    showCode: boolean,
    taskName: string,
    taskHandle: string | undefined,
    formRef: (c: ClientDetail) => void,
    commandForm: CommandForm
) {
    return (
        <AsyncServiceRecipientWithEntities.Resolved>
            {(srContext: ServiceRecipientWithEntitiesContext) => (
                <AsyncSessionData.Resolved>
                    {(sessionData: SessionData) => {
                        const client = srContext.client!;
                        return (
                            <React.Fragment>
                                <WithLatestCommands srId={serviceRecipientId}>
                                    <TaskSummary
                                        taskName={"clientWithContact"}
                                        srId={serviceRecipientId}
                                    />
                                </WithLatestCommands>
                                <ClientDetail
                                    ref={formRef}
                                    serviceRecipientId={serviceRecipientId}
                                    serviceTypeId={srContext.serviceType.id}
                                    showCode={showCode}
                                    client={client}
                                    taskName={taskName}
                                    taskHandle={taskHandle}
                                    readOnly={!sessionData.hasRoleReferralEdit()}
                                    commandForm={commandForm}
                                    sessionData={sessionData}
                                />
                                {/* show the 'address' and change */}
                                <ClientAddressLocation
                                    ref={null}
                                    sessionData={sessionData}
                                    serviceRecipientId={serviceRecipientId}
                                    contactId={client.contactId}
                                    addressLocationId={client.addressedLocationId}
                                    buildingLocationId={client.residenceId}
                                    legacyAddress={client.address}
                                    commandForm={commandForm}
                                />
                            </React.Fragment>
                        );
                    }}
                </AsyncSessionData.Resolved>
            )}
        </AsyncServiceRecipientWithEntities.Resolved>
    );
}

interface Props extends ClassAttributes<ClientDetail> {
    readOnly: boolean;
    serviceRecipientId: number;
    showCode: boolean;
    client: Client;
    taskHandle?: string | undefined;
    taskName: string;
    serviceTypeId: number;
    sessionData: SessionData;
}

type ClientAbstractField = keyof ClientDetailAbstractSecretFields;
type ClientDetailField = keyof ClientSecretFields;

const contactOptions: SelectListOption[] = [
    {id: "m", name: "mobile"},
    {id: "ll", name: "landline"},
    {id: "e", name: "email"},
    {id: "l", name: "letter"},
    {id: "s", name: "sms"}
];

const Entry: FC = props => (
    <Grid item sm={6} xs={12}>
        {props.children}
    </Grid>
);

export function clientDetailsCommonFields<STATE extends ClientSecretFields>(
    stateSetter: (newState: STATE) => void,
    details: STATE,
    isRequired: (fieldName: ClientDetailField) => boolean,
    isOptional: (fieldName: ClientDetailField) => boolean,
    readOnly: boolean,
    sessionData: SessionDataGlobal,
    isNoShow?: ((fieldName: string) => boolean) | undefined
) {
    const field: (name: ClientDetailField) => Extract<keyof STATE, string> = name =>
        name as Extract<keyof STATE, string>;
    return (
        <>
            {isOptional("housingBenefit") ? (
                <Entry>
                    {textInput(
                        field("housingBenefit"),
                        "housing benefit",
                        stateSetter,
                        details,
                        undefined,
                        readOnly,
                        isRequired("housingBenefit")
                    )}
                </Entry>
            ) : null}
        </>
    );
}

export function individualDetailsCommonFields<STATE extends ClientDetailAbstractSecretFields>(
    stateSetter: (newState: STATE) => void,
    details: STATE,
    isRequired: (fieldName: ClientAbstractField) => boolean,
    isOptional: (fieldName: ClientAbstractField) => boolean,
    readOnly: boolean,
    sessionData: SessionDataGlobal,
    isNoShow?: ((fieldName: string) => boolean) | undefined
) {
    const field: (name: ClientAbstractField) => Extract<keyof STATE, string> = name =>
        name as Extract<keyof STATE, string>;
    return (
        <>
            {isOptional("completeAt") ? (
                <Grid item xs={12}>
                    {dateTimeIso8601Input(
                        field("completeAt"),
                        "client verified complete at",
                        stateSetter,
                        details,
                        readOnly
                    )}
                </Grid>
            ) : null}
            {isOptional("code") ? (
                <Entry>
                    {textInput(field("code"), "c-id", stateSetter, details, undefined, readOnly)}
                </Entry>
            ) : null}
            <Entry>
                {textInput(
                    field("firstName"),
                    "first name",
                    stateSetter,
                    details,
                    undefined,
                    readOnly,
                    true
                )}
            </Entry>
            <Entry>
                {textInput(
                    field("lastName"),
                    "last name",
                    stateSetter,
                    details,
                    undefined,
                    readOnly,
                    true
                )}
            </Entry>
            <Entry>
                {textInput(
                    field("knownAs"),
                    "known as",
                    stateSetter,
                    details,
                    undefined,
                    readOnly,
                    false
                )}
            </Entry>
            <Entry>
                {dropdownList(
                    "pronouns",
                    stateSetter,
                    details,
                    field("pronounsId"),
                    sessionData.getPronounsList(),
                    undefined,
                    readOnly,
                    false
                )}
            </Entry>
            <Entry>
                {/* 'details' has 'birthDate' as missing/undefined */}
                {datePickerIso8601Input(
                    field("birthDate"),
                    "birth date",
                    stateSetter,
                    details,
                    readOnly,
                    isRequired("birthDate"),
                    undefined,
                    EccoDate.todayLocalTime()
                )}
            </Entry>
            <Entry>
                {dropdownList(
                    "gender",
                    stateSetter,
                    details,
                    field("genderId"),
                    sessionData.getGenderList(),
                    undefined,
                    readOnly,
                    isRequired("genderId")
                )}
            </Entry>
            <Entry>
                {phoneInput(
                    field("phoneNumber"),
                    "landline",
                    stateSetter,
                    details,
                    undefined,
                    readOnly
                )}
            </Entry>
            <Entry>
                {phoneInput(
                    field("mobileNumber"),
                    "mobile",
                    stateSetter,
                    details,
                    undefined,
                    readOnly
                )}
            </Entry>
            <Entry>
                {emailInput(field("email"), "email", stateSetter, details, undefined, readOnly)}
            </Entry>
            {(!isNoShow || !isNoShow("preferredContact")) && (
                <Entry>
                    {dropdownList(
                        "preferred contact",
                        stateSetter,
                        details,
                        field("preferredContactMethod"),
                        contactOptions,
                        undefined,
                        readOnly
                    )}
                </Entry>
            )}
            {isOptional("genderAtBirthId") ? (
                <Entry>
                    {dropdownList(
                        "gender at birth",
                        stateSetter,
                        details,
                        field("genderAtBirthId"),
                        sessionData.getGenderAtBirthList(),
                        undefined,
                        readOnly,
                        isRequired("genderAtBirthId")
                    )}
                </Entry>
            ) : null}
            {isOptional("firstLanguageId") ? (
                <Entry>
                    {dropdownList(
                        "first language",
                        stateSetter,
                        details,
                        field("firstLanguageId"),
                        sessionData.getLanguageList(),
                        undefined,
                        readOnly,
                        isRequired("firstLanguageId")
                    )}
                </Entry>
            ) : null}
            {isOptional("ethnicOriginId") ? (
                <Entry>
                    {dropdownList(
                        "ethnic origin",
                        stateSetter,
                        details,
                        field("ethnicOriginId"),
                        sessionData.getEthnicOriginList(),
                        undefined,
                        readOnly,
                        isRequired("ethnicOriginId")
                    )}
                </Entry>
            ) : null}
            {isOptional("nationalityId") ? (
                <Entry>
                    {dropdownList(
                        "nationality",
                        stateSetter,
                        details,
                        field("nationalityId"),
                        sessionData.getNationalityList(),
                        undefined,
                        readOnly,
                        isRequired("nationalityId")
                    )}
                </Entry>
            ) : null}
            {isOptional("maritalStatusId") ? (
                <Entry>
                    {dropdownList(
                        "marital status",
                        stateSetter,
                        details,
                        field("maritalStatusId"),
                        sessionData.getMaritalStatusList(),
                        undefined,
                        readOnly,
                        isRequired("maritalStatusId")
                    )}
                </Entry>
            ) : null}
            {isOptional("religionId") ? (
                <Entry>
                    {dropdownList(
                        "religion",
                        stateSetter,
                        details,
                        field("religionId"),
                        sessionData.getReligionList(),
                        undefined,
                        readOnly,
                        isRequired("religionId")
                    )}
                </Entry>
            ) : null}
            {isOptional("disabilityId") ? (
                <Entry>
                    {dropdownList(
                        "disability",
                        stateSetter,
                        details,
                        field("disabilityId"),
                        sessionData.getDisabilityList(),
                        undefined,
                        readOnly,
                        isRequired("disabilityId")
                    )}
                </Entry>
            ) : null}
            {isOptional("sexualOrientationId") ? (
                <Entry>
                    {dropdownList(
                        "sexual orientation",
                        stateSetter,
                        details,
                        field("sexualOrientationId"),
                        sessionData.getSexualOrientationList(),
                        undefined,
                        readOnly,
                        isRequired("sexualOrientationId")
                    )}
                </Entry>
            ) : null}
            {isOptional("ni") ? (
                <Entry>
                    {textInput(
                        field("ni"),
                        "national insurance",
                        stateSetter,
                        details,
                        undefined,
                        readOnly,
                        isRequired("ni")
                    )}
                </Entry>
            ) : null}
            {isOptional("nhs") ? (
                <Entry>
                    {textInput(
                        field("nhs"),
                        "nhs number",
                        stateSetter,
                        details,
                        undefined,
                        readOnly,
                        isRequired("nhs")
                    )}
                </Entry>
            ) : null}
        </>
    );
}

type ClientField = keyof Client;

interface State {
    client: Client;
    requiredFields: ClientField[];
    optionalFields: ClientField[];
}

// see ClientDetailAbstract NINO_REGEX
export const ninoFn = calcPatternValidationState(/^[ABCEGHJKLMNOPRSTWXYZ][ABCEGHJKLMNPRSTWXYZ][0-9]{6}[A-Z]?$/);

export function translateNames(names: string[]) {
    return names.map(m => ("clientCompletedDate" == m ? "clientCompletedAt" : m));
}

export class ClientDetail extends CommandSubform<Props, State> implements CommandSource {
    //private clientAddressLocationEditor: ClientAddressLocationEditor = null;

    constructor(props: Props & CommandSubformProps) {
        super(props);
        const client = this.props.client;

        const requiredFields = translateNames(
            this.props.sessionData.getSettingAsArray("com.ecco.forms:CLIENT_DETAIL_REQUIRED_FIELDS")
        ) as ClientField[];

        const optionalFields = translateNames(
            this.props.sessionData.getSettingAsArray("com.ecco.forms:CLIENT_DETAIL_OPTIONAL_FIELDS")
        ) as ClientField[];
        props.showCode && optionalFields.push("code");

        // NB REQUIRED fields come from the cfg_settings table EXCEPT for clientWithContact2
        if (props.taskName == TaskNames.clientWithContact2) {
            const serviceType = props.sessionData.getServiceTypeById(props.serviceTypeId);
            // as per clientDetailAbstractRender defined required fields
            const requiredFields2Str =
                (serviceType &&
                    serviceType.getTaskDefinitionSetting(
                        TaskNames.clientWithContact2,
                        "clientDetailRequiredFields"
                    )) ||
                null;
            if (requiredFields2Str) {
                const requiredFields2 = requiredFields2Str
                    .split(",")
                    .map(i => i.trim())
                    .filter(i => requiredFields.indexOf(i as ClientField) < 0);
                requiredFields.push(...(translateNames(requiredFields2) as ClientField[]));
            }
        }

        this.state = {
            client: client,
            requiredFields,
            optionalFields
        };
    }

    getErrors(): string[] {
        const allErrors: string[] = [];
        // firstName and lastName are always required
        const minFields: ClientField[] = ["firstName", "lastName"];
        const required = minFields
            .concat(this.state.requiredFields)
            .reduce((errors, clientField) => {
                if (isEmpty(this.state.client[clientField])) {
                    errors.push(`${clientField} is required`);
                }
                return errors;
            }, [] as string[]);
        allErrors.push(...required);

        if (this.state.client.ni) {
            if (ninoFn(false, this.state.client.ni) == "error") {
                allErrors.push("ni invalid");
            }
        }

        return allErrors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        this.queueClientDetailCommand(commandQueue);
    }

    protected queueClientDetailCommand(commandQueue: CommandQueue) {
        const cmd = new ClientWithContactUpdateCommand(
            this.props.serviceRecipientId,
            this.props.taskHandle
        );
        cmd.change(this.props.client, this.state.client);
        cmd.changeHousingBenefit(
            this.props.client.housingBenefit,
            this.state.client.housingBenefit
        );

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }

        //this.clientAddressLocationEditor.
    }

    override render() {
        const isOptional = (fieldName: ClientField) => {
            return this.state.optionalFields.indexOf(fieldName) >= 0;
        };
        const isRequired = (fieldName: ClientField) => {
            return this.state.requiredFields.indexOf(fieldName) >= 0;
        };
        const clientStateSetter = (client: Client) => this.setState({client});
        return (
            <div className="v-gap-15">
                <Grid container>
                    {individualDetailsCommonFields(
                        clientStateSetter,
                        this.state.client,
                        isRequired,
                        isOptional,
                        this.props.readOnly,
                        this.props.sessionData
                    )}
                    {clientDetailsCommonFields(
                        clientStateSetter,
                        this.state.client,
                        isRequired,
                        isOptional,
                        this.props.readOnly,
                        this.props.sessionData
                    )}
                </Grid>
            </div>
        );
    }
}
