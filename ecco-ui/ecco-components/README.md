```typescript jsx
/**
 * NB for typescript there can be clever defaults and overrides and strong typing - see custom type - https://thoughtbot.com/blog/type-safe-state-modeling-with-typescript-and-react-hooks
 * and Partial in https://fettblog.eu/typescript-react/context/ and more types in https://medium.com/@jrwebdev/react-hooks-in-typescript-88fce7001d0d
 * and https://fettblog.eu/typescript-react/hooks/
 * which, overall, helps with readability and testing as only the properties required need to be provided
 * customTypes are also useful for grouping state/actions: https://upmostly.com/tutorials/using-custom-react-hooks-simplify-forms
 */
 interface ProfileState {
      profile: Profile;
      setProfile: React.Dispatch<React.SetStateAction<Profile>>;
     }
 export const useProfile = (overrides?: Partial<Profile>): ProfileState => {
      const defaultProfile: Profile = {
        firstName: "Foo",
        lastName: "Bar",
        title: "Software developer",
      };
      const [profile, setProfile] = useState<Profile>({
        ...defaultProfile,
        ...overrides,
      });
      return { profile, setProfile };
    };

 // * usage (also overriding the default state)
 const ProfilePage = (): ReactElement => {
      const { profile } = useProfile({
        title: "Designer",
      });
      return (. . .);
    };
```

Styling:
```typescript jsx
import { useStateValue } from './state';
const ThemedButton = () => {
    const [{ theme }, dispatch] = useStateValue();
    return (
        <Button
            primaryColor={theme.primary}
            onClick={() => dispatch({
                type: 'changeTheme',
                newTheme: { primary: 'blue'}
            })}
        >
            Make me blue!
        </Button>
    );
}


/*
* Styling buttons from https://material-ui.com/components/buttons/
* Styling thoughts: https://codeburst.io/my-journey-to-make-styling-with-material-ui-right-6a44f7c68113
*/
const theme = createMuiTheme({
    palette: {
        primary: green,
    },
});
<ThemeProvider theme={theme}>
    <Button variant="contained" color="primary" className={classes.margin}>
        Theme Provider
    </Button>
</ThemeProvider>
```