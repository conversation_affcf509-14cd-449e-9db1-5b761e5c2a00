import * as React from "react";
import {createContext, Component, ComponentType, useContext, FC} from "react";

// Inspired by https://github.com/Hotell/blogposts/blob/master/2018-02/ultimate-react-component-patterns/src/hoc-patterns/with-toggle.tsx

declare type Omit<T, K> = Pick<T, Exclude<keyof T, K>>

export interface Navigation {
    /**
     * Returns a callback that will cause navigation e.g. for use as onClick={asCallback("./page.html")}
     * Will return undefined if the registered implementation instead returns a string from asHref.
     */
    asCallback: (url: string) => (() => void) | undefined;

    /**
     * Returns the string to pass to href if this implementation doesn't have an asCallback implementation.
     */
    asHref: (url: string) => string | undefined;
}

export interface WithNavigationProps {
    navigation: Navigation;
}


/** Initial context value will default to using standard navigation */
export const NavigationContext = createContext<Navigation>({
    asCallback: () => undefined,
    asHref: url => url
});

type OwnProps = object;

export function withNavigation<OriginalProps>(
    UnwrappedComponent: ComponentType<OriginalProps & WithNavigationProps>
) {
    type Props = Omit<OriginalProps, keyof WithNavigationProps> & OwnProps
    class WithNavigation extends Component<Props> {
        static displayName = UnwrappedComponent.displayName;
        static WrappedComponent = UnwrappedComponent;
        override render() {
            // Generics and spread issue
            // https://github.com/Microsoft/TypeScript/issues/10727
            const {children, ...rest} = this.props as any;
            return (
                <NavigationContext.Consumer>
                    {navContext => <UnwrappedComponent {...rest} navigation={navContext} />}
                </NavigationContext.Consumer>
            );
        }
    }

    // TODO: We may need: return hoistNonReactStatics(WithToggle, UnwrappedComponent as any) as ComponentType<Props>
    return WithNavigation as ComponentType<Props>;
}

export const useNavigation = () => useContext(NavigationContext);

export const NavAnchor: FC<{key: string | number, url: string, className: string}> = props => {
    const nav = useNavigation();
    return <a key={props.key}
           className={props.className}
           href={nav.asHref(props.url)}
           onClick={nav.asCallback(props.url)}
        >{props.children}</a>;

};
