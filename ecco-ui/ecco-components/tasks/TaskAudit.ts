import {SparseArray, StringToObjectMap} from "@eccosolutions/ecco-common";
import {
    ApiClient,
    getGlobalApiClient,
    isOffline,
    ReferralAjaxRepository,
    ReferralSummaryDto,
    ReferralSummaryWithEntities,
    ReviewChoices,
    ServiceRecipientTaskBaseCommandDto,
    SupportWorkAjaxRepository,
    TaskNames
} from "ecco-dto";

/** Takes a command task name and ensure its relating to the current definition */
export function translateTaskName(taskNameCmd: string): string {
    // TODO fix command history properly / taskdefinitions.name or use synonyms
    if (taskNameCmd == "allocate") {
        return TaskNames.allocateToServices;
    }
    return taskNameCmd;
}

export class TaskAudit {
    private static _instance: TaskAudit | undefined;
    static get instance(): TaskAudit {
        if (!this._instance) {
            this._instance = new TaskAudit(getGlobalApiClient()); // should be set in ServicesContextProvider as that provides the apiClient that we load the parent resources from
        }
        return this._instance;
    }
    private referralRepository: ReferralAjaxRepository;
    private supportWorkRepository: SupportWorkAjaxRepository;

    private cacheCommandAudits: SparseArray<null | Promise<
        StringToObjectMap<ServiceRecipientTaskBaseCommandDto>
    >> = {};
    private cacheReferralWithEntities: SparseArray<null | Promise<ReferralSummaryWithEntities>> =
        {};
    private cacheReviewChoices: SparseArray<null | Promise<ReviewChoices>> = {};
    private cacheClientReferrals: SparseArray<null | Promise<ReferralSummaryDto[]>> = {};

    constructor(apiClient: ApiClient) {
        this.referralRepository = new ReferralAjaxRepository(apiClient);
        this.supportWorkRepository = new SupportWorkAjaxRepository(apiClient);
    }

    /**
     * Ensure that the next access to all audit data required forces a reload.
     */
    public clearAllCache(serviceRecipientId: number) {
        // promises could be in progress - but will just be completed
        this.cacheCommandAudits[serviceRecipientId] = null;
        this.cacheReferralWithEntities[serviceRecipientId] = null;
        this.cacheReviewChoices[serviceRecipientId] = null;
        this.cacheClientReferrals[serviceRecipientId] = null;
    }

    /** Returns latests commands indexed by taskName or an empty object if we are offline - i.e. no commands */
    public getLatestCommandsCached(
        clearCache: boolean,
        serviceRecipientId: number
    ): Promise<StringToObjectMap<ServiceRecipientTaskBaseCommandDto>> {
        if (isOffline()) {
            return Promise.resolve({});
        }

        // cache the query so we can re-use for the duration of this enhanceTaskList
        if (clearCache || !this.cacheCommandAudits[serviceRecipientId]) {
            this.cacheCommandAudits[serviceRecipientId] = this.referralRepository
                .findLatestCommandPerTaskName(serviceRecipientId)
                .then(cmds => {
                    const data: StringToObjectMap<ServiceRecipientTaskBaseCommandDto> = {};
                    cmds.forEach(c => (data[translateTaskName(c.taskName)] = c));
                    return data;
                });
        }
        return this.cacheCommandAudits[serviceRecipientId]!;
    }

    public getReviewChoicesQ(
        clearCache: boolean,
        serviceRecipientId: number
    ): Promise<ReviewChoices | null> {
        if (isOffline()) {
            return Promise.resolve(null);
        }

        // cache the query so we can re-use for the duration of this enhanceTaskList
        if (clearCache || !this.cacheReviewChoices[serviceRecipientId]) {
            this.cacheReviewChoices[serviceRecipientId] =
                this.supportWorkRepository.findReviewChoicesByServiceRecipientId(
                    serviceRecipientId
                );
        }

        return this.cacheReviewChoices[serviceRecipientId]!;
    }

    /*private getReferralWithEntitiesQ(
        clearCache: boolean,
        serviceRecipientId: number
    ): Promise<ReferralSummaryWithEntities> {
        // cache the query so we can re-use for the duration of this enhanceTaskList
        if (clearCache || !this.cacheReferralWithEntities[serviceRecipientId]) {
            this.cacheReferralWithEntities[serviceRecipientId] =
                this.referralRepository.findOneReferralSummaryWithEntitiesUsingDto(
                    serviceRecipientId
                );
        }

        return this.cacheReferralWithEntities[serviceRecipientId]!;
    }*/

    /**
     * Loads all referrals regardless of security of the user (eg ACLs)
     */
    public getClientReferralsWithoutSecurityQ(
        clearCache: boolean,
        serviceRecipientId: number
    ): Promise<ReferralSummaryDto[]> {
        // cache the query so we can re-use for the duration of this enhanceTaskList
        if (clearCache || !this.cacheClientReferrals[serviceRecipientId]) {
            // see if this referral had the info
            /*const clientReferrals: Promise<ReferralSummaryDto[]> = this.referralRepository
                .findOneReferralSummaryByServiceRecipientIdUsingDto(serviceRecipientId)
                .then(referral =>
                    // used by data protection to get the latest for the audit
                    this.referralRepository.findAllReferralWithoutSecuritySummaryByClient(
                        referral.clientId
                    )
                )
                .then(clientReferrals => {
                    return clientReferrals;
                });*/
            this.cacheClientReferrals[serviceRecipientId] = this.referralRepository
                .findOneReferralSummaryByServiceRecipientIdUsingDto(serviceRecipientId)
                .then(referral =>
                    // used by data protection to get the latest for the audit
                    this.referralRepository.findAllReferralWithoutSecuritySummaryByClient(
                        referral.clientId
                    )
                )
                .then(clientReferrals => {
                    return clientReferrals;
                });
        }

        return this.cacheClientReferrals[serviceRecipientId]!;
    }
}
