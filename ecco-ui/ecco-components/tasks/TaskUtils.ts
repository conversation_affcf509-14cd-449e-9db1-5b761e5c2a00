import {EccoDate} from "@eccosolutions/ecco-common";

export type TaskDueStatus = "overdue" | "now" | "soon" | "later"

export function taskDueStatus(dueDate: string | null): TaskDueStatus | null {
    if (!dueDate) {
        return null;
    }
    const overdue = EccoDate.parseIso8601FromDateTime(dueDate).earlierThan(EccoDate.todayUtc());
    const dueToday = EccoDate.parseIso8601FromDateTime(dueDate).equals(EccoDate.todayUtc());
    const dueSoon =
        EccoDate.parseIso8601FromDateTime(dueDate).laterThan(EccoDate.todayUtc()) &&
        EccoDate.parseIso8601FromDateTime(dueDate).earlierThanOrEqual(
            EccoDate.todayUtc().addDays(7)
        );
    return overdue ? "overdue" : dueToday ? "now" : dueSoon ? "soon" : "later";
}

export function taskDueClass<T>(status: TaskDueStatus | null) : string | null{
    if (!status) {
        return null;
    }

    switch (status) {
        case "overdue":
            return "task-overdue";
        case "now":
            return "task-due";
        case "soon":
            return "task-due";
        case "later":
            return "task-later";
        default:
            return null;
    }
}
