import {isOffline, TaskDto, TaskRepository, WorkflowDto, WorkflowOperations} from "ecco-dto";
import * as React from "react";
import {ReactElement} from "react";
import {
    getWorkflowRepository,
    stringifyPossibleError,
    withAuth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from "ecco-offline-data";
import {usePromise} from "../data/entityLoadHooks";
import {LoadingSpinner} from "../Loading";
import {bus} from "@eccosolutions/ecco-common";


export interface WorkflowContext {
    workflow: WorkflowDto;
}

export class WorkflowReloadEvent {
    public static bus = bus<WorkflowReloadEvent>();
}

export function useWorkflow(srId: number) {
    // FIXME: add workflow repo to EccoAPI
    //  const {getWorkflowRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () =>
            withAuthErrorHandler(getWorkflowRepository().findOneWorkflowByServiceRecipientId(srId)),
        [srId],
        WorkflowReloadEvent.bus
    );
    return {workflow: resolved, error, loading};
}

export const WorkflowLoader = (props: {
    srId: number;
    children: (workflow: WorkflowDto) => ReactElement | ReactElement[];
}): ReactElement => {
    const {workflow, loading, error} = useWorkflow(props.srId);
    if (!workflow) {
        if (loading) return <LoadingSpinner />;
        else return <div>{stringifyPossibleError(error)}</div>;
    }
    return <>{props.children(workflow)}</>;
};
WorkflowLoader.displayName = "WorkflowLoader";

export class WorkflowActiveRecord implements WorkflowOperations {
    constructor(
        private workflow: WorkflowDto,
        private reload: () => void,
        private taskRepository: TaskRepository
    ) {}

    getTasks() {
        return this.workflow.tasks;
    }

    claimTask(task: TaskDto) {
        const claimQ =
            task.isCompleted || isOffline()
                ? Promise.resolve(null)
                : this.taskRepository.postClaimTask(task.taskHandle);
        // Always refresh workflow and referral afterwards

        return claimQ.then(() => this.reload());
    }

    // same logic as claimTask, as that is what we are mimicking
    ensureUnplannedWorkflowTask(task: TaskDto) {
        const claimQ =
            task.isCompleted || isOffline()
                ? Promise.resolve(null)
                : this.taskRepository.ensureUnplannedWorkflowTask(task.taskHandle);
        // Always refresh workflow and referral afterwards

        return claimQ.then(() => this.reload());
    }

    /**
     * @Deprecated This is the NON-COMMAND based approach. See EditTaskCommand in ts, and ReferralTaskStatusCommandViewModel.java
     */
    markComplete(task: TaskDto) {
        const legacy = task.assignedTo == "possibly complete";
        const markCompleteQ = legacy
            ? this.claimTask(task).then(() =>
                  this.taskRepository.postMarkCompleted(task.taskHandle)
              )
            : this.taskRepository.postMarkCompleted(task.taskHandle);

        const doneQ = task.isCompleted || isOffline() ? Promise.resolve(null) : markCompleteQ;
        doneQ.then(() => this.reload());
    }
}
