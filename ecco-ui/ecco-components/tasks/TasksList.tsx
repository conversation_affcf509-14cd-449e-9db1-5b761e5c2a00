import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {Box, Button} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {useState} from "react";
import {useHistory} from "react-router";
import {SchemaList} from "../json-schema-form/SchemaList";
import {TaskStatusRowDto} from "./task-dtos";



export function TasksList () {
    const [search, setSearch] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});
    const [editing, setEditing] = useState(false);
    const [taskHandle, setTaskHandle] = useState<string | undefined>();

    const params = new URLSearchParams();
    if (search) params.append("search", search);
    if (page) params.append("page", page.toFixed());
    const filterKeys = Object.keys(filters);
    filterKeys.forEach(key => filters[key].forEach(val => params.append(key, val)));
    if (search || filterKeys.length) {
        params.append("enabled", "false")
    }
    const paramStr = params.toString();
    const src = paramStr.length ? `task-status/?${paramStr}` : "task-status/";

    const history = useHistory();

    return <Box m={1}>
        <Box my={1}>
            {/*TODO: Move button into top bar + as on rota*/}
            <Button key="new-task" variant="outlined" onClick={() => {
                setEditing(true);
                setTaskHandle(undefined);
            }}>new task</Button>
        </Box>
        <SchemaList
            title="tasks"
            src={src}
            displayFields={["taskStatusId","description","dueDate","assignedUser"]}
            filterFields={[]}
            filters={filters}
            onFilter={filters => {
                setPage(0);
                setFilters(filters);
            }}
            page={page}
            onPage={setPage}
            onSearch={search => {
                setPage(0);
                setSearch(search);
            }}
            searchText={search}
            onRowClick={task => history.push("./task-status/" + (task as TaskStatusRowDto).taskStatusId) }
        />
        {/*<UserForm show={editing} setShow={setEditing} username={username} notifyNewUsername={setSearch}/>*/}
    </Box>;
}