{
  "extends": "../tsconfig.ecco-module.json",
  "compilerOptions": {
    "lib": ["es2017.object", "es2015", "dom"],
    "outDir": ".",
    "baseUrl": "",
    "declarationDir": "./_typings",
    "types": ["@types/jest", "application-properties", "ecco-spa-global"]
    //"jsx": "react-jsx" // avoids writing import * as React from "react";" in every file
  },
  "references": [
    {"path": "../ecco-commands/tsconfig.json"},
    {"path": "../ecco-components-core/tsconfig.json"},
    {"path": "../ecco-dto/tsconfig.json"},
    {"path": "../ecco-forms/tsconfig.json"},
    {"path": "../ecco-offline-data/tsconfig.json"}
  ],
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["cypress", "debug", "dist", "_typings/**", "__tests__"] // Technically shouldn't need to specify debug and dist as they don't have *.tsx? in
}
