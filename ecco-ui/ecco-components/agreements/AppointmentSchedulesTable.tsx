import {EccoDate, EccoTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {IconButton} from "@eccosolutions/ecco-mui";
import {createStyles} from "@eccosolutions/ecco-mui";
import {Theme} from "@eccosolutions/ecco-mui";
import {Table} from "@eccosolutions/ecco-mui";
import {TableBody} from "@eccosolutions/ecco-mui";
import {TableCell} from "@eccosolutions/ecco-mui";
import {TableRow} from "@eccosolutions/ecco-mui";
import DeleteIcon from "@material-ui/icons/Delete";
import EditIcon from "@material-ui/icons/Edit";
import PersonIcon from "@material-ui/icons/Person";
import {makeStyles} from "@eccosolutions/ecco-mui";
import {ServiceRecipientAppointmentScheduleCommand} from "ecco-commands";
import {Agreement, ScheduleData} from "ecco-rota";
import {DemandScheduleDto, frequencyTypeDisplay} from "ecco-dto";
import * as React from "react";
import {useServicesContext} from "../ServicesContext";
import {CronScheduleStatus} from "./CronScheduleStatus";
import ExpandMore from "@material-ui/icons/ExpandMore";
import {useToggleList} from "../hooks";

interface ScheduleProps {
    hierarchy: number;
    schedule: DemandScheduleDto;
    scheduleChildren: DemandScheduleDto[];
    schedulesPrevious?: DemandScheduleDto[] | undefined;
    srId: number;
    agreement: Agreement;
    editScheduleCallback: (
        onSave: () => void,
        srId: number,
        agreement: Agreement,
        schedule: DemandScheduleDto
    ) => void;
    reloadSchedule: (scheduleRef: string) => void;
    onClickExpander?: (() => void) | undefined;
    expanded?: boolean | undefined;
}

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        table: {
            minWidth: 800,
            width: "100%"
        },
        muted: {
            opacity: 0.5
        }
    })
);

const childSchedulesByParentId = (id: number, agreement: Agreement) =>
    agreement.getDemandSchedules().filter(s => s.parentScheduleId == id);

function ScheduleTableRow(props: ScheduleProps): React.ReactElement {
    const classes = useStyles();
    const {schedule} = props;
    const {getCommandRepository, sessionData} = useServicesContext();

    function openEditSchedule() {
        props.editScheduleCallback(() => props.reloadSchedule(schedule.eventRef), props.srId, props.agreement, schedule);
    }

    function deleteSchedule() {
        if (window.confirm("Are you sure you want to delete this schedule?")) {
            const cmd = new ServiceRecipientAppointmentScheduleCommand(
                "remove",
                Uuid.randomV4(),
                props.srId,
                schedule.eventRef,
                props.agreement.getAgreementId()
            );
            getCommandRepository()
                .sendCommand(cmd)
                .then(() => props.reloadSchedule(schedule.eventRef)); // TODO: This should cause it to disappear.
        }
    }

    const muiTableOpacity = 0.75;

    return (
        <>
            <TableRow
                style={props.hierarchy > 0 ? {backgroundColor: "#eee"} : {}}
                className={
                    schedule.end < EccoDate.todayLocalTime().formatIso8601() ? classes.muted : ""
                }
            >
                <TableCell>{schedule.title}</TableCell>
                <TableCell>
                    {schedule.durationMins} {"mins"}
                </TableCell>
                {/* see also the other files in this commit msg */}
                {/*for appointment schedules, this categoryId is from 'appointmenttypes', for a resource, its a listDef */}
                {/*but this is exactly what the title is, so we leave this categoryId*/}
                {/*<TableCell>{props.sessionData && schedule.categoryId}</TableCell>*/}
                <TableCell>
                    <CronScheduleStatus
                        allowMultipleTimes={true}
                        readOnly={true}
                        title={"agreement"}
                        instanceState={ScheduleData.fromDemandSchedule(schedule)}
                        withStartEnd={true}
                        onChange={(_scheduleData: ScheduleData) => {}}
                    />
                    {frequencyTypeDisplay(schedule.intervalType, schedule.intervalFrequency)}
                    <PersonIcon
                        style={{
                            verticalAlign: "middle",
                            paddingLeft: "5px",
                            opacity: muiTableOpacity
                        }}
                    />
                    {props.scheduleChildren ? props.scheduleChildren.length + 1 : 1}
                </TableCell>
                <TableCell>
                    <IconButton color="primary" size="small" onClick={() => openEditSchedule()}>
                        <EditIcon />
                    </IconButton>
                    {sessionData.hasRoleSysAdmin() && (
                        <IconButton size={"small"} onClick={() => deleteSchedule()}>
                            <DeleteIcon />
                        </IconButton>
                    )}
                    <small>{`[s-id ${schedule.scheduleId}]`}</small>
                </TableCell>
                <TableCell colSpan={1}>
                    {props.schedulesPrevious && (
                        <IconButton onClick={props.onClickExpander}>
                            <ExpandMore />
                        </IconButton>
                    )}
                </TableCell>
            </TableRow>
            {props.expanded && (
                <>
                    {props.schedulesPrevious?.map((sched, _index) => {
                        return (
                            <ScheduleTableRow
                                key={sched.eventRef}
                                hierarchy={1}
                                schedule={sched}
                                scheduleChildren={childSchedulesByParentId(
                                    sched.scheduleId,
                                    props.agreement
                                )}
                                srId={props.agreement.getServiceRecipientId()}
                                agreement={props.agreement}
                                editScheduleCallback={props.editScheduleCallback}
                                reloadSchedule={props.reloadSchedule}
                            />
                        );
                    })}
                    {/* create a gap to the next row - margins / padding etc gets hidden */}
                    <TableRow>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                    </TableRow>
                </>
            )}
        </>
    );
}

function sortSchedule(a: DemandScheduleDto, b: DemandScheduleDto) {
    // if ended before today, order by end (we can use '<' since its natural ordering iso yyyy-mm-dd
    const aEndedBeforeToday = a.end && a.end < EccoDate.todayLocalTime().formatIso8601();
    const bEndedBeforeToday = b.end && b.end < EccoDate.todayLocalTime().formatIso8601();
    const bothNotEnded = !a.end && !b.end;
    const ended = bothNotEnded
        ? 0 // 0, move on to match on days if both not ended
        : aEndedBeforeToday && bEndedBeforeToday
        ? a < b
            ? 1
            : -1 // b < a, if both ended, decide which one is first, if a earlier push a down
        : aEndedBeforeToday
        ? 1 // 1, if only a ended - push a down
        : bEndedBeforeToday
        ? // -1, if only b ended - push a up
          -1
        : // 0, if both ended after today - move on to match on days
          0;
    if (ended != 0) {
        return ended;
    }

    const days = b.calendarDays[0]! - a.calendarDays[0]!;
    if (days != 0) {
        return days > 0 ? -1 : 1;
    }
    const time =
        EccoTime.parseIso8601(b.time).millisecondsSinceDayStart() -
        EccoTime.parseIso8601(a.time).millisecondsSinceDayStart();
    return time == 0 ? 0 : time > 0 ? -1 : 1;
}

function schedulesSplitFrom(agreement: Agreement, previousScheduleId: number): DemandScheduleDto[] {
    return agreement
        .getDemandSchedules()
        .filter(s => s.scheduleId == previousScheduleId)
        .map(s => {
            const from = schedulesSplitFrom(agreement, s.previousScheduleId);
            return [s, ...from];
        })
        .reduce((r, x) => r.concat(x), []); // flatMap;
}

export function AppointmentSchedulesTable(props: {
    agreement: Agreement;
    editScheduleCallback: (
        onSave: () => void,
        srId: number,
        agreement: Agreement,
        schedule?: DemandScheduleDto | undefined
    ) => void;
    reloadSchedule: (scheduleRef: string) => void;
}) {
    const classes = useStyles();
    const {agreement} = props;
    const [expandedRows, toggleRow] = useToggleList();
    function isExpanded(index: number): boolean {
        const foundIndex = expandedRows.indexOf(index);
        return foundIndex > -1;
    }

    // we want parents (not those with a parent), but also with a previous schedule (if one exists) - to indicate we're a later version
    const previousScheduleIds = agreement.getDemandSchedules().map(s => s.previousScheduleId);
    const parentSchedulesWithPreviousOrNone = agreement
        .getDemandSchedules()
        .filter(s => !s.parentScheduleId && previousScheduleIds.indexOf(s.scheduleId) == -1);
    const sortedSchedules = parentSchedulesWithPreviousOrNone.sort((a, b) => sortSchedule(a, b));

    return (
        <Table className={classes.table}>
            <TableBody>
                {sortedSchedules.map((sched, index) => {
                    return (
                        <ScheduleTableRow
                            key={sched.eventRef}
                            hierarchy={0}
                            schedule={sched}
                            scheduleChildren={childSchedulesByParentId(
                                sched.scheduleId,
                                props.agreement
                            )}
                            schedulesPrevious={schedulesSplitFrom(
                                agreement,
                                sched.previousScheduleId
                            )}
                            srId={agreement.getServiceRecipientId()}
                            agreement={agreement}
                            editScheduleCallback={props.editScheduleCallback}
                            reloadSchedule={props.reloadSchedule}
                            onClickExpander={() => toggleRow(index)}
                            expanded={isExpanded(index)}
                        />
                    );
                })}
            </TableBody>
        </Table>
    );
}