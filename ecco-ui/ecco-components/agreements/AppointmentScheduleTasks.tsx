import {
    Avatar,
    Checkbox,
    FormControlLabel,
    Grid,
    IconButton,
    List,
    ListItem,
    ListItemAvatar,
    ListItemSecondaryAction,
    ListItemText,
    TextField,
    Typography
} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {DemandScheduleTaskDto} from "ecco-rota";
import AddIcon from "@material-ui/icons/Add";
import AssignmentOutlined from "@material-ui/icons/AssignmentOutlined";
import {Autocomplete} from "@eccosolutions/ecco-mui";
import {stringFromHtmlInput} from "ecco-components-core";
import {useServiceRecipient} from "../data/entityLoadHooks";
import {useServicesContext} from "../ServicesContext";
import {EccoModal, Footer} from "ecco-components-core";
import {useEffect, useState} from "react";
import {SelectListOption} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Action} from "ecco-dto";
import EditOutlined from "@material-ui/icons/EditOutlined";

export interface DemandScheduleTaskDtoDisable extends DemandScheduleTaskDto {
    delete: boolean;
}

interface Props {
    serviceRecipientId: number;
    tasks: DemandScheduleTaskDtoDisable[];
    taskUpdate: (task: DemandScheduleTaskDtoDisable) => void;
}

export function AppointmentScheduleTasks(props: Props) {
    const [showInstanceId, setShowInstanceId] = useState<string | null>(null);
    const onUpdate = (task: DemandScheduleTaskDtoDisable) => {
        setShowInstanceId(null);
        props.taskUpdate(task);
    };
    return (
        <>
            <Typography
                /*className={classes.title}*/
                color="textSecondary"
                style={{paddingTop: "25px"}}
            >
                planned tasks:
            </Typography>
            <IconButton
                color="primary"
                id={"eccotest-add"}
                onClick={() => setShowInstanceId("new")}
            >
                <AddIcon />
            </IconButton>
            {showInstanceId && (
                <AppointmentScheduleTaskLinkedDirectlyEdit
                    task={
                        showInstanceId == "new"
                            ? null
                            : props.tasks.filter(t => t.taskInstanceId == showInstanceId).pop()!
                    }
                    serviceRecipientId={props.serviceRecipientId}
                    save={onUpdate}
                    cancel={() => setShowInstanceId(null)}
                />
            )}
            <List>
                {props.tasks?.map(t => (
                    <React.Fragment key={`sched-task-${t.taskInstanceId}`}>
                        <AppointmentScheduleTaskLinkedDirectly
                            task={t}
                            edit={taskInstanceId => setShowInstanceId(taskInstanceId)}
                        />
                    </React.Fragment>
                ))}
            </List>
        </>
    );
}

/**
 * List the tasks for this schedule.
 * A schedule can be independent to the tasks - the tasks don't even show on the schedule in that case, only in
 * the 'care plan' configured evidence page and the app - which loads from that page using the target dates.
 * This work shows the tasks directly linked to the schedule, and always show in the app when the schedule does.
 * See also CareTaskAsListItem.tsx
 */
function AppointmentScheduleTaskLinkedDirectly(props: {
    task: DemandScheduleTaskDtoDisable;
    edit: (taskInstanceId: string) => void;
}) {
    const task = props.task;

    const opacity = task.delete ? 0.75 : 1;
    return (
        <ListItem style={task.delete ? {backgroundColor: "#ffdddc", opacity: opacity} : {}}>
            <ListItemAvatar>
                <Avatar>
                    <AssignmentOutlined />
                </Avatar>
            </ListItemAvatar>
            <ListItemText
                primary={<Typography>{task.taskText || task.taskDefName}</Typography>}
                secondary={<Typography variant="caption">{task.taskDescription}</Typography>}
                disableTypography
            ></ListItemText>
            <ListItemSecondaryAction>
                <IconButton onClick={() => props.edit(task.taskInstanceId)}>
                    <EditOutlined className={"eccotest-pencil"} />
                </IconButton>
            </ListItemSecondaryAction>
        </ListItem>
    );
}

interface PropsEdit {
    task: DemandScheduleTaskDtoDisable | null;
    cancel: () => void;
    save: (task: DemandScheduleTaskDtoDisable) => void;
    serviceRecipientId: number;
}

/**
 * Popup for editing a task
 */
function AppointmentScheduleTaskLinkedDirectlyEdit(props: PropsEdit) {
    const [category, setCategory] = useState<SelectListOption | null>(null);
    const [name, setName] = useState<string | null>(null);
    const [description, setDescription] = useState<string | null>(null);
    const [disable, setDisable] = useState<boolean>(false);
    const [valid, setValid] = useState<boolean>(false);

    const {sessionData} = useServicesContext();
    const {serviceRecipient} = useServiceRecipient(props.serviceRecipientId);

    const actionToDdl = (a: Action) => {
        const ddl: SelectListOption = {
            id: a.getId(),
            name: a.getName(),
            disabled: a.isDisabled()
        };
        return ddl;
    };

    useEffect(() => {
        if (props.task && serviceRecipient && sessionData) {
            setCategory(actionToDdl(sessionData.getSupportActionById(props.task.taskDefId)));
            setName(props.task.taskText);
            setDescription(props.task.taskDescription);
            setDisable(props.task.delete);
        }
    }, [serviceRecipient, sessionData]);

    useEffect(() => {
        setValid(category != null);
    }, [category]);

    // NB 'carePlan' is now hard-coded data
    // categories are ordered according using @Orderby
    const categories =
        sessionData &&
        serviceRecipient &&
        sessionData
            .getServiceTypeByServiceCategorisationId(serviceRecipient.serviceAllocationId)
            .getOutcomesForTaskName("carePlan", sessionData)
            .map(o => o.getActionGroups())
            .reduce((r, x) => r.concat(x), []) // flatMap
            .map(ag => ag.getActions())
            .reduce((r, x) => r.concat(x), []) // flatMap
            .map(a => actionToDdl(a));

    const onSave = () => {
        const t: DemandScheduleTaskDtoDisable = {
            taskDefId: +category!.id,
            taskDefName: category!.name,
            taskInstanceId: props.task ? props.task.taskInstanceId : Uuid.randomV4().toString(),
            taskText: name!,
            taskDescription: description,
            delete: disable
        };
        props.save(t);
    };

    const footer = (
        <Footer
            onCancel={props.cancel}
            onSave={onSave}
            saveEnabled={valid}
            action={props.task == null ? "add" : "update"}
        />
    );

    return (
        <EccoModal
            title={props.task == null ? "new task" : "edit task"}
            maxWidth="xs"
            footer={footer}
            show={true}
            onEscapeKeyDown={props.cancel}
        >
            <Grid container spacing={4}>
                <Grid item xs={12}>
                    <Autocomplete
                        id={"eccotest-category"}
                        disabled={props.task != null}
                        renderInput={params => (
                            // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                            <TextField {...params} label="task name" variant="outlined" />
                        )}
                        getOptionLabel={option => option.name}
                        getOptionSelected={(a, b) => a.id == b.id}
                        options={categories || []}
                        value={category}
                        onChange={(_event, category) => {
                            setCategory(category);
                        }}
                        /*style={{width: 270}}*/
                    />
                </Grid>
                <Grid item xs={12}>
                    <TextField
                        name={`task-name`}
                        label={"task name (alternative)"}
                        fullWidth={true}
                        placeholder={""}
                        required={false}
                        onChange={event => setName(stringFromHtmlInput(event.target))}
                        value={name}
                    />
                </Grid>
                <Grid item xs={12}>
                    <TextField
                        name={`task-description`}
                        label={"carer guidance"}
                        fullWidth={true}
                        placeholder={""}
                        required={false}
                        multiline={true}
                        onChange={event => setDescription(stringFromHtmlInput(event.target))}
                        value={description}
                    />
                </Grid>
                {/* only show if taskInstanceId has something - which it doesn't on 'add new' */}
                {props.task?.taskInstanceId && (
                    <Grid item xs={12}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name={`task-disable`}
                                    checked={disable}
                                    onChange={(_, checked) => {
                                        setDisable(checked);
                                    }}
                                />
                            }
                            label={"disable"}
                        />
                    </Grid>
                )}
            </Grid>
        </EccoModal>
    );
}
