import {EccoDate} from "@eccosolutions/ecco-common";
import {Grid} from "@eccosolutions/ecco-mui";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {CommandQueue, ServiceAgreementCommand} from "ecco-commands";
import {Agreement} from "ecco-rota";
import * as React from "react";
import {FC} from "react";
import {ContractSelect} from "../contracts/ContractSelector";
import {useServicesContext} from "../ServicesContext";
import {CommandForm, CommandSubform, ModalCommandForm} from "../cmd-queue/CommandForm";
import {EccoAPI} from "../EccoAPI";

type Props = {
    serviceRecipientId: number;
    agreementId?: number | undefined;
    show: boolean;
    setShow: (show: boolean) => void;
};

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

type State = {
    origAgreement?: Agreement | undefined;
    startDate?: EccoDate | null | undefined;
    endDate?: EccoDate | null | undefined;
    contractId?: number | null | undefined;
};

class EditServiceAgreementForm extends CommandSubform<Props & LocalProps, State> {
    constructor(props: Props & LocalProps) {
        super(props);
        this.state = {};
    }

    override componentDidMount() {
        super.componentDidMount();
        const {agreementId, services} = this.props;
        if (agreementId) {
            services.rotaRepository.findAgreementByAgreementId(agreementId).then(agreement => {
                this.setState({
                    origAgreement: agreement,
                    startDate: agreement.start,
                    endDate: agreement.end,
                    contractId: agreement.getContractId()
                });
            });
        }
    }

    getErrors(): string[] {
        return this.state.startDate ? [] : ["start date is required"];
    }

    emitChangesTo(cmdQ: CommandQueue) {
        let cmd;
        const {origAgreement, startDate, endDate, contractId} = this.state;
        if (origAgreement) {
            const {serviceRecipientId} = this.props;
            cmd = new ServiceAgreementCommand(
                "update",
                serviceRecipientId,
                origAgreement.getAgreementId()
            )
                .changeStartDate(origAgreement.start, startDate!)
                .changeEndDate(origAgreement.end, endDate!);
        } else {
            cmd = new ServiceAgreementCommand("add", this.props.serviceRecipientId)
                .changeStartDate(null, startDate!)
                .changeEndDate(null, endDate!);
        }
        cmd.withContractId(contractId);

        if (cmd.hasChanges()) {
            cmdQ.addCommand(cmd);
        }
    }

    override render() {
        const {startDate, endDate, contractId} = this.state;
        const readOnly = !!this.props.agreementId;
        return (
            <Grid container={true} justify={"center"}>
                <Grid item={true} lg={6} md={6} xs={8}>
                    <Grid container={true}>
                        <Grid item xs={12}>
                            <DatePickerEccoDate
                                name="start date"
                                label="start date"
                                onChange={startDate => this.setState({startDate})}
                                maxDate={endDate}
                                value={startDate!}
                                required
                                disabled={readOnly}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <DatePickerEccoDate
                                name="end date"
                                label="end date"
                                onChange={endDate => this.setState({endDate})}
                                minDate={startDate}
                                value={endDate || null}
                                disabled={readOnly}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <ContractSelect
                                contractId={contractId}
                                onChange={contractId => this.setState({contractId})}
                                disabled={false}
                            />
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        );
    }
}

/** @Exemplar */
export const EditServiceAgreementModal: FC<Props> = props => {
    const eccoAPI = useServicesContext();

    return props.show ? (
        <ModalCommandForm
            show={true}
            setShow={props.setShow}
            title={props.agreementId ? "edit schedule group" : "add new schedule group"}
            action={props.agreementId ? "update" : "save"}
            maxWidth="sm"
        >
            {form => <EditServiceAgreementForm {...props} services={eccoAPI} commandForm={form} />}
        </ModalCommandForm>
    ) : null;
};
