import {EccoDate} from "@eccosolutions/ecco-common";

import {
    createStyles,
    Fab,
    Grid,
    Hidden,
    IconButton,
    makeStyles,
    Table,
    TableBody,
    TableCell,
    TableRow,
    Theme
} from "@eccosolutions/ecco-mui";
import {AddIcon} from "@eccosolutions/ecco-mui-controls";
import EditIcon from "@material-ui/icons/Edit";
import ExpandMore from "@material-ui/icons/ExpandMore";

import {Agreement} from "ecco-rota";
import {DemandScheduleDto} from "ecco-dto";

import * as React from "react";
import {Reducer, useEffect, useReducer, useState} from "react";

import {useToggleList} from "../hooks";
import {useServicesContext} from "../ServicesContext";
import {AppointmentSchedulesTable} from "./AppointmentSchedulesTable";
import {EditScheduleModal} from "./EditScheduleForm";
import {EditServiceAgreementModal} from "./EditServiceAgreementForm";
import {EvidenceGroup, RateCardDto, ServiceRecipient} from "ecco-dto";
import {useDirectTasks} from "../data/entityLoadHooks";

// styles:
// https://material-ui.com/customization/components/#2-dynamic-variation-for-a-one-time-situation
// also typescript workarounds: https://material-ui.com/guides/typescript/
// also see "const useStyles = makeStyles((theme: Theme) =>", eg DateRangePicker.tsx
// also see 9d8d2697

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        table: {
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        },
        muted: {
            opacity: 0.5
        },
        inverseIcon: {
            // color: "white",
            // backgroundColor: "blue"
        },
        extendedIcon: {
            marginRight: theme.spacing(1),
        },
    })
);


function AgreementTableRow(props: {
        agreement: Agreement, onClickExpander: () => void, expanded: boolean,
        dispatchEdit: DispatchEdit,
        reloadSchedule: (scheduleRef: string) => void}) {

    const styles = useStyles();
    const {onClickExpander, expanded, agreement} = props;

    function openNewSchedule() {
        props.dispatchEdit({type: "editSchedule", agreement});
    }

    return (
        <>
            <TableRow
                className={
                    agreement.end && agreement.end.earlierThan(EccoDate.todayLocalTime())
                        ? styles.muted
                        : ""
                }
            >
                <TableCell colSpan={1}>
                    <small>[a-id {agreement.getAgreementId()}]</small>
                </TableCell>
                <TableCell colSpan={9}>
                    {agreement.getDateDescription()}
                    <br />
                    <small>{agreement.getContractName() ?? ""}</small>
                </TableCell>
                <TableCell colSpan={1}>
                    <IconButton
                        color="primary"
                        aria-label="edit"
                        size="small"
                        onClick={() => props.dispatchEdit({type: "editAgreement", agreement})}
                    >
                        <EditIcon />
                    </IconButton>
                    <IconButton color="primary" onClick={openNewSchedule}>
                        <AddIcon />
                    </IconButton>
                </TableCell>
                <TableCell colSpan={1}>
                    <IconButton onClick={onClickExpander}>
                        <ExpandMore />
                    </IconButton>
                </TableCell>
            </TableRow>
            {expanded && (
                <TableRow>
                    <TableCell colSpan={12}>
                        <AppointmentSchedulesTable
                            agreement={agreement}
                            editScheduleCallback={(_onSave, _srId, agreement, schedule) =>
                                props.dispatchEdit({type: "editSchedule", agreement, schedule})
                            }
                            reloadSchedule={props.reloadSchedule}
                        />
                    </TableCell>
                </TableRow>
            )}
        </>
    );
}

interface AgreementsProps {
    serviceRecipientId: number;
    // editScheduleCallback: (onSave: () => void, srId: number, agreement: Agreement, scheduleRef?: string, schedule?: DemandScheduleDto) => void;
}

interface EditState {
    agreement?: Agreement | null | undefined;
    schedule?: DemandScheduleDto | null | undefined;
    dialog: "agreement" | "schedule" | null;
    version: number;
}
interface EditAction {
    type: "close" | "editAgreement" | "editSchedule" | "reload";
    agreement?: Agreement | undefined;
    schedule?: DemandScheduleDto | undefined;
}

type AgreementReducer = Reducer<EditState, EditAction>;
type DispatchEdit = (action: EditAction) => void;

export function AgreementsView(props: AgreementsProps) {
    const classes = useStyles();
    const [rows, setRows] = useState<Agreement[]>([]);
    const [expandedRows, toggleRow] = useToggleList();
    const [editingScheduleRateCards, setEditingScheduleRateCards] = useState<RateCardDto[]>([]);
    const [editingServiceRecipient, setEditingServiceRecipient] = useState<ServiceRecipient | null>(
        null
    );

    const [state, dispatchEdit] = useReducer<AgreementReducer>(
        (prevState, action) => {
            if (action.type == "close" || action.type == "reload") {
                return {
                    agreement: null,
                    schedule: null,
                    dialog: null,
                    version: prevState.version + 1
                };
            } else if (action.type == "editAgreement") {
                return {
                    agreement: action.agreement,
                    dialog: "agreement",
                    version: prevState.version
                };
            } else if (action.type == "editSchedule") {
                return {
                    schedule: action.schedule,
                    agreement: action.agreement,
                    dialog: "schedule",
                    version: prevState.version
                };
            }
            return prevState;
        },
        {agreement: null, schedule: null, dialog: null, version: 0}
    );

    const services = useServicesContext();

    // when 'reload', the version changes, so we reload
    // NB the method includes reloading the schedules as the server side (AgreementController includes agreementResourceAssembler 'withSchedules')
    useEffect(() => {
        services.serviceRecipientRepository
            .findOneServiceRecipientById(props.serviceRecipientId)
            .then(sr => {
                setEditingServiceRecipient(sr);
                services.rotaRepository
                    .findAgreementsByServiceRecipientId(props.serviceRecipientId)
                    .then(a => setRows(a));
            });
    }, [state.version, props.serviceRecipientId]);

    // when 'reload', the schedule is null, so we reload extra information, like rate cards
    useEffect(() => {
        if (state.dialog == "schedule") {
            services.rotaRepository
                .findRateCardsForAgreement(state.agreement!.getAgreementId())
                .then(c => setEditingScheduleRateCards(c));
        }
    }, [state.schedule]);

    // when 'reload', the schedule is null, so we reload extra information, like directTasks
    const {directTasks} = useDirectTasks(
        state.schedule,
        props.serviceRecipientId,
        EvidenceGroup.needs
    );
    const loadedDirectTasks = !!directTasks; // NB !![] is true

    function isExpanded(index: number): boolean {
        const foundIndex = expandedRows.indexOf(index);
        return foundIndex > -1;
    }

    // noinspection JSUnusedLocalSymbols
    /*function reloadAgreement(agreementId?: number) {
        // reload all for now
        services.rotaRepository
            .findAgreementsByServiceRecipientId(props.serviceRecipientId)
            .then(a => setRows(a));
    }*/
    // noinspection JSUnusedLocalSymbols
    function reloadSchedule(_scheduleRef: string) {
        //dispatchEdit({type: "reload"})
        services.rotaRepository
            .findAgreementsByServiceRecipientId(props.serviceRecipientId)
            .then(a => setRows(a));
    }
    function newAgreement() {
        dispatchEdit({type: "editAgreement"});
    }

    const styles = useStyles();
    return (
        <>
            <Grid container>
                <Grid item xs={6} implementation="css" component={Hidden} />
                <Grid item xs={6} style={{textAlign: "right"}}>
                    {/* see also https://material-ui.com/components/icons/*/}
                    <Fab
                        size="small"
                        variant="extended"
                        // color="primary"
                        onClick={newAgreement}
                    >
                        new group
                        <AddIcon className={styles.extendedIcon} />
                    </Fab>
                </Grid>
                <Grid container justify={"center"}>
                    <Grid item xs={12}>
                        <div className={classes.tableWrapper}>
                            <Table className={classes.table}>
                                <TableBody>
                                    {rows &&
                                        rows.map((row, index: number) => (
                                            <AgreementTableRow
                                                key={row.getAgreementId() + "-" + state.version} // version added as HACK to force a redraw because props ain't cutting it
                                                agreement={row}
                                                dispatchEdit={dispatchEdit}
                                                reloadSchedule={scheduleRef =>
                                                    reloadSchedule(scheduleRef)
                                                }
                                                onClickExpander={() => toggleRow(index)}
                                                expanded={isExpanded(index)}
                                            />
                                        ))}
                                </TableBody>
                            </Table>
                        </div>
                    </Grid>
                </Grid>
            </Grid>
            {state.dialog == "agreement" && (
                <EditServiceAgreementModal
                    serviceRecipientId={props.serviceRecipientId}
                    agreementId={state.agreement?.getAgreementId()}
                    show={true}
                    setShow={() => dispatchEdit({type: "close"})}
                />
            )}
            {state.dialog == "schedule" && loadedDirectTasks && (
                <EditScheduleModal
                    serviceAllocationId={editingServiceRecipient?.serviceAllocationId || null}
                    serviceRecipientId={props.serviceRecipientId}
                    agreement={state.agreement!}
                    schedule={state.schedule!}
                    tasksDirect={directTasks}
                    editingScheduleRateCards={editingScheduleRateCards}
                    setShow={() => dispatchEdit({type: "close"})}
                />
            )}
        </>
    );
}

export default AgreementsView;
