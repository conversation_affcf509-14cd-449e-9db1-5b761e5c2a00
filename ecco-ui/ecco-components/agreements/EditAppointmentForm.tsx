import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandQueue, ServiceRecipientAppointmentScheduleCommand} from "ecco-commands";
import {Activity, DemandResource} from "ecco-rota";
import * as React from "react";
import {FC} from "react";
import {CommandForm, CommandSubform, ModalCommandForm} from "../cmd-queue/CommandForm";
import {useServicesContext} from "../ServicesContext";
import {AppointmentFields, AppointmentForm, ScheduleEvent} from "./AppointmentForm";
import {EccoAPI} from "../EccoAPI";

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    serviceId: number | null; // filter the resources to allocate to
    activity: Activity;
    resource?: DemandResource | undefined;
    setShow: (show: boolean) => void;
}
interface Props extends ModalProps {
    readOnly: boolean;
}

interface State {
    editedData: AppointmentFields
}


export class EditAppointmentCommandForm extends CommandSubform<Props & LocalProps, State> {
    private originalDurationMins: number;

    constructor(props: Props & LocalProps) {
        super(props);

        // should really call removePostSubmitHandler - like useCommandFormPostSubmitHandler
        props.commandForm.addPostSubmitHandler(() => ScheduleEvent.fire());

        this.originalDurationMins = this.props.activity
            .getEnd()!
            .subtractDateTime(this.props.activity.getStart())
            .inMinutes();

        this.state = {
            editedData: (props.activity
                ? {
                      date: props.activity.getStart().toEccoDate(),
                      time: props.activity.getStart().toEccoTime(),
                      durationMins: this.originalDurationMins,
                      title: props.activity.getTitle()
                  }
                : {}) as AppointmentFields
        };
    }

    getErrors(): string[] {
        const errors = [];
        const editedData = this.state.editedData;
        if (!editedData.time) {
            errors.push("you must provide a time");
        }
        if (!editedData.durationMins || editedData.durationMins < 5) {
            errors.push("duration should be at least 5 minutes");
        }
        return errors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const activity = this.props.activity;
        const editedData = this.state.editedData;
        const serviceRecipientId = activity.getServiceRecipientId()!;

        const initialDate = activity.getStart().toEccoDate();
        const initialTime = activity.getStart().toEccoTime();
        let cmd = new ServiceRecipientAppointmentScheduleCommand(
            "update",
            Uuid.randomV4(),
            serviceRecipientId,
            activity.getRef()!,
            undefined
        )
            .changeStartDate(
                initialDate.equals(editedData.date) ? null : initialDate,
                editedData.date
            ) // NOTE: null unless back end gets changed to reuse prev date
            .changeTime(
                initialTime.formatHoursMinutes() == editedData.time!.formatHoursMinutes()
                    ? null
                    : initialTime,
                editedData.time!
            ) // NOTE: null unless back end gets changed to reuse prev time
            .changeDurationMins(this.originalDurationMins, editedData.durationMins!)
            .changeTitle(activity.getTitle(), editedData.title!);

        if (this.props.resource) {
            cmd.withResourceSrId(this.props.resource.getServiceRecipientId());
        } else if (editedData.resourceSrId) {
            cmd.withResourceSrId(editedData.resourceSrId);
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    override render() {
        return (
            <AppointmentForm
                {...this.props}
                date={this.props.activity.getStart().toEccoDate()}
                data={this.state.editedData}
                onChange={editedData => this.setState({editedData})}
            />
        );
    }
}

/** @Exemplar */
export const EditAppointmentModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title="edit appointment"
            action="update"
            maxWidth="sm"
        >
            {form => <EditAppointmentCommandForm
                {...props}
                readOnly={!eccoAPI.sessionData.hasRoleReferralEdit()}
                services={eccoAPI}
                commandForm={form}
            />}
        </ModalCommandForm>
    );
};
