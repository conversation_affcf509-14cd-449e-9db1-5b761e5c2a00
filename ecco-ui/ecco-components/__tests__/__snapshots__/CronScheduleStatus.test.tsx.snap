// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CronScheduleStatus should show multiple times daily with end date weekly 1`] = `
<span
  className="target-schedule"
  onClick={[Function]}
  title="test 2:  10:00,22:15 (every day from 21 June 2021 until 30 June 2022)"
>
  <i
    className="clickable-image fa fa-clock-o fa-lg active"
    style={
      {
        "opacity": 0.75,
      }
    }
  />
  <span>
     10:00,22:15 (every day)
  </span>
</span>
`;

exports[`CronScheduleStatus should show single time with <PERSON><PERSON>,Thurs every 2 weeks 1`] = `
<span
  className="target-schedule"
  onClick={[Function]}
  title="test:  10:00 (on Tues, Thurs every 2 weeks from 31 May 2021)"
>
  <i
    className="clickable-image fa fa-clock-o fa-lg active"
    style={
      {
        "opacity": 0.75,
      }
    }
  />
  <span>
     10:00 (on <PERSON><PERSON>, Thurs every 2 weeks)
  </span>
</span>
`;
