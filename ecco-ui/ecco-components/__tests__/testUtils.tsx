import {
    ActionDto,
    ActionGroupDto,
    AppointmentTypeDto,
    ConfigResolverDefault,
    Feature,
    Messages,
    OutcomeDto,
    PersonUserSummary,
    ServiceCategorisation,
    ServiceDto,
    ServiceParametersDto,
    ServiceType,
    ServiceTypeDto,
    SessionData,
    SessionDataDto,
    SoftwareModule,
    StaffDto,
    TaskDefinition,
    UserSessionDataDto
} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {StringToObjectMap} from "@eccosolutions/ecco-common";

export const action1: ActionDto = {
    id: 1,
    //uuid?: string;
    name: "action1",
    disabled: false,
    activityTypes: [],
    orderby: 1,
    initialText: null
    //statusChangeReasonListName?: string;
};
export const action2: ActionDto = {
    id: 2,
    name: "Clean bedroom",
    disabled: false,
    activityTypes: [],
    orderby: 1,
    initialText: null
};
export const actiongroup1: ActionGroupDto = {
    id: 1,
    uuid: Uuid.randomV4().toString(),
    name: "actiongroup1",
    orderby: 1,
    disabled: false,
    actions: [action1, action2]
};
export const outcome1: OutcomeDto = {
    id: 1,
    uuid: Uuid.randomV4().toString(),
    name: "be healthy",
    disabled: false,
    actionGroups: [actiongroup1]
};

const serviceTypeDto: ServiceTypeDto = {} as ServiceTypeDto;
serviceTypeDto.supportOutcomes = [outcome1];
serviceTypeDto.riskAreas = [];
serviceTypeDto.questionGroups = [];
serviceTypeDto.taskDefinitionEntries = [];
export const serviceType = new ServiceType(serviceTypeDto, {} as Messages, () => outcome1);

export const configResolver = () =>
    ConfigResolverDefault.fromLegacyServiceType(serviceType, sessionData);

const sessionDataDto: SessionDataDto = {} as SessionDataDto;
const featureDirectTasks: Feature = {
    name: "rota.directTasks",
    description: "",
    defaultVote: "ENABLED_BY_DEFAULT"
};
const featureScheduler: Feature = {
    name: "rota.scheduler",
    description: "",
    defaultVote: "ENABLED_BY_DEFAULT"
};
const featureVotes: StringToObjectMap<Feature> = {};
featureVotes["rota.directTasks"] = featureDirectTasks;
featureVotes["rota.scheduler"] = featureScheduler;
sessionDataDto.featureSets = {global: {featureVotes: featureVotes}};

const rotaModule: SoftwareModule = {
    name: "rota",
    enabled: true
};
const softwareModules: StringToObjectMap<SoftwareModule> = {};
softwareModules["rota"] = rotaModule;
sessionDataDto.softwareModulesEnabled = softwareModules;

sessionDataDto.settings = {};
sessionDataDto.settings["com.ecco.rota:care.scheduler.days"] = "35";
sessionDataDto.appointmentTypes = [{id: 99, name: "apt type99"} as AppointmentTypeDto];

sessionDataDto.listDefinitions = {};
const taskDefRotaVisit: TaskDefinition = {
    id: 1,
    type: "EVIDENCE_SUPPORT",
    name: "rotaVisit",
    description: "does a rota visit",
    display: true,
    displayOverview: true,
    internal: false,
    metadata: {}
};
sessionDataDto.taskDefinitions = [taskDefRotaVisit];
sessionDataDto.serviceTypesById = {};
sessionDataDto.serviceTypesById["1"] = serviceTypeDto;

const service1: ServiceDto = {
    id: 1,
    serviceTypeId: 1,
    name: "service 1",
    projects: [],
    parameters: {} as ServiceParametersDto,
    disabled: false
};
const serviceCat1: ServiceCategorisation = {
    id: 1,
    serviceId: 1,
    projectId: null,
    disabled: false,
    serviceName: "service1",
    buildingId: 1065 // buildingTestData[0].buildingId
};
sessionDataDto.services = [service1];
sessionDataDto.serviceCategorisations = [serviceCat1];
sessionDataDto.supportOutcomes = [outcome1];
sessionDataDto.listDefinitions["care-checks"] = [
    {
        id: 188,
        name: "complete",
        listName: "care-checks",
        businessKey: "188",
        disabled: false,
        defaulted: false,
        order: null,
        // NB isIconClassSuccess checks for 'fa-check-circle'
        metadata: {iconClasses: "fa-check-circle", value: "0", displayName: ""}
    },
    {
        id: 190,
        name: "not complete",
        listName: "care-checks",
        businessKey: "190",
        disabled: false,
        defaulted: false,
        order: null,
        metadata: {iconClasses: "fa-check-times", value: "1", displayName: ""}
    },
    {
        id: 191,
        name: "tenant not home",
        listName: "care-checks",
        businessKey: "191",
        disabled: false,
        defaulted: false,
        order: null,
        metadata: {iconClasses: "fa-check-times", value: "2", displayName: ""}
    },
    {
        id: 192,
        name: "tenant on holiday",
        listName: "care-checks",
        businessKey: "192",
        disabled: true,
        defaulted: false,
        order: null,
        metadata: {iconClasses: "fa-check-times", value: "3", displayName: ""}
    }
];
sessionDataDto.listDefinitions["care-locations"] = [
    {
        id: 200,
        name: "seen in property",
        listName: "care-locations",
        businessKey: "200",
        disabled: false,
        defaulted: false,
        order: null
    },
    {
        id: 201,
        name: "left card",
        listName: "care-locations",
        businessKey: "201",
        disabled: false,
        defaulted: false,
        order: null
    }
];
sessionDataDto.listDefinitions["chargeNames"] = [
    {
        id: 219,
        name: "service charges",
        listName: "chargeNames",
        businessKey: "219",
        disabled: false,
        defaulted: false,
        order: null
    }
];

sessionDataDto.appointmentTypes = [
    {
        id: 109,
        name: "one-to-one",
        service: "service name",
        serviceId: 99,
        recommendedDurationInMinutes: 7,
        parameters: {colourCssValue: null}
    }
];

export const carer1: PersonUserSummary = {
    individualId: 1,
    displayName: "my name",
    calendarId: "calId"
} as PersonUserSummary;
export const carer2: PersonUserSummary = {
    individualId: 2,
    displayName: "my other friend",
    calendarId: "calId2"
} as PersonUserSummary;

const person1: PersonUserSummary = {
    individualId: 1,
    displayName: "my name",
    calendarId: "calId"
} as PersonUserSummary;
sessionDataDto.individualUserSummary = person1;

const userSession: UserSessionDataDto = {
    username: "mystaff",
    restrictedServiceCategorisations: [serviceCat1]
} as UserSessionDataDto;

export const sessionData = new SessionData({...sessionDataDto, ...userSession});

export const testStaff: StaffDto[] = [
    {
        workerId: 100002,
        contactId: 100003,
        /** firstname lastname separated by a space, where both exist, otherwise whichever exists */
        displayName: "Jim Staff",
        /** The UUID of this person's calendar */
        calendarId: "12345-calId",
        jobs: [
            {
                id: 100,
                workerId: 100002,
                code: null,
                contractedWeeklyHours: null,
                /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
                startDate: null,
                /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
                endDate: null,
                serviceRecipient: {serviceRecipientId: 99}
            }
        ]
    },
    {
        workerId: 100159,
        contactId: 100160,
        /** firstname lastname separated by a space, where both exist, otherwise whichever exists */
        displayName: "Jo Test",
        /** The UUID of this person's calendar */
        calendarId: "23456-calId",
        jobs: [
            {
                id: 101,
                workerId: 100159,
                code: null,
                contractedWeeklyHours: null,
                /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
                startDate: null,
                /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
                endDate: null,
                serviceRecipient: {serviceRecipientId: 98}
            }
        ]
    },
    {
        workerId: 100019,
        contactId: 100020,
        /** firstname lastname separated by a space, where both exist, otherwise whichever exists */
        displayName: "Margaret Davis",
        /** The UUID of this person's calendar */
        calendarId: "34566-calId",
        jobs: [
            {
                id: 102,
                workerId: 100019,
                code: null,
                contractedWeeklyHours: null,
                /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
                startDate: null,
                /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
                endDate: null,
                serviceRecipient: {serviceRecipientId: 97}
            }
        ]
    }
] as any as StaffDto[];