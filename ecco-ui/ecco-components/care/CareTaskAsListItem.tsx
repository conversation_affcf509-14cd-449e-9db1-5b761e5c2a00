import {CareTask, useCareVisitContext} from "./careVisitState";
import {
    AssignmentOutlined,
    CancelOutlined,
    CheckCircleOutline
} from "@eccosolutions/ecco-mui-controls";
import * as React from "react";
import {
    Avatar,
    IconButton,
    ListItem,
    ListItemAvatar,
    ListItemSecondaryAction,
    ListItemText,
    Typography
} from "@eccosolutions/ecco-mui";
import {useServicesContext} from "../ServicesContext";
import {EccoDateTime} from "@eccosolutions/ecco-common";

export const TaskUntouched = (
    props?: {fontSize?: "inherit" | "default" | "small" | "large" | undefined} | undefined
    // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
) => <CheckCircleOutline style={{fill: "#bbb"}} {...props} />;
export const TaskCompletedSuccess = (
    props?:
        | {
              fontSize?: "inherit" | "default" | "small" | "large" | undefined;
          }
        | undefined
    // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
) => <CheckCircleOutline style={{fill: "#3a3"}} {...props} />;
export const TaskCompletedFail = (
    props?: {fontSize?: "inherit" | "default" | "small" | "large" | undefined} | undefined
    // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
) => <CancelOutlined style={{fill: "#a33"}} {...props} name={"close-icon"} />;

export const CareTaskAsListItem = (props: {item: CareTask; editable: boolean}) => {
    const {sessionData} = useServicesContext();
    const {dispatch} = useCareVisitContext();

    const listDefinitionEntries = sessionData.getListDefinitionEntriesByListName(
        "care-checks",
        false,
        true
    );
    // this may not exist in test data, but is baked into our init data
    const firstSuccess = listDefinitionEntries.filter(l => l.isIconClassSuccess())?.pop();

    const toggleSuccessItem = () => {
        if (props.editable) {
            const value =
                props.item.listDefId == firstSuccess?.getId()
                    ? null
                    : firstSuccess?.getId() || null;
            dispatch({
                type: "changeTaskOutcome",
                task: props.item,
                value: value,
                dispatch: dispatch
            });
        }
    };

    const dueTime = props.item.taskTime; // 8:30 shows as due at 9:30 UTC+1 in jsDate but that's *not* relevant
    const overdue = dueTime && EccoDateTime.nowLocalTime().laterThan(dueTime);
    const dueNow =
        !overdue && dueTime && EccoDateTime.nowLocalTime().laterThan(dueTime.subtractMinutes(30));

    const dueText = overdue ? " (overdue)" : dueNow ? " (due now)" : "";

    const listDefinitionEntry = props.item.listDefId
        ? sessionData.getListDefinitionEntryById(props.item.listDefId)
        : null;
    return (
        <ListItem onClick={toggleSuccessItem}>
            <ListItemAvatar>
                <Avatar>
                    <AssignmentOutlined />
                </Avatar>
            </ListItemAvatar>
            <ListItemText
                primary={
                    <>
                        <Typography>{props.item.taskText}</Typography>
                        {props.item.taskDescription && (
                            <Typography variant={"caption"}>
                                {props.item.taskDescription}
                            </Typography>
                        )}
                    </>
                }
                secondary={
                    <>
                        {listDefinitionEntry ? (
                            <Typography>({listDefinitionEntry.getDisplayName()})</Typography>
                        ) : (
                            <Typography
                                style={overdue ? {color: "red"} : dueNow ? {color: "orange"} : {}}
                            >
                                {props.item.taskTime && props.item.taskTime.formatShort() + dueText}
                            </Typography>
                        )}
                    </>
                }
                disableTypography
            >
                {/*<span style={{color: overdue ? "red" : dueNow ? "orange" : undefined}}>{props.item.taskText}</span>*/}
            </ListItemText>
            {/* change text colour with disableTypography <Typography style={{ color: '#a00' }}>{props.taskText}</Typography>*/}
            {props.editable || listDefinitionEntry ? (
                <ListItemSecondaryAction>
                    <IconButton onClick={toggleSuccessItem}>
                        {listDefinitionEntry ? (
                            listDefinitionEntry.isIconClassSuccess() ? (
                                <TaskCompletedSuccess />
                            ) : (
                                <TaskCompletedFail />
                            )
                        ) : (
                            <TaskUntouched />
                        )}
                    </IconButton>
                </ListItemSecondaryAction>
            ) : null}
        </ListItem>
    );
};