import {FormControlLabel, Radio, RadioGroup} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {ListDefinitionEntry} from "ecco-dto";


export const ListDefRadioGroup = (props: {
    value: number | null;
    list: ListDefinitionEntry[];
    onChange: (value: string) => void;
}) => (
    <RadioGroup
        name="stopReason"
        data-test={"stopReason"}
        value={props.value === null ? null : props.value.toString()} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
        onChange={(_, value) => props.onChange(value)}
        style={{paddingLeft: "60px", paddingTop: "20px"}}
    >
        {props.list.map(l => (
            <FormControlLabel
                key={l.getId()}
                value={l.getId().toString()}
                control={<Radio />}
                label={l.getName()}
            />
        ))}
    </RadioGroup>
);
