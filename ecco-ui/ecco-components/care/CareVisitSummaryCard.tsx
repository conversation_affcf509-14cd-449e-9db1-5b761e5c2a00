import * as React from "react";
import {useCareVisitContext} from "./careVisitState";
import {CareVisitOverview} from "./CareVisitOverview";
import {AsyncServiceRecipientWithEntities} from "../data/serviceRecipientHooks";
import {CommandForm} from "../cmd-queue/CommandForm";
import {handleLazy} from "../Loading";
import {CareVisitDetail} from "./CareVisitDetail";
import {TaskNames} from "ecco-dto";
const ClientDetailEditor = React.lazy(() =>
    import("../clientdetails/ClientDetailForm").then(i => ({default: i.ClientDetailEditor}))
);

export function CareVisitSummaryCard() {
    const {state, dispatch} = useCareVisitContext();
    return (
        <>
            <CareVisitOverview />
            {state.showVisit && (
                /* show the visit */
                <CareVisitDetail />
            )}
            {state.showClientDetails && (
                <CommandForm
                    onCancel={() => dispatch({type: "hideClientDetails"})}
                    onFinished={() => dispatch({type: "hideClientDetails"})}
                >
                    <AsyncServiceRecipientWithEntities srId={state.serviceRecipientId}>
                        {handleLazy(
                            <ClientDetailEditor
                                serviceRecipientId={state.serviceRecipientId}
                                taskName={TaskNames.clientWithContact}
                                showCode={false}
                                formRef={() => {}}
                            />
                        )}
                    </AsyncServiceRecipientWithEntities>
                </CommandForm>
            )}
        </>
    );
}