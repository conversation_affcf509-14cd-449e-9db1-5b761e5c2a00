import {useCareVisitContext} from "./careVisitState";
import {ChatOutlined, EditOutlined} from "@eccosolutions/ecco-mui-controls";
import * as React from "react";
import {
    Avatar,
    IconButton,
    ListItem,
    ListItemAvatar,
    ListItemSecondaryAction,
    ListItemText
} from "@eccosolutions/ecco-mui";
import {EvidenceCommentForm} from "../evidence/EvidenceCommentForm";
import {removeDrafts} from "ecco-components-core";

const EditIcon = () => <EditOutlined/>;

export function CareVisitCommentAsListItem(props: {editable: boolean}) {
    const {state, dispatch} = useCareVisitContext();

    const editComment = () => {
        props.editable && dispatch({type: "showVisitComment", show: true});
    };

    return (
        <>
            <ListItem>
                <ListItemAvatar>
                    <Avatar>
                        <ChatOutlined />
                    </Avatar>
                </ListItemAvatar>
                <ListItemText
                    data-test={"comment-area"}
                    onClick={editComment}
                    primary={state.commentForm.comment ? "" : "Add note"}
                    secondary={state.commentForm.comment}
                    disableTypography
                ></ListItemText>
                <ListItemSecondaryAction>
                    <IconButton onClick={editComment}>
                        <EditIcon />
                    </IconButton>
                </ListItemSecondaryAction>
            </ListItem>
            {state.showCommentForm && (
                <EvidenceCommentForm
                    value={state.commentForm}
                    autoSaveKey={`visitComment-${state.serviceRecipientId}-${state.visitDateTime}`}
                    onSave={commentForm => {
                        removeDrafts(
                            `visitComment-${state.serviceRecipientId}-${state.visitDateTime}`
                        );
                        dispatch({type: "changeVisitComment", commentForm});
                    }}
                    onCancel={() => dispatch({type: "showVisitComment", show: false})}
                />
            )}
        </>
    );
}