import {CareVisitContextProps, useCareVisitContext} from "./careVisitState";
import * as React from "react";
import {FC, useState} from "react";
import {CareTaskAsListItem} from "./CareTaskAsListItem";
import {careStyles} from "./careStyles";
import {
    Box,
    Button,
    CardActions,
    createStyles,
    Divider,
    Grid,
    List,
    makeStyles,
    Typography
} from "@eccosolutions/ecco-mui";
import {handleLazy, LoadingSpinner} from "../Loading";
import {EccoModal} from "ecco-components-core";
import {CareVisitCommentAsListItem} from "./CareVisitCommentAsListItem";
import {RiskFlags} from "../flags/flags";
import {SrAvatarImage} from "../referral/SrAvatarImage";
import {CareVisitStopOptionsForm} from "./CareVisitStopOptions";
import {AdditionalStaffText} from "./AdditionalStaff";
import {CareVisitHistoryController} from "./CareVisitHistory";
import {useServicesContext} from "../ServicesContext";
import {Sr2View} from "../service-recipient/Sr2View";
import {canStartVisit, canStopVisit} from "./CareVisitOverview";

const useStyles = makeStyles(() => createStyles(careStyles));

type CareVisitDetailProps = {};

function shortenAddress(address: string | undefined) {
    const parts = address ? address.split(", ") : [];
    if (parts.length > 2) {
        return parts[0] + ", " + parts[parts.length - 1];
    }
    return address;
}

// NB duplicated with ClientFileButton below
function ClientFileBtn(props: {title: string | undefined; srId: number}) {
    const [show, setShow] = useState<boolean>(false);
    return (
        <>
            <Button
                variant={"outlined"}
                size={"small"}
                color={"primary"}
                onClick={() => setShow(true)}
            >
                open file
            </Button>

            {show && (
                <EccoModal
                    fullScreen={true}
                    title={props.title}
                    onEscapeKeyDown={() => setShow(false)}
                >
                    <>
                        <Button onClick={() => setShow(false)}>back to visit</Button>
                        {handleLazy(
                            <Sr2View basepath={"/nav/r/welcome/care/"} srId={props.srId} />
                        )}
                    </>
                </EccoModal>
            )}
        </>
    );
}

// NB duplicated with ClientFileMenuButton above
export function ClientFileButton(props: {title: string | undefined; srId: number}) {
    const [show, setShow] = useState<boolean>(false);
    return (
        <>
            <Button variant={"outlined"} color={"primary"} onClick={() => setShow(true)}>
                open file
            </Button>

            {show && (
                <EccoModal
                    fullScreen={true}
                    title={props.title}
                    onEscapeKeyDown={() => setShow(false)}
                >
                    <>
                        <Button onClick={() => setShow(false)}>back to visit</Button>
                        {handleLazy(
                            <Sr2View basepath={"/nav/r/welcome/care/"} srId={props.srId} />
                        )}
                    </>
                </EccoModal>
            )}
        </>
    );
}
export const CareVisitDetail: FC<CareVisitDetailProps> = ({}) => {
    const classes = useStyles();

    const {sessionData} = useServicesContext();
    const {dispatch, state}: CareVisitContextProps = useCareVisitContext();
    const onClose = () => dispatch({type: "hideVisit"});
    const showClientFileButton = sessionData.isEnabled("app.care.visit.allowClientFileAccess");
    const title = (
        <>
            <Grid container>
                <Grid item>
                    <SrAvatarImage srId={state.serviceRecipientId} asIcon={true} />
                </Grid>
                <Grid item xs>
                    {state.displayName}
                    <Typography
                        variant="body1"
                        className={classes.title}
                        color="textPrimary"
                        gutterBottom
                    >
                        {state.visitDateTime}
                        {state.visitType && (
                            <>
                                <br />
                                {state.visitType}
                            </>
                        )}
                    </Typography>
                </Grid>
                <Grid item>
                    {showClientFileButton && (
                        <ClientFileBtn title={state.displayName} srId={state.serviceRecipientId} />
                    )}
                </Grid>
            </Grid>
        </>
    );

    const stopAction = () => {
        const force = sessionData.isEnabled("careapp.stopOptions.always");
        const showOptions = force || state.tasks?.some(t => !t.listDefId);
        return showOptions
            ? dispatch({type: "showStopOptions", show: true})
            : dispatch({type: "stop", dispatch});
    };

    const StopOptionsModal = (
        <>
            {state.showStopOptions && (
                <CareVisitStopOptionsForm
                    onSave={stopOptions => {
                        dispatch({
                            type: "stopWithOptions",
                            stopReasonId: stopOptions.stopReasonId!,
                            dispatch: dispatch
                        });
                    }}
                    onCancel={() => dispatch({type: "showStopOptions", show: false})}
                />
            )}
        </>
    );

    const startable = canStartVisit(state);
    const editable = canStopVisit(state);

    return (
        <EccoModal title={title} maxWidth="xs" show={true} onEscapeKeyDown={onClose}>
            {/*<CardHeader // Own format below is better as we effectively get subheader above prominent name
                    title={state.visitDateTime}
                    subheader={state.displayName}
                    avatar={}
                />*/}
            <>
                {state.additionalStaff && (
                    <Typography className={classes.title} color="textSecondary">
                        <AdditionalStaffText additionalStaff={state.additionalStaff} />
                        <br />
                    </Typography>
                )}
                <Box style={{marginBottom: 16}}>
                    <Typography className={classes.title} color="textSecondary">
                        location:
                    </Typography>
                    <Typography className={classes.pos} color="textPrimary">
                        {shortenAddress(state.address)}
                    </Typography>
                </Box>
                <RiskFlags
                    resource={state.resources!}
                    renderer={(sessionData, flags) => (
                        <>
                            <Typography className={classes.title} color="textSecondary">
                                flags:
                            </Typography>
                            <Typography className={classes.pos} color="textPrimary">
                                {flags
                                    .map(it =>
                                        sessionData
                                            .getListDefinitionEntryById(it.flagId)
                                            .getDisplayName()
                                    )
                                    .join(", ")}
                            </Typography>
                        </>
                    )}
                />
                <Typography className={classes.title} color="textSecondary">
                    visit:
                </Typography>
                <Grid container>
                    {state.timerStartedAt && (
                        <Grid item xs="auto">
                            <Typography className={classes.pos}>
                                started: {state.timerStartedAt.toEccoTime().formatHoursMinutes()}
                                {state.timerStoppedAt && (
                                    <span>
                                        {" "}
                                        completed:{" "}
                                        {state.timerStoppedAt.toEccoTime().formatHoursMinutes()}
                                    </span>
                                )}
                            </Typography>
                        </Grid>
                    )}
                    <Grid item xs />
                    <Grid item xs="auto">
                        {!state.timerStartedAt ? (
                            <Button
                                disabled={!startable}
                                size="small"
                                variant="contained"
                                color="primary"
                                onClick={() => {
                                    dispatch({type: "start", dispatch});
                                }}
                            >
                                start visit
                            </Button>
                        ) : (
                            <span>
                                <Button
                                    size="small"
                                    variant="contained"
                                    color="secondary"
                                    disabled={!editable}
                                    onClick={stopAction}
                                >
                                    finish visit
                                </Button>
                                {StopOptionsModal}
                            </span>
                        )}
                    </Grid>
                </Grid>
                {!editable && state.eventStatusId && (
                    <Grid container>
                        <Grid item xs>
                            <Typography className={classes.pos}>
                                not completed:{" "}
                                {state.eventStatusId &&
                                    sessionData
                                        .getListDefinitionEntryById(state.eventStatusId)
                                        .getDisplayName()}
                            </Typography>
                        </Grid>
                    </Grid>
                )}
                <List>
                    <CareVisitCommentAsListItem editable={editable} />
                </List>
                <Typography className={classes.title} color="textSecondary">
                    planned task summary:
                </Typography>
                <Typography className={classes.pos} color="textPrimary">
                    {state.taskSummary}
                </Typography>
                {state.tasks == null ? (
                    <LoadingSpinner />
                ) : state.tasks.length == null ? (
                    <Typography>no tasks</Typography>
                ) : (
                    <>
                        <Typography
                            className={classes.title}
                            color="textSecondary"
                            style={{paddingTop: "25px"}}
                        >
                            planned tasks:
                        </Typography>
                        <List>
                            {state.tasks.map(t => (
                                <React.Fragment key={t.taskInstanceId}>
                                    <CareTaskAsListItem item={t} editable={editable} />
                                    <Divider variant="inset" /> {/*component={"li"}*/}
                                </React.Fragment>
                            ))}
                        </List>
                    </>
                )}
                <Typography
                    className={classes.title}
                    color="textSecondary"
                    style={{paddingTop: "25px"}}
                >
                    previous visits:
                </Typography>
                <CareVisitHistoryController srId={state.serviceRecipientId} />
            </>
            <CardActions></CardActions>
        </EccoModal>
    );
};
CareVisitDetail.displayName="CareVisitDetail"