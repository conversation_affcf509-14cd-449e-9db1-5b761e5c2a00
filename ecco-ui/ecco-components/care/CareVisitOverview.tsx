import {CareVisitContextProps, CareVisitState, useCareVisitContext} from "./careVisitState";
import * as React from "react";
import {FC} from "react";
import {careStyles} from "./careStyles";
import {Button, Card, CardActions, CardContent, createStyles, makeStyles, Typography} from "@eccosolutions/ecco-mui";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {flagsAsGraphical, RiskFlags} from "../flags/flags";
import {AdditionalStaffText} from "./AdditionalStaff";
import {useServicesContext} from "../ServicesContext";
import {ClientFileButton} from "./CareVisitDetail";
import {SupportWork} from "ecco-dto";

const useStyles = makeStyles(() => createStyles(careStyles));

/**
 * Calculate the visit start from temporary data - either the current state or the transient snapshot (when reloaded, if available).
 * TODO a permanent startedAt could be gathered from the command, or the work date or created date - see EvidenceAssociatedContactCommandHandler.
 *  NB generateLoneWorkerUpdate saves work on AttendanceStatus.START
 */
export function getTemporaryStartedAt(startedClickedAt: EccoDateTime | undefined, work: SupportWork | null) {
    const eventStatus = work?.eventSnapshot; // after a refresh, and when the snapshot exists
    const timerStartedAt =
            startedClickedAt || EccoDateTime.parseIso8601Utc(eventStatus?.startInstant ?? null);
    return timerStartedAt;
}

/**
 * Calculate the visit start from temporary data - either the current state or the transient snapshot (when reloaded, if available).
 * TODO the work date or created date with the minsSpent could also be used for the end date - see EvidenceAssociatedContactCommandHandler.
 */
export function getTemporaryStoppedAt(finishedClickedAt: EccoDateTime | undefined, work: SupportWork | null) {
    const eventStatus = work?.eventSnapshot; // after a refresh, and when the snapshot exists
    const timerStoppedAt =
            finishedClickedAt || EccoDateTime.parseIso8601Utc(eventStatus?.endInstant ?? null);
    return timerStoppedAt;
}

/**
 * Can do a 'start visit' if:
 *  - not clicked 'start visit', or no eventStatus, or no eventStatus.startInstant
 *  - no work item (in case we obliterate the transient snapshot data)
 */
export function canStartVisit(state: CareVisitState) {
    const startableTemp = !getTemporaryStartedAt(state.timerStartedAt, state.work);
    const startedPerm = !!state.work;

    return startableTemp && !startedPerm;
}

/**
 * Can 'finish visit' if:
 *  - has clicked 'start visit' but not 'finish visit'
 *  - hasn't got a work item with minsSpent > 0 (in case we obliterate the transient snapshot data)
 */
export function canStopVisit(state: CareVisitState) {
    const stoppableTemp = !!getTemporaryStartedAt(state.timerStartedAt, state.work) && !getTemporaryStoppedAt(state.timerStoppedAt, state.work);
    const stoppedPerm = state.work?.minsSpent; // if it doesn't exist, we can stop (0 mins can stop again - sort of okay)

    return stoppableTemp && !stoppedPerm;
}

export const CareVisitOverview: FC = () => {
    const classes = useStyles();

    const {dispatch, state}: CareVisitContextProps = useCareVisitContext();
    const {sessionData} = useServicesContext();
    const visit = state;

    const timerStartedAt = getTemporaryStartedAt(visit.timerStartedAt, visit.work);
    const timerStoppedAt = getTemporaryStoppedAt(visit.timerStoppedAt, visit.work);
    const startable = canStartVisit(visit);
    const stoppable = canStopVisit(visit);
    const opacity = timerStoppedAt ? 0.5 : 1;
    return (
        <Card className={classes.card} style={{opacity: opacity}}>
            {/*<CardHeader // Own format below is better as we effectively get subheader above prominent name
                    title={visit.visitDateTime}
                    titleTypographyProps={{color: "textSecondary"}}
                    subheader={visit.displayName}
                    subheaderTypographyProps={{color: "textPrimary", variant: "h6"}}
                    avatar={<i className="fa fa-user"/>}
                />*/}
            <CardContent>
                <Typography className={classes.title} color="textSecondary" gutterBottom>
                    {visit.visitDateTime}
                </Typography>
                <Typography variant="h6">{visit.displayName}</Typography>
                <Typography className={classes.pos} color="textSecondary">
                    {visit.address}
                </Typography>
                <Typography variant="body1" gutterBottom>
                    {/* NB 'visitType' was the title or eventCategoryId as careSingleVisitDataLoader 'visitType: data.title' */}
                    {/* but is now from the categoryId, which is the appointment type - see commit */}
                    {/* the appointment type is actually provided in schedule.title - see rota-schedule-dto.ts DemandScheduleDto */}
                    {visit.visitType}
                </Typography>
                <AdditionalStaffText additionalStaff={state.additionalStaff} />
                <RiskFlags resource={visit.resources!} renderer={flagsAsGraphical} />
                {visit.supportWorker && (
                    <Typography variant="body1" gutterBottom>
                        {`key worker: ${visit.supportWorker}`}
                    </Typography>
                )}
                {visit.scheduleInfo && (
                    <Typography variant="body1" gutterBottom>
                        {visit.scheduleInfo}
                    </Typography>
                )}
                {visit.taskSummary && (
                    <Typography variant="body1" gutterBottom>
                        {visit.taskSummary}
                    </Typography>
                )}
            </CardContent>
            <CardActions>
                {/* TODO navigate with a standard <Link to the client file with eventId */}
                {/*<Link to={`/nav/r/main/sr2/${visit.serviceRecipientId}/`}>file</Link>*/}
                <Button
                    color={"primary"}
                    variant={stoppable ? "contained" : "outlined"}
                    onClick={() => {
                        dispatch({type: "showVisit", dispatch});
                    }}
                >
                    {startable
                        ? `visit`
                        : stoppable
                        ? `visiting (@ ${timerStartedAt?.formatHoursMinutes()})`
                        : "visited"}
                </Button>
                {sessionData.isEnabled("app.care.visit.allowClientFileAccess") && (
                    <ClientFileButton title={""} srId={visit.serviceRecipientId} />
                )}
                {!sessionData.isEnabled("app.care.visit.allowClientFileAccess") && (
                    <Button
                        color={"primary"}
                        variant={"outlined"}
                        onClick={() => {
                            dispatch({type: "showClientDetails"});
                        }}
                    >
                        client details
                    </Button>
                )}
                {/* could also provide an outcome-focused - directTasks could pick from existing smart steps? */}
                {/*<Button
                    onClick={() => {
                        dispatch({type: "showClientDetails"});
                    }}
                >
                    visit (support)
                </Button>*/}
            </CardActions>
        </Card>
    );
};