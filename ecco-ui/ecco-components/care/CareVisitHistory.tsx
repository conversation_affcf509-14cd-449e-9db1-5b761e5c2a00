import * as React from "react";
import {FC, ReactNode, useMemo, useState} from "react";
import {
    AssociatedContactCommandDto,
    AttendanceStatus,
    BaseServiceRecipientCommandDto,
    GoalUpdateCommandDto,
    SessionData,
    WorkEvidenceCommandDto
} from "ecco-dto";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {TaskCompletedFail, TaskCompletedSuccess} from "./CareTaskAsListItem";
import {AccessTime, ChatOutlined, AccountCircle, Map} from "@eccosolutions/ecco-mui-controls";
import {Button, Grid, Typography} from "@eccosolutions/ecco-mui";
import "./CareVisitHistory.css";
import {useServicesContext} from "../ServicesContext";
import {ROTA_VISIT} from "./careVisitReducer";
import {groupByAndMap} from "../data/entityLoaders";

interface GroupedVisitCommands {
    start: AssociatedContactCommandDto | undefined;
    tasks: GoalUpdateCommandDto[];
    comment: WorkEvidenceCommandDto | undefined;
    stop: AssociatedContactCommandDto | undefined;
}
const commandsByWorkUuid = (commands: BaseServiceRecipientCommandDto[]) =>
    groupByAndMap(
        commands,
        v => v.workUuid!,
        cmdsWorkUuid => {
            let chunked: GroupedVisitCommands = {
                start: cmdsWorkUuid
                    .filter(c => c.commandName == "associatedContact") // FIXME: this should be "is AssociatedContactCommandDto" and next line removed
                    .map(c => c as AssociatedContactCommandDto)
                    .filter(c => c.attendanceStatus == AttendanceStatus.START)
                    .pop(),
                stop: cmdsWorkUuid
                    .filter(c => c.commandName == "associatedContact") // FIXME: this should be "is AssociatedContactCommandDto" and next line removed
                    .map(c => c as AssociatedContactCommandDto)
                    .filter(c => c.attendanceStatus == AttendanceStatus.END)
                    .pop(),
                tasks: cmdsWorkUuid
                    .filter(c => c.commandName == "goalUpdate") // FIXME: this should be "is GoalUpdateCommandDto" and next line removed
                    .map(c => c as GoalUpdateCommandDto),
                comment: cmdsWorkUuid
                    .filter(c => c.commandName == "comment") // FIXME: this should be "is WorkEvidenceCommandDto" and next line removed
                    .map(c => c as WorkEvidenceCommandDto)
                    .pop()
            };
            return chunked;
        }
    );

function constructHistoryControls(
    commands: BaseServiceRecipientCommandDto[] | null,
    sessionData: SessionData
) {
    const visitHistories: ReactNode[] | null = useMemo(() => {
        if (commands == null) {
            return null;
        } else {
            const result: ReactNode[] = [];
            commandsByWorkUuid(commands).forEach((uiGrp, _) =>
                result.push(<GroupedVisit uiGrp={uiGrp} sessionData={sessionData} />)
            );
            return result;
        }
    }, [commands]);
    return visitHistories;
}

/**
 * ROTA_VISIT taskName records items in a 'visit' which comes from calendarEventCard.tsx
 * including:
 *  - taskUpdateCommand() - for the smartstep/tasks (plannedDateTime, changeStatusReason)
 *  - commentUpdateCommand() - for the note (comment, workdate, minsSpent)
 *  - createLoneWorkerCommand() - for the start/stop
 */
// TODO: Sort out _welcome_ having access to users and impersonate
export const CareVisitHistory: FC<{
    commands: BaseServiceRecipientCommandDto[] | null;
    onLoad: () => void;
    allLoaded: boolean;
    sessionData: SessionData;
}> = ({commands, onLoad, allLoaded, sessionData}) => {

    const visitHistories = constructHistoryControls(commands, sessionData);

    return (
        <Grid container spacing={2}>
            <Grid item xs={12}>
                <ul className="CareVisitHistory" style={{listStyle: "none"}}>
                    {visitHistories}
                </ul>
            </Grid>
            <Grid item xs={12} style={{textAlign: "center"}}>
                {allLoaded && <Typography>no more history</Typography>}
                {!allLoaded && (
                    <Button onClick={() => onLoad()}>
                        {/* defaults are provided by careVisitInitialState */}
                        {commands == null ? "load" : "more"}
                    </Button>
                )}
            </Grid>
        </Grid>
    );
};

const GroupedVisit: FC<{uiGrp: GroupedVisitCommands; sessionData: SessionData}> = props => {
    const {uiGrp, sessionData} = props;
    const startDateTime = EccoDateTime.parseIso8601Utc(uiGrp.start?.timestamp || null);
    return (
        <>
            <RenderUser
                key={`${uiGrp.comment?.workUuid}-user`}
                username={uiGrp.comment?.userDisplayName}
            />
            {uiGrp.start && (
                <RenderStart
                    key={`${uiGrp.start.workUuid}-start`}
                    timer={uiGrp.start}
                    plannedStart={uiGrp.comment?.plannedWorkDateTime}
                    timeOnly={false}
                />
            )}
            {uiGrp.tasks.map(t => (
                <RenderTask
                    key={`${t.actionInstanceUuid}-task`}
                    task={t}
                    start={startDateTime}
                    sessionData={sessionData}
                />
            ))}
            {uiGrp.comment?.eventStatusId?.to && (
                <li>
                    <IconWithText>
                        {sessionData
                            .getListDefinitionEntryById(uiGrp.comment?.eventStatusId?.to)
                            ?.isIconClassSuccess() ? (
                            <TaskCompletedSuccess fontSize={"small"} />
                        ) : (
                            <TaskCompletedFail fontSize={"small"} />
                        )}
                        {`Not completed: ${sessionData
                            .getListDefinitionEntryById(uiGrp.comment?.eventStatusId?.to)
                            .getDisplayName()}`}
                    </IconWithText>
                </li>
            )}
            {uiGrp.comment?.locationId?.to && (
                <li>
                    <IconWithText>
                        <Map />
                        {`location: ${sessionData
                            .getListDefinitionEntryById(uiGrp.comment?.locationId?.to)
                            .getDisplayName()}`}
                    </IconWithText>
                </li>
            )}
            {<RenderComment key={`${uiGrp.comment?.workUuid}-cmt`} comment={uiGrp.comment} />}
            {uiGrp.stop && (
                <RenderFinished
                    key={`${uiGrp.stop.workUuid}-end`}
                    timer={uiGrp.stop}
                    timeOnly={true}
                    start={uiGrp.start?.timestamp}
                    plannedMins={uiGrp.comment?.plannedMinsSpent}
                    eventStatusId={uiGrp.comment?.eventStatusId?.to}
                    sessionData={sessionData}
                />
            )}
            <li>&nbsp;</li>
            <li>&nbsp;</li>
        </>
    );
};

export const CareVisitHistoryController: FC<{srId: number}> = ({srId}) => {
    const {sessionData, serviceRecipientRepository} = useServicesContext();
    const [page, setPage] = useState(0);
    const [previousVisits, setPreviousVisits] = useState<BaseServiceRecipientCommandDto[] | null>(
        null
    );
    const [fullyLoaded, setFullyLoaded] = useState(false);

    const onLoad = () =>
        serviceRecipientRepository
            .findServiceRecipientTaskCommandsByCreated(srId, page, ROTA_VISIT)
            .then(history => {
                setPage(page + 1);
                setPreviousVisits(previousVisits ? previousVisits.concat(history) : history);
                if (history.length == 0) setFullyLoaded(true);
            });

    return (
        <CareVisitHistory
            commands={previousVisits}
            onLoad={onLoad}
            allLoaded={fullyLoaded}
            sessionData={sessionData}
        />
    );
};

const IconWithText: FC = props => {
    return <Typography style={{display: "flex"}}>{props.children}</Typography>;
};

const RenderUser: FC<{
    username: string | undefined;
}> = props => {
    const {username} = props;
    return (
        <>
            {username && (
                <li>
                    <IconWithText>
                        <AccountCircle />
                        {username}
                    </IconWithText>
                </li>
            )}
        </>
    );
};

const RenderTask: FC<{
    task: GoalUpdateCommandDto;
    start: EccoDateTime | null;
    sessionData: SessionData;
}> = props => {
    const {task, sessionData} = props;
    const plannedDateTime = EccoDateTime.parseIso8601(task.plannedDateTime || null);
    const showDate =
        plannedDateTime &&
        props.start &&
        !props.start.toEccoDate().equals(plannedDateTime?.toEccoDate());
    return (
        <li>
            <IconWithText>
                {task.statusChangeReason &&
                task.statusChangeReason.to &&
                sessionData
                    .getListDefinitionEntryById(task.statusChangeReason.to)
                    ?.isIconClassSuccess() ? (
                    <TaskCompletedSuccess fontSize={"small"} />
                ) : (
                    <TaskCompletedFail fontSize={"small"} />
                )}
                {sessionData.getAnyActionById(task.actionDefId!).getName()}
                {plannedDateTime &&
                    !showDate &&
                    ` (due ${plannedDateTime?.toEccoTime()?.formatHoursMinutes()})`}
            </IconWithText>
            {plannedDateTime && showDate && (
                <ul>
                    <li>
                        <Typography display={"inline"}>
                            {`due: ${plannedDateTime?.formatDateTimePretty()}`}
                        </Typography>
                    </li>
                </ul>
            )}
        </li>
    );
};

// our ecco-common time.ts doesn't yet provide local alternatives for the common methods
// the conversions we want involve a bit more than basic stuff as 'toLocalEccoDate' documents
const formatDateOrTime = (datetime: string, utc: boolean, timeOnly: boolean) => {
    const ops: Intl.DateTimeFormatOptions = {
        weekday: "short",
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "numeric",
        timeZone: "Europe/London"
    };
    const t = utc
        ? EccoDateTime.parseIso8601Utc(datetime).toUtcJsDate()
        : EccoDateTime.parseIso8601Utc(datetime).toLocalJsDate();
    return timeOnly
        ? t.toLocaleString("en-GB", {hour: ops.hour, minute: ops.minute})
        : t.toLocaleString("en-GB", ops);
};

const RenderStart: FC<{
    timer: AssociatedContactCommandDto;
    timeOnly: boolean;
    plannedStart: string | undefined;
}> = props => {
    const {timer, timeOnly, plannedStart} = props;
    return (
        <>
            <li>
                <IconWithText>
                    <AccessTime fontSize={"small"} />
                    {"Started"}
                    :&nbsp;
                    {formatDateOrTime(timer.timestamp, true, timeOnly)}
                </IconWithText>
            </li>
            {plannedStart && (
                <li>
                    <IconWithText>
                        <AccessTime fontSize={"small"} />
                        {"Planned"}
                        :&nbsp;
                        {formatDateOrTime(plannedStart, false, timeOnly)}
                    </IconWithText>
                </li>
            )}
        </>
    );
};
const RenderFinished: FC<{
    timer: AssociatedContactCommandDto;
    timeOnly: boolean;
    start: string | undefined;
    plannedMins: number | undefined;
    eventStatusId: number | undefined | null;
    sessionData: SessionData;
}> = props => {
    const {timer, timeOnly, plannedMins, start} = props;
    const actualMins =
        start &&
        EccoDateTime.parseIso8601Utc(timer.timestamp)
            .subtractDateTime(EccoDateTime.parseIso8601Utc(start), true)
            ?.inMinutes()
            .toFixed(0);
    return (
        <>
            <li>
                <IconWithText>
                    <AccessTime fontSize={"small"} />
                    {`Finished ${formatDateOrTime(timer.timestamp, true, timeOnly)}`}
                </IconWithText>
            </li>
            {(plannedMins || actualMins) && (
                <li>
                    <IconWithText>
                        <AccessTime fontSize={"small"} />
                        {actualMins && (
                            <span
                                style={{
                                    paddingRight: "5px",
                                    ...(actualMins.startsWith("-") ? {color: "red"} : {})
                                }}
                            >
                                {`Actual: ${actualMins}m`}
                            </span>
                        )}
                        <span>{plannedMins && `Expected: ${plannedMins}m`}</span>
                    </IconWithText>
                </li>
            )}
        </>
    );
};
const RenderComment: FC<{
    comment: WorkEvidenceCommandDto | undefined;
}> = props => {
    const {comment} = props;
    return (
        <li>
            <IconWithText>
                <ChatOutlined fontSize={"small"} />
                {comment?.comment?.to}
            </IconWithText>
        </li>
    );
};
