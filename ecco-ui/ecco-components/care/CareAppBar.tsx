import * as React from "react";
import {FC} from "react";
import {useHistory, useRouteMatch} from "react-router";
import {AppBarBase, AppBarContextProvider, useAppBarContext} from "../AppBarBase";
import {SidebarMenuBuilder} from "../SidebarMenuBuilder";
import {useServicesContext} from "../ServicesContext";
import {UserMenu} from "../user/UserMenu";
import {
    CareVisitMenu,
    CareVisitMenuContextProvider,
    useCareVisitMenuContext
} from "./CareVisitMenu";

const MainMenuItems: FC = () => {
    const {sessionData} = useServicesContext();
    return (
        new SidebarMenuBuilder("/nav/r/care/")
            .addSubHeader("ecco care")
            .addExternalRoute("menu", "fa fa-calendar-o", "nav/r/welcome/",
                sessionData.hasRoleReferralAdmin()
            )
            .addExternalRoute("calendar", "fa fa-calendar", "nav/r/welcome/cal",
                !sessionData.hasRoleCarer()
            )
            .build()
    );
};

const CareAppBar: FC = props => {
    return (
        <AppBarContextProvider>
            <CareVisitMenuContextProvider>
                <CareAppBarBase>{props.children}</CareAppBarBase>
            </CareVisitMenuContextProvider>
        </AppBarContextProvider>
    );
};
const CareAppBarBase: FC = props => {
    const {path} = useRouteMatch();
    const history = useHistory();

    const ctx = useAppBarContext();
    const ctxMenu = useCareVisitMenuContext();
    if (!ctx && !ctxMenu) {
        return null;
    }

    const userMenu = <UserMenu key="user-menu" extraMenuItems={ctx.extraMenuItems} />;
    const careVisitMenu = <CareVisitMenu key="page-menu" />;
    const menuRight = [userMenu, careVisitMenu];

    return (
        <>
            <AppBarBase
                appColour={ctx.appColour}
                title={"care overview"}
                right={menuRight}
                onLogoClick={() => history.push(`${path}`)}
                drawerContent={<MainMenuItems />}
            >
                {props.children}
            </AppBarBase>
        </>
    );
};

export default CareAppBar;