import * as React from "react";
import {createContext, FC, useContext, useEffect, useState} from "react";
import {
    Checkbox,
    FormControlLabel,
    green,
    Grid,
    IconButton,
    InputAdornment
} from "@eccosolutions/ecco-mui";
import {FilterListIcon, ClearIcon} from "@eccosolutions/ecco-mui-controls";
import {useDebounce} from "ecco-components-core";
import {EccoTextInput, possiblyModalForm} from "ecco-components-core";


// ##### MENU CONTEXT #####
type CareVisitMenuContextProps = {
    searchData: SearchFormData;
    setSearchData: (data: SearchFormData) => void;
    openSearch: boolean;
    setOpenSearch: (dialog: boolean) => void;
};
const CareVisitMenuContext = createContext<CareVisitMenuContextProps | undefined>(undefined);
export const useCareVisitMenuContext = () => useContext(CareVisitMenuContext)!; // It's a dev error if we haven't initialised before use

export const CareVisitMenuContextProvider: FC = props => {
    const [searchData, setSearchData] = useState<SearchFormData>(SearchFormDataDefault);
    const [openSearch, setOpenSearch] = useState(false);
    const opts: CareVisitMenuContextProps = {
        searchData,
        setSearchData,
        openSearch,
        setOpenSearch
    };
    return <CareVisitMenuContext.Provider value={opts}>{props.children}</CareVisitMenuContext.Provider>;
};
// ##### MENU CONTEXT #####

export const CareVisitMenu: FC<{}> = props => {
    //const [open, setOpen] = useState(false);

    const ctx = useCareVisitMenuContext();
    if (!ctx) {
        return null;
    }

    const filterApplied = !(
        JSON.stringify(SearchFormDataDefault) === JSON.stringify(ctx.searchData)
    );

    return (
        <>
            <IconButton color="default" onClick={() => ctx.setOpenSearch(true)}>
                <FilterListIcon style={filterApplied ? {color: green["A400"]} : {}} />
            </IconButton>
        </>
    );
};

export interface SearchFormData {
    showNotStarted: boolean | null;
    showStarted: boolean | null;
    showFinished: boolean | null;
    searchText: string | null;
    showAdhoc: boolean | null;
}
export const SearchFormDataDefault = {
    showNotStarted: true,
    showStarted: true,
    showFinished: false,
    searchText: null,
    showAdhoc: true
};

const SearchText: FC<{
    searchText: string | null;
    onChange: (text: string | null) => void;
}> = props => {
    const [searchText, setSearchText] = useState<string | null>(props.searchText);

    // debounce for live updates
    let debouncedValue = useDebounce(searchText, 1000);
    // if debounced value changes, then save it
    useEffect(() => {
        props.onChange(debouncedValue);
    }, [debouncedValue]);

    return (
        <EccoTextInput
            data-test={"text-search"}
            propertyKey="search-no-autocomplete"
            label="search anywhere"
            value={searchText}
            autoCompleteOff={true}
            onChange={val => setSearchText(val)}
            type="text"
            InputProps={{
                endAdornment: (
                    <InputAdornment position="end">
                        <IconButton aria-label="clear search" onClick={() => setSearchText(null)}>
                            <ClearIcon />
                        </IconButton>
                    </InputAdornment>
                )
            }}
        />
    );
};

export const SearchForm: FC<{
    searchData: SearchFormData;
    onChange: (searchFormData: SearchFormData) => void;
    onClose: (searchFormData: SearchFormData) => void;
}> = props => {
    const [searchData, setSearchData] = useState<SearchFormData>(props.searchData);

    // debounce for live updates
    let debouncedValue = useDebounce(searchData, 1000);
    // if debounced value changes, then save it
    useEffect(() => {
        props.onChange(debouncedValue);
    }, [debouncedValue]);

    // immediate change, and close form
    const onClose = () => {
        props.onClose(searchData);
    };

    const IncludeFilter = () => (
        <>
            <FormControlLabel
                control={
                    <Checkbox
                        name={"chkDue"}
                        checked={searchData.showNotStarted || false}
                        onChange={() =>
                            setSearchData({
                                ...searchData,
                                showNotStarted: !searchData.showNotStarted
                            })
                        }
                    />
                }
                label={"not started"}
            ></FormControlLabel>
            <FormControlLabel
                control={
                    <Checkbox
                        name={"chkOpen"}
                        checked={searchData.showStarted || false}
                        onChange={() =>
                            setSearchData({...searchData, showStarted: !searchData.showStarted})
                        }
                    />
                }
                label={"started"}
            ></FormControlLabel>
            <FormControlLabel
                control={
                    <Checkbox
                        name={"chkVisited"}
                        checked={searchData.showFinished || false}
                        onChange={() =>
                            setSearchData({...searchData, showFinished: !searchData.showFinished})
                        }
                    />
                }
                label={"finished"}
            ></FormControlLabel>
            <FormControlLabel
                control={
                    <Checkbox
                        name={"chkAdhoc"}
                        checked={searchData.showAdhoc || false}
                        onChange={() =>
                            setSearchData({...searchData, showAdhoc: !searchData.showAdhoc})
                        }
                    />
                }
                label={"ad-hoc"}
            ></FormControlLabel>
        </>
    );

    return possiblyModalForm(
        "filter",
        true,
        true,
        onClose, // noFooterButtons means we cancel is called! so supply the search
        () => {},
        false,
        true,
        <Grid container>
            <Grid item xs={12}>
                <IncludeFilter />
            </Grid>
            <Grid item xs={12}>
                <SearchText
                    searchText={searchData.searchText}
                    onChange={text => setSearchData({...searchData, searchText: text})}
                />
            </Grid>
        </Grid>,
        "none",
        undefined,
        undefined,
        "sm",
        0,
        false
    );
};

CareVisitMenu.displayName = "CareVisitMenu";
