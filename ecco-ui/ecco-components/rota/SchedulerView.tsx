import * as React from "react";
import {
    Autocomplete,
    Button,
    ButtonGroup,
    Checkbox,
    createStyles, FormControl,
    FormControlLabel,
    FormGroup,
    FormLabel,
    Grid, InputLabel, ListItemText,
    makeStyles,
    MenuItem,
    Radio,
    RadioGroup, Select,
    TextField,
    Theme
} from "@eccosolutions/ecco-mui";
import {IconMenu, MoreVert} from "@eccosolutions/ecco-mui-controls";
import {EccoDate, EccoDateTime, EccoTime} from "@eccosolutions/ecco-common";
import {FC, useEffect, useState} from "react";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {LoadingSpinner, LoadingSpinnerDialogWithMessage} from "../Loading";
import {useAppBarOptions} from "../AppBarBase";
import {loadSchedule} from "../data/schedulerDataLoader";
import {useServicesContext} from "../ServicesContext";
import {DataTable} from "../table/DataTable";
import {
    SchedulerBulkChangeCarer,
    SchedulerBulkDrop,
    SchedulerBulkMove
} from "./SchedulerBulkChange";
import {AppointmentActionCommand, ServiceRecipientAppointmentScheduleCommand} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {processLinear} from "../data/entityLoaders";
import {EccoTheme} from "ecco-components-core";
import {CreateAppointmentModal} from "../agreements/CreateAppointmentForm";
import {MUIDataTableColumnDef} from "mui-datatables";
import {applicationRootPath} from "application-properties";
import {EventResourceDto} from "ecco-dto";
import {EccoV3Modal} from "ecco-components-core";
import {CareVisitSummaryCard} from "../care/CareVisitSummaryCard";
import {CareVisitRoot} from "../care/CareVisitRoot";
import {useCareOrEventCard} from "../data/careSingleVisitDataLoader";

// see ClientReferralsPopup.tsx
export function openReferralSrIdDirect(srId: number, newTab = false) {
    if (newTab) {
        window.open(getReferralSrIdDirectHref(srId), "_blank");
    } else {
        location.href = getReferralSrIdDirectHref(srId);
    }
}
export function getReferralSrIdDirectHref(srId: number) {
    return new URL(`${applicationRootPath}nav/referrals/sr/${srId}/`, location.href).href;
}

export interface ScheduleRowProps {
    eventUid: string | null;
    demandScheduleId: number | null;
    srDisplayName: string | null;
    srAddress: string | null;
    srId: number | null;
    srAllocationId: number | null;
    aptTitle: string | null;
    aptTypeId: number | null;
    aptTypeName: string | null; // check (eg 12 month) or visit
    plannedStartDateTime: string | undefined; // ISO8601 local-datetime
    taskSummary: string | null | undefined;
    frequencyType: string | null;
    frequencyDays: string | undefined | null;
    plannedEndDateTime: string | undefined; // ISO8601 local-datetime
    plannedResource: string | undefined;
    workDateTime: string | undefined; // ISO8601 local-datetime (extended, zoneless - so 7am BST is stored as 7am). NB workdate saved from changeWorkDate(null, state.timerStartedAt?.formatIso8601())
    workAuthorDisplayName: string | null;
    dueData: {text: string; value: string} | null;
}

/**
 * Generate a command to allocate an appointment to someone else.
 * TODO can we even use recurring?
 * @param eventUid
 * @param srId
 * @param resourceId the workerJobId
 */
export const demandFilter = (svcCatId: number | null) => `svccats:${svcCatId || "-"}`;
export const resourceFilter = () => "workers:-";
const generateSingleAllocateCommand = (eventUid: string, srId: number, resourceId: number) => {
    const actionSingleCommand = new AppointmentActionCommand(
        "allocate",
        Uuid.randomV4(),
        eventUid,
        srId,
        resourceFilter(), // chooses the rota handler
        demandFilter(null) // chooses the rota handler (could just do 'svccats:-')
    );
    actionSingleCommand.allocateResourceId = resourceId;
    return actionSingleCommand;
};

const generateSingleDropCommand = (eventUid: string, srId: number, dropReasonId: number) => {
    const actionSingleCommand = new AppointmentActionCommand(
        "drop",
        Uuid.randomV4(),
        eventUid,
        srId,
        resourceFilter(), // chooses the rota handler
        demandFilter(null) // chooses the rota handler (could just do 'svccats:-')
    );
    actionSingleCommand.dropReasonId = dropReasonId;
    return actionSingleCommand;
};

const generateSingleMoveCommand = (
    eventUid: string,
    srId: number,
    timeFrom: EccoTime,
    moveFrom: EccoDate | null,
    moveTo: EccoDate
) => {
    const actionSingleCommand = new ServiceRecipientAppointmentScheduleCommand(
        "update",
        Uuid.randomV4(),
        srId,
        eventUid,
        undefined
    )
        .changeStartDate(moveFrom, moveTo)
        .changeTime(null, timeFrom); // not ideal but not sure why server rejects without a time
    //.changeTime(initialTime.formatHoursMinutes() == editedData.time!.formatHoursMinutes() ? null : initialTime, editedData.time!) // NOTE: null unless back end gets changed to reuse prev time
    //.changeDurationMins(this.originalDurationMins, editedData.durationMins!)
    //.changeTitle(activity.getTitle(), editedData.title!);
    return actionSingleCommand;
};

const Visit: FC<{visitEventId: string; onClose: () => void}> = props => {
    const {calendarRepository} = useServicesContext();
    const [visitEvent, setVisitEvent] = useState<EventResourceDto | null>(null);

    useEffect(() => {
        if (!visitEvent || visitEvent.uid != props.visitEventId) {
            calendarRepository
                .fetchEventsById([props.visitEventId])
                .then(events => setVisitEvent(events[0]!));
        }
    }, [props.visitEventId]);

    return visitEvent ? <VisitInner visitEvent={visitEvent} onClose={props.onClose} /> : null;
};
const VisitInner: FC<{visitEvent: EventResourceDto; onClose: () => void}> = ({
    visitEvent,
    onClose
}) => {
    const {data: visitCare} = useCareOrEventCard(visitEvent, false);

    const VisitModal = (
        <EccoV3Modal title={""} show={true} onCancel={() => onClose()} action="close">
            {visitCare && (
                <CareVisitRoot careVisitInitState={visitCare}>
                    <CareVisitSummaryCard />
                </CareVisitRoot>
            )}
        </EccoV3Modal>
    );
    return <>{VisitModal}</>;
};

const Menu: FC<{data: ScheduleRowProps}> = ({data}) => {
    const [menuOpen, setMenuOpen] = useState(false);
    const [showVisit, setShowVisit] = useState<boolean>(false);

    const onMenuChoice = () => {
        setMenuOpen(false);
    };

    return (
        <>
            <IconMenu
                id={`datatable-context-menu`}
                iconComponent={<MoreVert />}
                onClick={() => setMenuOpen(true)}
                open={menuOpen}
                onClose={() => setMenuOpen(false)}
            >
                <MenuItem
                    title={"client-file"}
                    onClick={() => {
                        onMenuChoice();
                        openReferralSrIdDirect(data.srId!, true);
                    }}
                >
                    client file
                </MenuItem>
                <MenuItem
                    title={"visit"}
                    onClick={() => {
                        onMenuChoice();
                        setShowVisit(true);
                    }}
                >
                    visit
                </MenuItem>
                {/*<MenuItem title={"reschedule"} onClick={() => onMenuChoice()}>
                edit
            </MenuItem>
            <MenuItem title={"reassign"} onClick={() => onMenuChoice()}>
                reassign
            </MenuItem>
            <MenuItem title={"drop"} onClick={() => onMenuChoice()}>
                drop
            </MenuItem>
            */}
            </IconMenu>
            {showVisit && (
                <Visit visitEventId={data.eventUid!} onClose={() => setShowVisit(false)} />
            )}
        </>
    );
};

const DataTableWrapper: FC<{
    data: ScheduleRowProps[];
    serviceId: number | null;
    reload: () => void;
}> = ({data, serviceId, reload}) => {
    const {sessionData, getCommandRepository} = useServicesContext();
    const [bulkCarerOpen, setBulkCarerOpen] = useState(false);
    const [bulkMoveOpen, setBulkMoveOpen] = useState(false);
    const [bulkDropOpen, setBulkDropOpen] = useState(false);
    const [selectedRows, setSelectedRows] = useState<Partial<ScheduleRowProps>[]>([]);
    const [carerJobId, setCarerJobId] = useState<number | null>(null);
    const [dropReasonId, setDropReasonId] = useState<number | null>(null);
    const [moveToDate, setMoveToDate] = useState<EccoDate | null>(null);
    const [processingType, setProcessingType] = useState<"carer" | "drop" | "move" | null>(null);
    const [processing, setProcessing] = useState(false);
    const [processingCount, setProcessingCount] = useState(0);

    const singleCarerPromise = (id: {eventUid: string; srId: number; carerJobId: number}) => {
        const cmd = generateSingleAllocateCommand(id.eventUid, id.srId, id.carerJobId);
        return getCommandRepository()
            .sendCommand(cmd)
            .then(() => {
                setProcessingCount(processingCount - 1);
                // for now, return blank so that the processLinear doens't need much messing around with void
                return [];
            });
    };

    const singleDropPromise = (id: {eventUid: string; srId: number; dropReasonId: number}) => {
        const cmd = generateSingleDropCommand(id.eventUid, id.srId, id.dropReasonId);
        return getCommandRepository()
            .sendCommand(cmd)
            .then(() => {
                setProcessingCount(processingCount - 1);
                // for now, return blank so that the processLinear doens't need much messing around with void
                return [];
            });
    };

    const singleMovePromise = (id: {
        eventUid: string;
        srId: number;
        timeFrom: EccoTime;
        moveFrom: EccoDate | null;
        moveTo: EccoDate;
    }) => {
        const cmd = generateSingleMoveCommand(
            id.eventUid,
            id.srId,
            id.timeFrom,
            id.moveFrom,
            id.moveTo
        );
        return getCommandRepository()
            .sendCommand(cmd)
            .then(() => {
                setProcessingCount(processingCount - 1);
                // for now, return blank so that the processLinear doens't need much messing around with void
                return [];
            });
    };

    // start the processing
    const processCarerStart = (carerJobId: number | null) => {
        setProcessingType("carer");
        setCarerJobId(carerJobId);
        setProcessingCount(selectedRows.length);
        setProcessing(true);
    };
    const processMoveStart = (moveTo: EccoDate | null) => {
        setProcessingType("move");
        setMoveToDate(moveTo);
        setProcessingCount(selectedRows.length);
        setProcessing(true);
    };
    const processDropStart = (dropReasonId: number | null) => {
        setProcessingType("drop");
        setDropReasonId(dropReasonId);
        setProcessingCount(selectedRows.length);
        setProcessing(true);
    };

    // BULK UPDATE per row/event
    useEffect(() => {
        // if triggered to load...
        if (processingCount > 0) {

            let q: Promise<any>;
            switch (processingType) {
                case "carer": {
                    const carerDataToProcess = selectedRows.map(d => {
                        return {
                            eventUid: d.eventUid!,
                            srId: d.srId!,
                            carerJobId: carerJobId!
                        };
                    });
                    q = processLinear(carerDataToProcess, singleCarerPromise).then(() => {
                        setCarerJobId(null);
                        setBulkCarerOpen(false);
                    });
                    break;
                }
                case "drop": {
                    const dropDataToProcess = selectedRows.map(d => {
                        return {
                            eventUid: d.eventUid!,
                            srId: d.srId!,
                            dropReasonId: dropReasonId!
                        };
                    });
                    q = processLinear(dropDataToProcess, singleDropPromise).then(() => {
                        setDropReasonId(null);
                        setBulkDropOpen(false);
                    });
                    break;
                }
                case "move": {
                    const moveDataToProcess = selectedRows.map(d => {
                        return {
                            eventUid: d.eventUid!,
                            srId: d.srId!,
                            timeFrom: EccoDateTime.parseIso8601(
                                d.plannedStartDateTime!
                            ).toEccoTime(),
                            moveFrom: EccoDateTime.parseIso8601(
                                d.plannedStartDateTime!
                            ).toEccoDate(),
                            moveTo: moveToDate!
                        };
                    });
                    q = processLinear(moveDataToProcess, singleMovePromise).then(() => {
                        setMoveToDate(null);
                        setBulkMoveOpen(false);
                    });
                    break;
                }
                default:
                    throw new Error("unknown processingType");
            }
            q.then(() => {
                setProcessing(false);
                setProcessingCount(0);
                reload();
            });
        }
    }, [processing]);

    const columns: MUIDataTableColumnDef[] = [
        {label: "eventUid", name: "eventUid", options: {filter: false, display: false}},
        {label: "name", name: "srDisplayName", options: {filterType: "textField"}},
        {label: "address", name: "srAddress", options: {filterType: "textField"}},
        {label: "srId", name: "srId", options: {filter: false, display: false}},
        {
            label: "service",
            name: "srAllocationId",
            options: {
                filterType: "multiselect",
                customBodyRender: (value, _tableMeta, _updateValue) =>
                    sessionData.getServiceCategorisationName(value)
            }
        },
        {label: "title", name: "title", options: {filter: false, display: false}},
        {label: "type", name: "aptTypeName"},
        {
            label: "planned",
            name: "plannedStartDateTime",
            options: {
                filter: true,
                filterType: "custom",

                // if the below value is set, these values will be used every time the table is rendered.
                // it's best to let the table internally manage the filterList
                //filterList: [25, 50],

                customFilterListOptions: {
                    render: v => {
                        if (v[0] && v[1]) {
                            return [`planned min: ${v[0]}`, `planned max: ${v[1]}`];
                        } else if (v[0]) {
                            return `planned min: ${v[0]}`;
                        } else if (v[1]) {
                            return `planned max: ${v[1]}`;
                        }
                        return [];
                    },
                    update: (filterList, filterPos, index) => {
                        //console.log("customFilterListOnDelete: ", filterList, filterPos, index);

                        if (filterPos === 0) {
                            filterList[index].splice(filterPos, 1, "");
                        } else if (filterPos === 1) {
                            filterList[index].splice(filterPos, 1);
                        } else if (filterPos === -1) {
                            filterList[index] = [];
                        }

                        return filterList;
                    }
                },
                filterOptions: {
                    names: [],
                    logic(value, filters) {
                        if (filters[0] && filters[1]) {
                            return value < filters[0] || value > filters[1];
                        } else if (filters[0]) {
                            return value < filters[0];
                        } else if (filters[1]) {
                            return value > filters[1];
                        }
                        return false;
                    },
                    display: (filterList, onChange, index, column) => (
                        <div>
                            <FormLabel>planned</FormLabel>
                            <FormGroup row>
                                <TextField
                                    label="min"
                                    value={filterList[index]![0] || ""}
                                    onChange={event => {
                                        // TODO find why this works / fix
                                        // @ts-ignore
                                        filterList[index][0] = event.target.value;
                                        onChange(filterList[index], index, column);
                                    }}
                                    style={{width: "45%", marginRight: "5%"}}
                                />
                                <TextField
                                    label="max"
                                    value={filterList[index]![1] || ""}
                                    onChange={event => {
                                        // TODO find why this works / fix
                                        // @ts-ignore
                                        filterList[index][1] = event.target.value;
                                        onChange(filterList[index], index, column);
                                    }}
                                    style={{width: "45%"}}
                                />
                            </FormGroup>
                        </div>
                    )
                },
                print: false
            }
        },
        {label: "frequency", name: "frequencyType"},
        {
            label: "days",
            name: "frequencyDays",
            options: {
                filter: true,
                display: true,
                filterType: "custom",
                customFilterListOptions: {
                    render: v => v.map((l: string) => l.toUpperCase()),
                    update: (filterList, filterPos, index) => {
                        filterList[index].splice(filterPos, 1);
                        return filterList;
                    }
                },
                filterOptions: {
                    logic: (freqDays: string | undefined, filters) => {
                        console.log(`freqDays ${freqDays} / filters ${filters}`);
                        if (!filters.length || !freqDays) {
                            return false;
                        }
                        const match =
                            (filters.indexOf("Mon") >= 0 && freqDays.indexOf("Mon") >= 0) ||
                            (filters.indexOf("Tues") >= 0 && freqDays.indexOf("Tues") >= 0) ||
                            (filters.indexOf("Wed") >= 0 && freqDays.indexOf("Wed") >= 0) ||
                            (filters.indexOf("Thurs") >= 0 && freqDays.indexOf("Thurs") >= 0) ||
                            (filters.indexOf("Fri") >= 0 && freqDays.indexOf("Fri") >= 0) ||
                            (filters.indexOf("Sat") >= 0 && freqDays.indexOf("Sat") >= 0) ||
                            (filters.indexOf("Sun") >= 0 && freqDays.indexOf("Sun") >= 0);
                        return !match;
                    },
                    display: (filterList, onChange, index, column) => {
                        const optionValues = ["Mon", "Tues", "Wed", "Thurs", "Fri", "Sat", "Sun"];
                        return (
                            <FormControl>
                                <InputLabel htmlFor="select-multiple-chip">days</InputLabel>
                                <Select
                                    multiple
                                    value={filterList[index]}
                                    renderValue={selected => (selected as string[]).join(", ")}
                                    onChange={event => {
                                        filterList[index] = event.target.value as string;
                                        onChange(filterList[index], index, column);
                                    }}
                                >
                                    {optionValues.map(item => (
                                        <MenuItem key={item} value={item}>
                                            <Checkbox
                                                color="primary"
                                                checked={filterList[index]!.indexOf(item) > -1}
                                            />
                                            <ListItemText primary={item} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        );
                    }
                }
            }
        },
        {
            label: "task summary",
            name: "taskSummary",
            options: {filterType: "textField"}
        },
        {label: "worker", name: "plannedResource", options: {filterType: "multiselect"}},
        {
            label: "overdue",
            name: "dueData",
            options: {
                customBodyRenderLite: (dataIndex, _rowIndex) => {
                    const dueData = data[dataIndex]!.dueData;
                    return <p style={{backgroundColor: dueData?.value!}}>{dueData?.text}</p>;
                }
            }
        },
        {label: "visited", name: "workDateTime"},
        /* column for menu action button */
        {
            label: "",
            name: "",
            options: {
                filter: false,
                sort: false,
                customBodyRenderLite: (dataIndex, rowIndex) => {
                    return <Menu data={data[dataIndex]!} />;
                }
            }
        },
        {label: "author", name: "workAuthorDisplayName", options: {filter: false, display: false}}
    ];

    // FIXME this is dodgy as we should easily be able to get the actual data back
    // https://github.com/gregnb/mui-datatables/blob/master/test/MUIDataTableToolbarSelect.test.js
    const mapSelectedToData = (selected: Array<any[]>): Partial<ScheduleRowProps>[] => {
        return selected.map(r => {
            const sched: Partial<ScheduleRowProps> = {
                eventUid: r[0],
                srId: r[3],
                plannedStartDateTime: `${r[7]}T07:00:00.0000`
            };
            return sched;
        });
    };
    const toolbar = (selectedData: () => Array<any[]>) => (
        <ButtonGroup>
            <Button
                style={{padding: "5px", margin: "10px"}}
                onClick={() => {
                    setBulkMoveOpen(true);
                    setSelectedRows(mapSelectedToData(selectedData()));
                }}
            >
                move
            </Button>
            <Button
                style={{padding: "5px", margin: "10px"}}
                onClick={() => {
                    setBulkCarerOpen(true);
                    setSelectedRows(mapSelectedToData(selectedData()));
                }}
            >
                reassign
            </Button>
            <Button
                style={{padding: "5px", margin: "10px"}}
                onClick={() => {
                    setBulkDropOpen(true);
                    setSelectedRows(mapSelectedToData(selectedData()));
                }}
            >
                drop
            </Button>
        </ButtonGroup>
    );

    return (
        <>
            {/*NB in testing, font-awesome isn't loaded so can't see it, so dialog helps*/}
            {/*<LoadingSpinner useProgress={useApiClientProgress}/>*/}
            <LoadingSpinnerDialogWithMessage
                show={processing}
                message={`processing: ${processingCount} of ${selectedRows?.length}`}
            />
            <SchedulerBulkMove
                open={bulkMoveOpen}
                onClose={() => setBulkMoveOpen(false)}
                onSave={processMoveStart}
            />
            <SchedulerBulkChangeCarer
                serviceId={serviceId}
                open={bulkCarerOpen}
                onClose={() => setBulkCarerOpen(false)}
                onSave={processCarerStart}
            />
            <SchedulerBulkDrop
                open={bulkDropOpen}
                onClose={() => setBulkDropOpen(false)}
                onSave={processDropStart}
            />
            <DataTable title={"schedule"} columns={columns} data={data} toolbar={toolbar} />
        </>
    );
};

type LoadType = "date" | "open";

// table options:
// - load everything, and use react table to filter
// - react table is MuiDataTable
// - used in ecco-components/table/DataTable (and DataTableWrapper in offline) for showing loaded data
// - used in SchemaDataTable, eg UsersList.tsx and others
// SO: we should copy UsersList.tsx, fix the date sorting!, copy UserListController.java
// THEN: create tick boxes and actions - like 'bulk assign'
// HOWEVER: given we are applying client-side, and only the first page is be loaded, we'd have to loop through pages and apply
//  or have some way to load the criteria client-side (which is what the hateos does really)
// SO: for now, we should load everything and use react table - with

export const SchedulerView = () => {
    useAppBarOptions("scheduler");
    const {sessionData, rotaRepository} = useServicesContext();

    const classes = useStyles();
    const [rows, setRows] = useState<ScheduleRowProps[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [loadedTimestamp, setLoadedTimestamp] = useState<number | null>(null);
    const [loadAvailable, setLoadAvailable] = useState<boolean>(false);

    const now = EccoDate.todayLocalTime();
    const [startDate, setStartDate] = useState<EccoDate | null>(now);
    const daysStr = sessionData.getSetting("com.ecco.rota:care.scheduler.days");
    const days = parseInt(daysStr);
    const maxDate = now.addDays(days);
    const idealDays = days > 7 ? 7 : days;
    const [endDate, setEndDate] = useState<EccoDate | null>(now.addDays(idealDays));
    const [serviceId, setServiceId] = useState<number | null>(null);
    const [loadSelector, setLoadSelector] = useState<LoadType>("date");
    const [showAdHoc, setShowAdHoc] = useState(false);

    function setLoaded() {
        setLoading(false);
        setLoadedTimestamp(Date.now());
    }

    function getDataByDate() {
        setLoading(true);
        return loadSchedule(
            sessionData,
            rotaRepository,
            loadSelector == "open" ? undefined : startDate!,
            endDate!
        ).then(data => {
            setRows(data);
            setLoaded();
        });
    }

    useEffect(() => {
        const selectorDateAvail = loadSelector == "date" && serviceId && startDate && endDate;
        const selectorDateOpen = loadSelector == "open" && serviceId && endDate;
        if (selectorDateAvail || selectorDateOpen) {
            setLoadAvailable(true);
        } else {
            setLoadAvailable(false);
        }
    }, [serviceId, startDate, endDate, loadSelector]);
    useEffect(() => {
        // when every loadedTimestamp changes (when load completes in setLoaded)
        // and where loadedTimestamp isn't null to start with, then disallow load
        if (loadAvailable && loadedTimestamp) {
            setLoadAvailable(false);
        }
    }, [loadedTimestamp]);

    if (loading) {
        return (
            <Grid container={true} direction={"row"} justify={"center"} alignItems={"center"}>
                <LoadingSpinner />
            </Grid>
        );
    }

    const currSvc = sessionData
        .getRestrictedServices()
        .filter(sc => sc.id == serviceId)
        ?.pop();
    const ServiceSelector = (
        <Autocomplete
            id={"eccotest-svc"}
            renderInput={params => (
                // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                <TextField {...params} label={"service"} variant="outlined" required={true} />
            )}
            getOptionLabel={option => option!.name}
            getOptionSelected={(a, b) => a!.id == b?.id}
            options={sessionData.getRestrictedServices() || []}
            value={currSvc}
            onChange={(_event, obj) => {
                setServiceId(obj?.id || null);
            }}
            /*style={{width: 270}}*/
        />
    );
    const LoadSelector = (
        <RadioGroup
            row={true}
            name="loadSelector"
            title={"load type"}
            value={loadSelector} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
            onChange={(_, value: string) => setLoadSelector(value as LoadType)}
        >
            <FormControlLabel
                key={"date"}
                value={"date"}
                control={<Radio />}
                label={"load by dates"}
            />
            <FormControlLabel key={"open"} value={"open"} control={<Radio />} label={"load open"} />
        </RadioGroup>
    );

    return (
        <>
            <Grid container direction={"row"} className={classes.filterElement}>
                <Grid container item lg={4} md={6} xs={12} justify={"flex-start"}>
                    <Grid item={true} xs={12}>
                        {ServiceSelector}
                    </Grid>
                    {/*<Box component="fieldset" style={{ width: '100%' }} borderColor="#eee">*/}
                    {/*<legend>load type</legend>*/}
                    <Grid item={true} xs={12}>
                        {LoadSelector}
                    </Grid>
                    <Grid item={true} xs={6}>
                        <DatePickerEccoDate
                            onChange={setStartDate}
                            value={startDate}
                            label={"from"}
                            maxDate={endDate}
                            disabled={loadSelector == "open"}
                        />
                    </Grid>
                    <Grid item={true} xs={6}>
                        <DatePickerEccoDate
                            onChange={setEndDate}
                            value={endDate}
                            label={"to"}
                            minDate={startDate}
                            maxDate={maxDate}
                        />
                    </Grid>
                    {/*</Box>*/}
                    <Grid item={true} xs={6}>
                        <Button
                            variant="contained"
                            color="primary"
                            disabled={!loadAvailable}
                            onClick={getDataByDate}
                        >
                            load
                        </Button>
                    </Grid>
                    <Grid item={true} xs={6}>
                        <Button
                            size={"small"}
                            variant="outlined"
                            color="primary"
                            onClick={() => setShowAdHoc(true)}
                        >
                            new ad-hoc
                        </Button>
                    </Grid>
                </Grid>
                {showAdHoc && (
                    <EccoTheme prefix="rv">
                        <CreateAppointmentModal
                            serviceId={serviceId}
                            demandFilter={demandFilter(
                                (serviceId &&
                                    sessionData.getServiceCategorisationByIds(serviceId, null)
                                        ?.id) ||
                                    null
                            )}
                            resourceFilter={resourceFilter()}
                            date={startDate || EccoDate.todayLocalTime()}
                            setShow={setShowAdHoc}
                        />
                    </EccoTheme>
                )}

                <Grid item={true} xs={12}>
                    <div className={classes.tableWrapper}>
                        <DataTableWrapper
                            data={rows}
                            serviceId={serviceId}
                            reload={() => getDataByDate()}
                        />
                    </div>
                </Grid>
            </Grid>
        </>
    );
};

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        filterElement: {
            margin: 20
        },
        button: {
            margin: 20
        },
        table: {
            verticalAlign: "top",
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        }
    })
);
