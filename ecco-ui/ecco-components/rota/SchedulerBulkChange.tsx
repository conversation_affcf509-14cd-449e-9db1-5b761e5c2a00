import * as React from "react";
import {FC, useState} from "react";
import {Autocomplete, Grid, TextField} from "@eccosolutions/ecco-mui";
import {possiblyModalForm} from "ecco-components-core";
import {WorkerJobSelector} from "./WorkerJobSelector";
import {useServicesContext} from "../ServicesContext";
import {EccoDate} from "@eccosolutions/ecco-common";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";

export const SchedulerBulkChangeCarerForm: FC<{
    serviceId: number | null;
    carerJobId: number | null;
    setCarerJobId: (carerJobId: number | null) => void;
}> = props => {
    return (
        <Grid container direction="row" justify="center" alignItems="center">
            {/*{this.props.task.created && ("task created " + this.props.task.created)}*/}
            {/*{textInput("description", "description", state => this.setState(state), this.state)}*/}
            <Grid item xs={12}>
                <div style={{width: 260, height: 38, marginLeft: "auto", marginRight: "auto"}}>
                    <WorkerJobSelector
                        labelName={"assign to"}
                        role={"ROLE_CARER"}
                        serviceIds={props.serviceId ? [props.serviceId] : null}
                        onChange={(
                            _contactId: number,
                            _contactName: string,
                            _calendarId: string,
                            workerJobId: number,
                            _serviceRecipientId
                        ) => props.setCarerJobId(workerJobId)}
                        selectedSrId={props.carerJobId}
                    />
                </div>
            </Grid>
            <Grid item xs={12}>
                <span>&nbsp;</span>
            </Grid>
        </Grid>
    );
};

export const SchedulerBulkChangeCarer: FC<{
    serviceId: number | null;
    open: boolean;
    onClose: () => void;
    onSave: (carerJobId: number | null) => void;
}> = props => {
    const [carerJobId, setCarerJobId] = useState<number | null>(null);

    return possiblyModalForm(
        "bulk reassign",
        true,
        props.open,
        props.onClose,
        () => props.onSave(carerJobId),
        !carerJobId,
        false,
        <SchedulerBulkChangeCarerForm
            serviceId={props.serviceId}
            carerJobId={carerJobId}
            setCarerJobId={setCarerJobId}
        />,
        "update",
        undefined,
        undefined,
        "md",
        0,
        false
    );
};

// NB see showDropReasonModal for drop reasons with rate cards
export const SchedulerBulkDropForm: FC<{
    dropReasonId: number | null;
    setDropReasonId: (completeId: number | null) => void;
}> = props => {

    const {sessionData} = useServicesContext();
    const list = sessionData && sessionData.getListDefinitionEntriesByListName("eventStatusRateId");

    const selected = (props.dropReasonId && sessionData && sessionData.getListDefinitionEntryById(props.dropReasonId)) || null;

    return (
        <Grid container direction="row" justify="center" alignItems="center">
            <Grid item xs={12}>
                <div style={{width: 260, height: 38, marginLeft: "auto", marginRight: "auto"}}>
                    <Autocomplete
                        id={"eccotest-drop"}
                        renderInput={params => (
                            // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                            <TextField {...params} label={"reason"} variant="outlined" />
                        )}
                        getOptionLabel={option => option.getDisplayName()}
                        getOptionSelected={(a, b) => a.getId() == b.getId()}
                        options={list}
                        value={selected}
                        onChange={(_event, obj) => {
                            props.setDropReasonId(obj?.getId() || null);
                        }}
                        /*style={{width: 270}}*/
                    />
                </div>
            </Grid>
            <Grid item xs={12}>
                <span>&nbsp;</span>
            </Grid>
        </Grid>
    );
};

export const SchedulerBulkDrop: FC<{
    open: boolean;
    onClose: () => void;
    onSave: (dropReasonId: number | null) => void;
}> = props => {
    const [dropReasonId, setDropReasonId] = useState<number | null>(null);

    return possiblyModalForm(
        "bulk drop",
        true,
        props.open,
        props.onClose,
        () => props.onSave(dropReasonId),
        !dropReasonId,
        false,
        <SchedulerBulkDropForm dropReasonId={dropReasonId} setDropReasonId={setDropReasonId} />,
        "update",
        undefined,
        undefined,
        "md",
        0,
        false
    );
};


// NB see showDropReasonModal for drop reasons with rate cards
export const SchedulerBulkMoveForm: FC<{
    moveTo: EccoDate | null;
    setMoveTo: (moveTo: EccoDate | null) => void;
}> = props => {

    return (
            <Grid container direction="row" justify="center" alignItems="center">
                <Grid item xs={12}>
                    <div style={{width: 260, height: 38, marginLeft: "auto", marginRight: "auto"}}>
                        <DatePickerEccoDate
                            name="date"
                            label="date"
                            value={props.moveTo}
                            onChange={date => props.setMoveTo(date)}
                            required={true}
                        />
                    </div>
                </Grid>
                <Grid item xs={12}>
                    <span>&nbsp;</span>
                </Grid>
            </Grid>
    );
};

export const SchedulerBulkMove: FC<{
    open: boolean;
    onClose: () => void;
    onSave: (moveTo: EccoDate | null) => void;
}> = props => {
    const [moveTo, setMoveTo] = useState<EccoDate | null>(null);

    return possiblyModalForm(
            "bulk move",
            true,
            props.open,
            props.onClose,
            () => props.onSave(moveTo),
            !moveTo,
            false,
            <SchedulerBulkMoveForm moveTo={moveTo} setMoveTo={setMoveTo}/>,
            "update",
            undefined,
            undefined,
            "md",
            0,
            false
    );
};
