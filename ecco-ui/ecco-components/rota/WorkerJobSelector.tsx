import * as React from "react";
import {FC} from "react";
import {Autocomplete, TextField} from "@eccosolutions/ecco-mui";
import {useWorkersWithAccessToEmployedAt} from "../data/entityLoadHooks";
import {EccoDate} from "@eccosolutions/ecco-common";
import {StaffDto} from "ecco-dto";

export const WorkerJobSelector: FC<{
    labelName: string;
    serviceIds: null | number[];
    role: string;
    onChange: (
        contactId: number,
        contactName: string,
        calendarId: string,
        workerJobId: number,
        serviceRecipientId: number
    ) => void;
    selectedSrId: number | null;
}> = props => {
    const {workersWithAccessToEmployedAt} = useWorkersWithAccessToEmployedAt(
        props.serviceIds,
        EccoDate.todayLocalTime(),
        props.role
    );
    if (!workersWithAccessToEmployedAt) {
        return null;
    }

    return (
        <WorkerJobSelectorOnly
            labelName={props.labelName}
            onChange={props.onChange}
            workersWithAccessToEmployedAt={workersWithAccessToEmployedAt}
            selectedSrId={props.selectedSrId}
        />
    );
};

export const WorkerJobSelectorOnly: FC<{
    labelName: string;
    workersWithAccessToEmployedAt: StaffDto[];
    onChange: (
        contactId: number,
        contactName: string,
        calendarId: string,
        workerJobId: number,
        serviceRecipientId: number
    ) => void;
    selectedSrId: number | null;
}> = props => {
    const findWorkerWithJobId = (id: number) =>
        props.workersWithAccessToEmployedAt
            .filter(w => w.jobs?.map(j => j.id).indexOf(id) > -1)
            ?.pop();
    const findWorkerJob = (id: number) =>
        props.workersWithAccessToEmployedAt
            .map(w => w?.jobs)
            .reduce((r, x) => r.concat(x), []) // flatMap
            .filter(j => j.id == id)
            .pop();

    function onChange(value: number | null) {
        const selectedWorker = value && findWorkerWithJobId(value);
        const selectedJob = value && findWorkerJob(value);
        if (selectedWorker && selectedJob) {
            props.onChange(
                selectedWorker.contactId,
                selectedWorker.displayName,
                selectedWorker.calendarId,
                selectedJob.id,
                selectedJob.serviceRecipient.serviceRecipientId
            );
        }
    }
    const allList = props.workersWithAccessToEmployedAt
        .map(w =>
            w.jobs.map(j => {
                return {
                    contactId: w.contactId,
                    contactName: w.displayName,
                    calendarId: w.calendarId,
                    workerJobId: j.id,
                    serviceRecipientId: j.serviceRecipient.serviceRecipientId
                };
            })
        )
        .reduce((r, x) => r.concat(x), []); // flatMap
    const list = allList.filter(d => d.serviceRecipientId != props.selectedSrId);
    const selected = allList.filter(d => d.serviceRecipientId == props.selectedSrId)?.pop();

    return (
        <Autocomplete
            id={"eccotest-carers"}
            renderInput={params => (
                // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                <TextField {...params} label={props.labelName} variant="outlined" />
            )}
            getOptionLabel={option => option!.contactName}
            getOptionSelected={(a, b) => a!.workerJobId == b!.workerJobId}
            options={list}
            value={selected}
            onChange={(_event, obj) => {
                onChange(obj?.workerJobId || null);
            }}
            /*style={{width: 270}}*/
        />
    );
};
