import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {Icon} from "@eccosolutions/ecco-mui";
import {Tabs} from "@eccosolutions/ecco-mui";
import {useMediaQuery} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {ReactNode, useState} from "react";
import {Tab} from "@eccosolutions/ecco-mui";
import {TabPanel} from "../layout/TabPanel";
import {handleLazy} from "../Loading";

/**
 * Builds a tab component *on each render* with the ability to show and hide tabs according to permissions.
 * NOTE: Do not make this conditional within the consuming function (i.e. Do allow it to render the tabs).
 * You can make the content conditional if it is waiting on data from a hook.
 */
export class TabsBuilder {
    // This could impl an interface that allows addMenu, addDivider for different output
    /** Tabs are added as calls are made to addTab */
    private tabs: ReactNode[] = [];

    /** We select just the one when we call build */
    private tabContentItems: StringToObjectMap<ReactNode> = {};

    private tab: string | undefined;
    private setTab: (tab: string) => void;
    private firstTab: string | null = null; // set on first call to addTab

    constructor(
        private showText = useMediaQuery("(min-width: 400px)") // TODO: use breakpoints?

        // private routerPath: string
    ) {
        [this.tab, this.setTab] = useState<string>(); // If you get a hooks fail here. See doc for this class.
    }

    addTab(
        label: string,
        tabContent: ReactNode | (() => ReactNode),
        condition = true,
        iconClass?: string | undefined
    ) {
        if (condition) {
            this.firstTab = this.firstTab || label;
            this.tabs.push(
                // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                <Tab
                    key={label}
                    label={this.showText ? label : undefined}
                    value={label}
                    icon={this.showText ? undefined : <Icon className={`fa ${iconClass}`} />}
                    onClick={() => this.setTab(label)}
                />
            );
            this.tabContentItems[label] =
                typeof tabContent === "function" ? tabContent() : tabContent;
        }
        return this;
    }

    build() {
        return (
            <>
                <Tabs
                    value={this.tab || this.firstTab}
                    onChange={(_event, value) => this.setTab(value)}
                >
                    {this.tabs}
                </Tabs>
                <TabPanel value={this.tab} index={this.tab}>
                    {handleLazy(this.tabContentItems[this.tab || this.firstTab!])}
                </TabPanel>
            </>
        );
    }
}