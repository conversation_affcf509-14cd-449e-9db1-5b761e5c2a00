import {ApiClient, isOffline, setGlobalApiClient} from "ecco-dto";
import {askUserForCredentials} from "./inputs/LoginDialog";
import {formThenOfflineLogin} from "ecco-offline-data";

const {applicationRootPath, remoteHost} = window.applicationProperties;

console.assert(applicationRootPath.endsWith("/"));

const apiUrl = new URL(`${applicationRootPath}api/`, remoteHost || window.location.href).href;
console.debug(`environment.ts: initialising ApiClient with path ${apiUrl}`);

/** @deprecated FIXME: Get rid of this one */
export const apiClient = new ApiClient(
    apiUrl,
    askUserForCredentials,
    isOffline,
    {credentials: window.global_credentials, attemptReAuth: true},
    formThenOfflineLogin
);

setGlobalApiClient(apiClient);