import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {Fragment} from "react";
import {SessionData} from "ecco-dto";
import {createInstance} from "react-async";
import {LoadingSpinner} from "./Loading";
import {assertNotNull} from "ecco-commands";
import {getGlobalEccoAPI, hasGlobalEccoAPI} from "./EccoAPI";
import {WebApiError} from "@eccosolutions/ecco-common";
import {stringifyPossibleError} from "ecco-offline-data";


/** @deprecated Not for use directly. You should use something like mountWithServices(), and then
 * useServicesContext().sessionData to get at session data.
 */
export const AsyncSessionData = createInstance<SessionData>({}, "SessionData");

/** @deprecated It's preferable to use useServicesContext - apart from in ServicesContextProvider */
export function withSessionData(children: (value: SessionData) => JSX.Element, spinner = <LoadingSpinner/>) {
    if (hasGlobalEccoAPI()) {
        console.debug("withSessionData() used where sessionData already set globally - consider migrating to useServicesContext()");
        return children(getGlobalEccoAPI().sessionData);
    }

    return <Fragment>
        <AsyncSessionData.Loading>
            {spinner}
        </AsyncSessionData.Loading>
        <AsyncSessionData.Resolved>
            {(data: SessionData) => {
                assertNotNull(data, "AsyncSessionData resolved to null")
                return children(data);
            }}
        </AsyncSessionData.Resolved>
        <AsyncSessionData.Rejected>
            {(error: WebApiError) => {
                console.error(error);
                return (
                    <>
                        <Snackbar
                            open={true}
                            message={
                                <span>
                                    Request failed:{" "}
                                    {error.statusCode == 403
                                        ? error.reason.message
                                        : stringifyPossibleError(error)}
                                </span>
                            }
                            action={
                                <Button
                                    color="secondary"
                                    size="small"
                                    onClick={() => {
                                        error.statusCode == 403 && window.location.reload();
                                    }}
                                >
                                    {error.statusCode == 403 ? "Try again" : "No connection?"}
                                </Button>
                            }
                            // autoHideDuration={5000}
                            onClose={() => {
                                /*Would handle autoHide*/
                            }}
                        />
                    </>
                );
            }
            }
        </AsyncSessionData.Rejected>
    </Fragment>;
}