// This should be specified in Configurations -> Templates -> Jest as the config file

/** @type {import('jest').Config} */
const config = {
    testEnvironment: "jsdom",
    testEnvironmentOptions: {
        // url: 'https://jestjs.io/',
    },
    transform: {
        "^.+\\.tsx?$": [
            "ts-jest",
            {
                tsconfig: "tsconfig.json"
            }
        ]
    },
    testRegex: ".*/__tests__/.*([Tt]est|[Ss]pec)\\.(tsx?)$",
    setupFiles: ["./__tests__/setupJest.ts"],
    moduleFileExtensions: ["ts", "tsx", "js", "json", "node"],
    moduleNameMapper: {
        bowser: "<rootDir>/__mocks__/bowser",
        services: "<rootDir>/__mocks__/null",
        punycode: "<rootDir>/__mocks__/null",
        IPv6: "<rootDir>/__mocks__/null",
        SecondLevelDomains: "<rootDir>/__mocks__/null"
    },
    modulePaths: ["<rootDir>"],
    snapshotSerializers: ["enzyme-to-json/serializer"]
    // collectCoverage: true,
    // mapCoverage: true
};
module.exports = config;
