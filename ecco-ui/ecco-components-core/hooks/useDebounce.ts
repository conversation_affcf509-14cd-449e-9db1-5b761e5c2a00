import {useEffect, useState} from "react";

/** Hook that debounces any changes to value, such that the returned value is only changed from the original
 * once the timeout has passed without a change.
 * NOTE: Ensure you useMemo() if value is an object that might change
 */
export function useDebounce<T>(value: T, timeoutMs: number) {
    const [state, setState] = useState(value);
    console.debug(`value: ${value} state: ${state}`);
    useEffect(() => {
        console.debug(`debounce updated value from ${state} to ${value}`);
        const handle = setTimeout(() => {
            console.debug(`debounce timeout: emitting ${value}`);
            return setState(value);
        }, timeoutMs);

        return () => {
            console.debug(`debounce clearTimeout: orig value ${value}`);
            clearTimeout(handle);
        };
    }, [value, timeoutMs]);

    return state;
}
