import {useCallback, useEffect, useState} from "react";

/** Access local or session storage as a hook with event handler to cause re-render if value is changed by
 * a different hook. */
export function useStorage(
    storageType: "localStorage" | "sessionStorage",
    storageKey: string,
    initialValue: string
): [string, (value: string) => void] {
    const [value, setValue] = useState<string>(self[storageType][storageKey] ?? initialValue);
    const handler = useCallback(() => {
        if (value != self[storageType][storageKey]) {
            setValue(self[storageType][storageKey]);
        }
    }, [storageKey, storageType]);

    useEffect(() => {
        window.addEventListener("storage", handler);
        return () => window.removeEventListener("storage", handler);
    }, [storageKey, storageType]);

    const setter = useCallback(
        (value: string) => {
            self[storageType][storageKey] = value;
            setValue(value);
            window.dispatchEvent(new Event("storage")); // Because storage events are only between tabs not within same page
        },
        [storageKey, storageType]
    );

    return [value, setter];
}

/** From https://usehooks.com/useLocalStorage/
 * FIXME: Prefer useStorage() above as it
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
    // State to store our value
    // Pass initial state function to useState so logic is only executed once
    const [storedValue, setStoredValue] = useState<T>(() => {
        try {
            const item = window.localStorage.getItem(key);
            // Parse stored json or if none return initialValue
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            // If error also return initialValue
            console.log(error);
            return initialValue;
        }
    });
    // Return a wrapped version of useState's setter function that ...
    // ... persists the new value to localStorage.
    const setValue = (value: T | ((val: T) => T)) => {
        try {
            // Allow value to be a function so we have same API as useState
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            // Save state
            setStoredValue(valueToStore);
            window.localStorage.setItem(key, JSON.stringify(valueToStore));
        } catch (error) {
            // A more advanced implementation would handle the error case
            console.log(error);
        }
    };
    return [storedValue, setValue] as const;
}

export function getJsonLocalStorage() {
    return {
        get: function <T>(key: string) {
            const item = localStorage[key];
            return item ? (JSON.parse(item) as T) : null;
        },

        set: function <T>(key: string, value: T | null | ((prev: T | null) => T | null)) {
            // Allow value to be a function so we have same API as useState
            const obj = value instanceof Function ? value(this.get(key)) : value;
            localStorage[key] = obj == null ? null : JSON.stringify(obj);
        },

        /** Does what is stored match value serialised to JSON */
        matches: function <T>(key: string, value: T | null) {
            return localStorage[key] == JSON.stringify(value);
        }
    };
}
