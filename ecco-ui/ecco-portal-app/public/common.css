@charset "UTF-8";

html {
  font-family: sans-serif;
  font-size: initial; /* Revert Bootstrap */
}

/* OVERRIDE bootstrap themes */

/* Flatten buttons: this can be deleted when we update BS to v3 default CSS */
.btn {
  background-image: none;
}

.react-select {
  z-index: 2;
}

.MuiIcon-root.fa {
  overflow: visible; /* Deals with quirk in  */
  font-size: 1.3em;
  text-align: center;
}

.MuiAppBar-colorPrimary {
  background-color: #0d83ca !important; /* Forced due to global bug in MUI styles */
}

.MuiListItem-button:hover,
.MuiListItem-button:focus {
  color: inherit; /* Can remove when bootstrap isn't in the mix */
  text-decoration: snow;
}

/** Limit width by default for modern pages. Must be overridden for reports etc */
html.v2 {
  max-width: 1160px;
  margin-left: auto;
  margin-right: auto;
}

html.referral-router {
  max-width: 768px;
}

html.login,
html.logout {
  max-width: 392px;
}

html.login {
  background: linear-gradient(#fff 0%, rgba(60, 131, 202, 0.08) 50%, #fff 100%);
}

.login body,
.logout #bannerContainer {
  background: none;
}

html.menu-page {
  max-width: 768px;
}

/** Give migrated forms some space before them allowing <br> to be removed where it's nastily in there */
html.layout-wide form {
  margin-top: 25px;
}

/** Override for embedded action forms such as on taskList.jsp */
html.layout-wide form.actionHidden {
  margin-top: 0;
}

html.new-layout form#referral {
  min-height: 25em;
}

.double-spaced,
.double-spaced .btn {
  line-height: 2.5em;
}

.menu-page .entityForm {
  margin: 15px 8px 8px;
}

.login .box {
  margin-top: 12px;
  min-height: 232px;
  padding: 12px 12px 24px 12px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

html.reports,
html.new-layout {
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

div.notifications {
  position: fixed;
  right: 0;
  top: 50px;
  background-color: rgba(192, 192, 192, 0.3);
  padding: 20px 20px 0;
  margin-bottom: 20px;
  border-radius: 0;
  z-index: 12; /* in front of banner-titlebar */
}
div.notifications:empty {
  display: none;
}

/**********/
/* Navbar */
/**********/
.navbar {
  margin: 0;
  border: 0;
}
.navbar-default {
  box-shadow: none;
  background: transparent none;
}

.navbar-collapse {
  background-color: white;
}
.navbar-brand {
  padding: 15px !important; /* Override ecco things like a:link */
}
.navbar-brand {
  padding-top: 6px !important;
  padding-left: 12px !important;
}
.navbar-title {
  float: left;
  font-size: 18px;
  font-style: italic;
  padding-top: 11px;
}

.navbar-fixed-top {
  border-width: 0;
}

/* Could poss do a bit better than this, but it does the trick */
.navbar span.fa.fa-user,
.navbar i.fa.fa-user {
  line-height: 20px;
}

a[href^="tel:"]:before {
  content: "\260E";
  display: inline-block;
  margin-right: 0.5em;
}
body {
  font-size: 16px; /* as per house style but after bootstrap which opts for 14 */
}

h1 {
  margin-top: 0.2em;
  margin-bottom: 0em;
}

/** override bootstrap default of 700 */
label {
  font-weight: normal;
}

/*--------------------------------------*/
/* Apply some style we do want */
small {
  font-size: 0.8em;
}

strong {
  font-weight: bold;
}

em {
  font-style: italic;
}

p {
  margin-top: 1em;
  margin-bottom: 1em;
}

/** Bootstrap style on all but input */
textarea,
select,
input,
div[contenteditable="true"] {
  padding: 4px;
  font-size: 15px;
  color: #555;
  background: #fff none;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

/** For where we use contenteditable on a textarea */
div.textarea {
  min-height: 70px;
  height: auto;
  display: block;
  white-space: pre-wrap;
}

input[type="image"] {
  border: none;
  padding: 0;
}

textarea:focus,
select:focus,
input:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}

.m-ui textarea,
.m-ui select,
.m-ui input,
.m-ui div[contenteditable="true"],
.m-ui input:focus {
  box-shadow: none;
  /* Perhaps only box shadow was the issue...*/
  /*border: none;*/
}

/* Customise btn-link 'a' links */
a.btn-link,
a.btn-link:visited {
  color: #3c83ca;
}

a.btn-link.prev-state {
  font-style: italic;
}

a.btn-link.curr-state {
  font-weight: bold;
}

button.btn-link:hover,
a.btn-link:hover,
a.btn-link:visited:hover {
  color: #f7f6f6;
  background: #3c83ca;
  text-decoration: none;
}
button.btn-link:active,
a.btn-link:active {
  color: #f7f6f6;
  background: #3c83ca;
}

/* see elsewhere where we change 'a' to a:link...' since 'a' can be an anchor too */
a.btn-link,
a.btn-link:visited,
.dragText {
  padding: 2px 6px;
  text-decoration: none;
  display: inline-block; /* so that wrapped link doesn't get padding stagger */
}

.blurBlock a:link,
.blurBlock a:visited {
  color: #878787;
}

.blurBlock a:link:hover,
.blurBlock a:visited:hover,
.blurBlock .active a,
.blurBlock a.active,
.blurBlock a:active {
  color: #fff;
  background: #d0d0d0;
  text-decoration: none;
}
.blurText {
  color: #878787 !important;
}
.buttonBlur,
.btn.disabled,
.ra-task .btn[disabled] {
  color: #404040;
  background: none transparent;
  opacity: 0.65; /* aligning with .btn.disabled */
}
.buttonBlur:hover {
  color: #fff;
  background: #d0d0d0;
}
.halfbutton {
  color: #404040;
}
.button {
  color: #3c83ca;
  background: none transparent;
}
.button:hover,
.halfbutton:hover,
#mailList tr.dblclick:hover,
.dragText {
  color: #f7f6f6;
  background: #3c83ca;
  text-decoration: none;
}

/* HIDE WHEN PRINTED */
/* NB can now use bootstrap hidden-print and .visible-print-x */
@media print {
  .no-print {
    display: none;
  }
}

.center {
  /* display: block; */
  margin-left: auto;
  margin-right: auto;
  /* position: relative; */
  text-align: center;
}

.current-user,
.navbar .glyphicon,
.navbar .fa {
  color: #777 !important; /* currently trashed by main-styles.css which we need to incorporate into bootstrap theme */
}

.nosplit {
  white-space: nowrap;
}

.error {
  text-align: center;
}
.messages {
  text-align: center;
}

.inline {
  display: inline;
}

div.menu {
  margin: 0 140px;
}

div.menu :focus {
  /* Override bootstrap thick stuff - mainly for quick guide item retaining focus after dlg dismissed */
  outline: thin dotted;
}

.border {
  border: 1px #3c83ca solid;
  border-radius: 10px;
}

.thick {
  border-width: 2px;
}

/**********/
/* Forms  */
/**********/
.button,
.buttonBlur {
  border: 0;
  margin: 0;
  padding: 0 0;
  text-decoration: none;
  /* seems not in firefox */
  cursor: pointer;
}
/* match the padding with the links */

input[type="submit"].button,
a.button,
span.button,
span.buttonBlur,
button.button,
button.buttonBlur {
  border: 0;
  margin: 0;
  padding: 2px 6px;
  border-radius: 0;
  box-shadow: none;
}

.m-ui span.button {
  padding: 2px 0;
}

.pagenum a,
.pagenum button {
  padding: 2px 2px;
  text-decoration: none;
}
.pagenum .disabled {
  border-radius: 3px;
  background-color: #3c83ca;
  color: white;
}

div.pagenum {
  margin-bottom: 1em;
  padding-bottom: 1em;
}

.e-row:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

div.e-row {
  margin: 5px 0;
}

.e-row span.e-label,
.e-row label.e-label {
  float: left;
  width: 48%;
  text-align: right;
  text-transform: lowercase;
  padding-right: 0;
}

.e-label,
.e-row label.e-label {
  font-size: 15px;
  font-weight: normal;
  color: #222;
}

div.actionbar {
  text-align: center;
}

div.entityForm .e-label .btn {
  font-size: 15px; /* for the moment force BS btn class to be our size, not BS default  */
}
div.entityForm .e-label.blurText {
  font-size: 15px; /* to avoid below size change  */
  padding-right: 6px;
}
.e-label.blurText {
  font-size: 14px; /* to match btn btn-link from bootstrap */
  padding: 2px 6px;
}
.e-label a.small {
  font-size: 13px;
}

.form-30-50 label {
  width: 30%;
  text-align: right;
  margin-right: 8px;
}
.form-30-50 .form-control {
  width: 50%;
  display: inline;
}

.form-30-50 .form-control-text {
  width: 50%;
  display: inline-block;
  text-align: right;
}

.form-30-50 .input-group-wrapper {
  display: inline-block;
  width: 50%;
  vertical-align: middle;
}

.form-30-50 .input-group-wrapper .form-control {
  width: 100%;
}

.form-responsive .form-group {
  margin: 15px -15px;
}

.datepicker-addon {
  padding: 2px 5px;
}

.e-row span.input,
.e-row .input-group {
  float: right;
  width: 48%;
  text-align: left;
}

.e-label i {
  padding-top: 4px;
}
.e-label i,
.e-label div {
  float: right;
}

@media (max-width: 899px) {
  .e-row span.e-label,
  .e-row label.e-label {
    width: 40%;
  }
  .e-row span.input,
  .e-row .input-group {
    width: 58%;
  }
}
@media (max-width: 639px) {
  .e-row span.e-label,
  .e-row label.e-label {
    width: 100%;
    text-align: left;
  }
  .e-row span.input,
  .e-row .input-group {
    width: 90%;
    text-align: left;
  }
  .e-label .hover-reveal {
    float: right;
  }
  .e-label i,
  .e-label div {
    float: left;
  }
}

/** Force narrow such as when in BS col-md-6 etc */
.force-narrow .e-row span.e-label {
  width: 100%;
  text-align: left;
}
.force-narrow .e-row span.input,
.force-narrow .e-row .input-group {
  width: 90%;
  text-align: left;
}
.force-narrow .e-label .hover-reveal {
  float: right;
}
.force-narrow .e-label i,
.force-narrow .e-label div {
  float: left;
}

/*********************/
/* Accelerator keys */
/********************/
.accelerator-hint-container {
  position: relative;
}

.accelerator-hint {
  position: absolute;
  display: block;
  top: 12px;
  left: 4px;
  border-radius: 6px;
  padding: 4px;
  background: #3c83ca;
  color: #fff;
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 0.4s;
  transition-delay: 0.2s;
  transition-timing-function: ease-in;
}

.show-accelerator-hints .accelerator-hint {
  top: -4px;
  opacity: 1;
  transition-property: none;
}

.invisible {
  visibility: hidden;
}

#guidance {
  color: goldenrod;
}
#guidance * {
  text-align: left;
}

#guidance ol,
#guidance ul {
  list-style: disc;
  margin-left: 1em;
}

#guidance h1,
#guidance h2 {
  font-weight: bold;
  margin-bottom: 0.5em;
}

#guidance p,
#guidance ul {
  margin-bottom: 0.5em;
}
/******************************

/* Note must have span inside link for this to work http://stackoverflow.com/questions/4929310/why-isnt-css-visibility-working */
a.hover-reveal span {
  visibility: hidden;
}
a.hover-reveal:hover span {
  visibility: visible;
}

.e-row.hover-reveal span.row-reveal {
  visibility: hidden;
}
.e-row.hover-reveal:hover span.row-reveal {
  visibility: visible;
}

.task-available > i.fa.fa-check-circle {
  color: lightgrey !important;
}

.task-completed > i.fa.fa-check-circle,
i.fa.fa-check-circle.selected {
  color: lightgreen !important;
}

.task-due {
  color: darkorange;
}

.task-overdue {
  color: red;
}

.lightgrey,
i.fa-check-circle,
.radiobuttons i.fa {
  color: lightgrey;
}

.row.disabled {
  color: #bbb;
}

.grey {
  color: grey;
}

i.fa.selected {
  width: 1em;
  margin-right: 0.2em;
  text-align: center;
}

.rag-green,
.selected .green,
.green.selected,
.selected i.fa-check-circle {
  color: lightgreen !important;
}

.rag-amber,
.selected .amber,
.amber.selected,
.selected i.fa-warning,
i.fa-warning.selected {
  color: orange !important;
}

.rag-red,
.selected .red,
.red.selected,
.selected i.fa-times,
i.fa-times.selected {
  color: red !important;
}

span.assignee {
  font-size: 0.9em;
  margin-left: 20px;
  /*     font-style: italic; */
  color: orange;
}

i.task-assigned,
i.task-unavailable {
  color: lightgrey;
}

i.task-owner,
i.task-owner.clickable {
  color: orange;
}

.searchable-list {
  margin-top: 20px;
}
.searchable-list .panel-group {
  margin-top: 5px; /* ensure first item is spaced from input above */
}

/* Override entry-list > li margins when it's a searchable list */
.searchable-list ul.entry-list.list-unstyled > li {
  margin: 8px 0;
}

/* Fixup bootstrap's inherit mixing badly with jqueryui.css */
.ui-datepicker button,
.ui-datepicker input,
.ui-datepicker optgroup,
.ui-datepicker select,
.ui-datepicker textarea {
  color: black;
  font-weight: normal;
}

/* Prevent input within a control with datepicker from getting outlined too */
.date-input.form-control input {
  border: none;
}

/* Override for more compact style */
.modal-header {
  padding: 10px 15px;
}
.modal-footer {
  padding: 10px 20px;
}
/* Ensure select lists don't autosize off the side of the modal */
.modal select {
  max-width: 100%;
}

/* For use alongside Bootstrap .modal-dialog - gives full width vs .modal-lg */
.modal-full {
  position: fixed;
  margin: 0;
  width: 100%;
}
.modal-full .modal-body {
  overflow-y: auto !important;
}
@media (min-width: 1330px) {
  .modal-full {
    position: fixed;
    width: 1300px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* .modal-lg is disabled by bootstrap-modal-bs3patch.css; reinstate it here. */
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}

.chart {
  overflow-y: scroll;
  overflow-x: hidden;
}
/* make chart smaller on shallow displays */
@media (max-height: 768px) {
  .chart {
    height: 220px;
  }
}

.radar-chart-col {
  text-align: center;
  float: left;
  margin: 0;
  width: 45%;
  min-width: 220px;
}
.radar-chart > svg,
.radar-chart > div {
  /* It's a div in IE8 */
  margin-left: auto;
  margin-right: auto;
  display: block;
}

div.dynamic-tree {
  margin: -20px 0 0;
}

.dynamic-tree > svg {
  width: 100% !important;
  -webkit-filter: drop-shadow(0 0 4px #a2b7d8);
  filter: drop-shadow(0 0 4px #a2b7d8);
}

.smart-step-score-control span {
  margin: 10px;
  padding: 5px;
}

.smart-step-score-control span.selected {
  border: 1px solid grey;
}

@media (min-width: 992px) {
  .abs-top-right {
    position: absolute;
    z-index: 1;
    right: 5px;
    top: 5px;
    border-radius: 5px;
    border: 1px solid rgb(211, 211, 211);
    padding: 10px;
    height: auto;
    background: rgba(255, 255, 255, 0.94);
  }
}

.spinner {
  display: block;
  margin: 0 auto;
  width: 16px;
  height: 16px;
}

@media print {
  a[href]:after {
    content: none; /* reset bootstrap behaviour */
  }
}

.address > span {
  white-space: nowrap;
}
.address > span:after {
  content: ", ";
}
.address > span:last-child:after {
  content: "";
}

.id-badge {
  width: 100%;
  margin-bottom: 12px;
}
.id-badge .thumbnails {
  float: left;
  width: 160px;
  margin: 12px 12px 0;
}

.id-badge > div {
  margin-top: 12px;
}

.id-badge div.fullish,
.id-badge div.halfish {
  float: left;
  margin: 0 12px 0;
  min-width: 70px;
}

.id-badge h4 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.id-badge a {
  margin-left: -6px; /* compensate for the 6px padding on our link styling */
}

.id-badge dd,
.id-badge dt {
  margin-left: 0;
}

.legacy-avatar-control .image-drop,
.legacy-avatar-control .image-overlay {
  min-height: 100px;
  text-align: center;
}
.legacy-avatar-control .image-drop {
  border: 1px solid #ddd;
  background-color: #eee;
}
.legacy-avatar-control .image-overlay {
  z-index: 1;
  margin: -4px;
  width: 100%;
  height: 100%;
  background: #eeeeee; /* Fallback solid colour */
  background: rgba(238, 238, 238, 0.75);
}
.legacy-avatar-control .image-drop > .placeholder,
.legacy-avatar-control .image-overlay > .placeholder {
  padding: 40px 10px;
}

/* this is what causes the horizontal scroll bar */
#logo {
  position: fixed;
  left: 2px;
  top: 0;
  padding: 5px;
}

.rounded {
  background-color: rgba(0, 0, 0, 0.02);
  padding: 4px 4px 4px 4px;
  border: 1px solid #a2b7d8;
  border-radius: 10px;
}

@media print {
  .rounded {
    border: 0;
  }
}

.top-divider {
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid #a2b7d8;
  padding: 4px 4px 4px 4px;
}

.error-page {
  margin: 10px 12px;
  padding: 80px 20%;
}

.error-page > div,
.error-page > p {
  text-align: center;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

#footer {
  text-align: center;
}

#footer img {
  margin-bottom: -5px;
  max-height: 75px;
}

.quick-guide h1 {
  text-align: left;
  font-weight: bold;
}

.quick-guide-snippet {
  padding: 15px 35px;
}

a.navbar-brand img {
  height: 30px;
}

#network-activity-control {
  position: fixed;
  top: 40%;
  width: 100%;
}
#network-activity-control > div {
  margin-left: auto;
  margin-right: auto;
  width: 150px;
  padding: 60px;
  background: rgba(224, 224, 224, 0.8);
  border-radius: 10px;
  text-align: center;
}

/* From:
 * Start Bootstrap - SB Admin 2 Bootstrap Admin Theme (http://startbootstrap.com)
 * Code licensed under the Apache License v2.0.
 * For details, see http://www.apache.org/licenses/LICENSE-2.0.
 */
.huge {
  font-size: 28px;
}

.panel-green {
  border-color: #5cb85c;
}

.panel-green .panel-heading {
  border-color: #5cb85c;
  color: #fff;
  background-color: #5cb85c;
}

.panel-green a.panel-link,
.panel-green .panel-footer {
  color: #5cb85c;
}

.panel-green a.panel-link:hover {
  color: #3d8b3d;
}

.panel-red {
  border-color: #d9534f;
}

.panel-red .panel-heading {
  border-color: #d9534f;
  color: #fff;
  background-color: #d9534f;
}

.panel-red a.panel-link,
.panel-red .panel-footer {
  color: #d9534f;
}

.panel-red a.panel-link:hover {
  color: #b52b27;
}

.panel-yellow {
  border-color: #f0ad4e;
}

.panel-yellow .panel-heading {
  border-color: #f0ad4e;
  color: #fff;
  background-color: #f0ad4e;
}

.panel-yellow a.panel-link,
.panel-yellow .panel-footer {
  color: #f0ad4e;
}

.panel-yellow a.panel-link:hover {
  color: #df8a13;
}

.panel-primary a.panel-link,
.panel-primary .panel-footer {
  color: #428bca;
}

.panel-comment {
  font-size: 85%;
}

.panel-ecco {
  color: #3c83ca;
  border-color: #a2b7d8;
  background-color: rgba(0, 0, 0, 0.02);
}

.panel-ecco > .panel-heading {
  /*background-color: #fff;*/
}

.panel-ecco > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #428bca;
}
.panel-ecco > .panel-heading .badge {
  color: #3c83ca;
  /*color: #428bca;*/
  /*background-color: #fff*/
}

.panel-ecco > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #a2b7d8;
}

/* Responsive layout for id-badge and menus */
.new-layout #context-badge {
  margin-left: -15px;
  margin-bottom: -18px;
}

.new-layout #main-content {
  min-height: 350px; /* For now so that footer isn't right 'up there' with small pages */
}

.table-control {
  margin-top: 25px;
  margin-bottom: 25px;
}

.table-control table {
  overflow-x: scroll;
  display: block;
}

@media (min-width: 768px) {
  /* context-badge is an image (photo or brand logo + some useful info) */
  .new-layout #context-badge {
    position: absolute;
    width: 200px;
    left: 4px;
    top: 61px;
  }
  .new-layout .row.context-badge-row.rounded {
    /* override style of container when we abs the #context-badge */
    border: none;
    border-radius: 0;
    padding: 0;
    margin-top: 0;
  }
  .new-layout body {
    margin-left: 200px;
    max-width: 900px; /* because we only want really wide for reports */
  }
  .new-layout .navbar-header {
    margin-left: -200px;
  }
}
@media (max-width: 767px) {
  .id-badge div.fullish {
    width: calc(100% - 190px);
    min-width: 220px;
  }
  .id-badge div.halfish {
    width: 200px;
  }
}
@media (max-width: 697px) {
  .id-badge div.fullish {
    width: calc(100% - 190px);
    min-width: 180px;
  }
  .id-badge div.halfish {
    width: 150px;
  }
  .id-badge .thumbnails {
    width: 120px;
  }
  .legacy-avatar-control .image-drop > .placeholder,
  .image-overlay > .placeholder {
    padding: 24px 10px;
  }
}

.my-plan-history .unchanged,
.my-plan-history .updated {
  font-weight: bold;
}

@media (min-width: 768px) {
  .my-plan-history dd,
  .my-plan-history dt,
  #myPlanControl dd,
  #myPlanControl dt {
    margin-left: 0;
    font-size: 0.9em;
  }
  .my-plan-history .col-sm-3,
  #myPlanControl .col-sm-3 {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.top-gap-15 {
  margin-top: 15px;
}
.bottom-gap-15 {
  margin-bottom: 15px;
}

.v-gap-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}

/* Override bootstrap so label and help block can be on same line */
.help-block {
  margin-top: 0;
  padding: 4px 0;
  margin-bottom: 5px;
}
.comment-form {
  margin-top: 15px; /* helps align with top of table when laid out on right */
}

.select2-container--open {
  z-index: 10001; /* hack instead of calculating - so it's in front of a modal*/
}
.select2-container {
  min-width: 50%;
}

/* Our Bootstrap adaptations */
.ecco-rounded {
  background-color: rgba(0, 0, 0, 0.02);
  padding-left: 10px;
  padding-right: 10px;
  border: 1px solid #a2b7d8;
  border-radius: 4px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
  margin: 4px -11px; /* default margin applied to col-* */
}

/* Remove border so looks more Material Design */
button.btn-default.dropdown-toggle {
  border-width: 0;
  box-shadow: none;
}

/* fudge it down a bit on .v3 */
.v3 button.btn-default.dropdown-toggle {
  margin-top: 2px;
}

/* Adapt margins so can apply directly to .container & container-fluid */
.container > .ecco-rounded,
.container-fluid > .ecco-rounded {
  margin: 4px -7px; /* left-right we double to give 8 at edges */
}

.v3 .ecco-rounded {
  border: 1px #ddd solid;
  /* MUI paper elevation 1 */
  border-radius: 4px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 8px;
}

/* Also apply ecco-row-rounded to the row element to override boostraps margins
 * NOTE: The row isn't rounded. It's so we can have .ecco-rounded and .tab-content inside */
.ecco-rounded-row {
  margin-left: -11px; /* we add 4px (our margin) to give us -15 + 4 instead of -15 */
  margin-right: -11px;
}

.ecco-rounded > .ecco-inset,
.ecco-rounded > pre {
  margin-top: 10px;
}

.ecco-rounded-panel {
  padding-left: 0;
  padding-right: 0;
  border-radius: 10px;
  margin: 4px -11px; /* default margin applied to col-* */
}
.v3 .ecco-rounded-panel {
  border-radius: 0;
}

.ecco-rounded-panel > .panel-heading {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.ecco-rounded-panel > .panel-heading.panel-no-footer {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-width: 0;
}

.ecco-rounded-panel > .panel-footer {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

/* ul.entry-list should be the equiv of a div with lots of container-rounded, where the child
 * li's are the container-rounded, hence the ui.entry-list > li rule */
.entry-list > li {
  list-style-type: none;
  margin: 12px 8px;
  background-color: white;
  padding: 5px 10px;
  border: 2px #ddd solid;
  /* MUI paper elevation 1 */
  border-radius: 5px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.v3 .entry-list {
  margin-top: 12px;
}
.v3 .entry-list > li {
  /* i.e. Material UI card */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 6px, rgba(0, 0, 0, 0.117647) 0 1px 4px;
  transition: all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
  box-sizing: border-box;
  font-family: Roboto, sans-serif;
  color: rgba(0, 0, 0, 0.870588);
  background-color: rgb(255, 255, 255);
  border-radius: 2px;
  border: none;
  z-index: 1;
  margin-top: 0;
  /*margin-bottom: 0;*/
}

.entry-list > li.add-entry {
  border-style: dashed;
  text-align: center;
  color: #777;
}

.entry-list > li.add-entry:hover {
  border-style: solid;
  color: #3d84ca;
  cursor: pointer;
}

.cursor-pointer,
.clickable:hover {
  cursor: pointer;
}

.action,
td.clickable {
  color: #3c83ca;
}
.action:hover,
td.clickable:hover,
tr td.mimic-hover {
  color: #f7f6f6;
  background: #3c83ca;
  cursor: pointer;
}

.rota-week .add-appt.fa.clickable {
  color: #acbbca;
}
.rota-week .add-appt.fa.clickable:hover {
  color: #3c83ca;
}

/* Responsive layout for id-badge and menus */
.sidebar-layout #context-badge {
  margin-left: -10px;
  margin-bottom: -19px;
}

@media (min-width: 768px) {
  /* context-badge is an image (photo or brand logo + some useful info) */
  .sidebar-layout .container-sidebar {
    position: absolute;
    width: 180px;
    /* Was left: 0px; but want to sit within margin-left:auto on html.v2 */
    top: 57px;
  }
  .sidebar-layout .container-sidebar .ecco-rounded {
    /* override style of container when we abs the .container-sidebar*/
    border: none;
    border-radius: 0;
    padding: 0;
    background: white;
  }
  .id-badge .thumbnails {
    margin-top: 0;
  }
  .sidebar-layout .container-main {
    margin-left: 180px;
  }
}

@media print {
  a,
  button {
    display: none;
  }

  .btn {
    display: none;
  }

  .nav-tabs {
    display: none;
  }

  .sidebar-layout .container-sidebar {
    display: none;
  }

  .sidebar-layout .container-main {
    margin-left: 0;
  }

  .ecco-rounded {
    border: 0;
  }
}

/** Make bootstrap look like JQUI tabs. */
.nav-tabs {
  border-bottom: 0;
}

ul.nav.nav-tabs {
  padding-left: 8px;
}

ul.nav.nav-tabs > li > a {
  background: #f5f8f9 50% 50% repeat-x;
  border-color: #c5dbec;
  border-bottom: 1px solid #a2b7d8;
}

ul.nav.nav-tabs > li.active > a {
  background: #f5f8f9 50% 50% repeat-x;
  border-color: #a2b7d8;
  border-bottom: 1px solid transparent;
}

.nav.nav-tabs > li > a,
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  border-radius: 4px 4px 0 0;
  color: #3c83ca;
  padding: 7px 14px;
}

ul.nav.nav-tabs > li.disabled > a {
  color: #999;
  opacity: 0.5;
}

.v3 ul.nav.nav-tabs > li > a {
  opacity: 1;
}

.tab-pane > .container-fluid > .ecco-rounded-row {
  margin-top: 4px;
  margin-bottom: 4px;
}

.row.ecco-rounded-row > div .tab-content,
.row.ecco-rounded-row > div .nav {
  /* div is actually col-*-* */
  margin-left: -11px;
  margin-right: -11px;
}

.tab-content {
  border: 1px solid #a2b7d8;
  border-radius: 10px;
  margin-bottom: 15px;
  min-height: 100px;
  background-color: rgba(0, 0, 0, 0.02);
  overflow: auto; /* to clip radius - may show scroll bars when zooming */
}

@media print {
  .tab-content {
    border: 0;
  }
}

/** Steps Nav **/
.nav-steps {
}

.nav-steps > li {
  float: left;
}

.nav-steps > li + li::before {
  content: ">";
  display: block;
  float: left;
  padding: 10px 0;
}

.nav-steps > li > a {
  float: left;
}

.nav-steps > li > a:hover,
.nav-steps > li > a:active {
  background: none;
  text-decoration: underline;
}

.nav-steps > li.active > a {
  font-weight: bold;
}

img.view-signature {
  margin: auto;
  display: block;
  max-height: 400px;
  max-width: 100%;
  height: auto;
  width: auto;
}

canvas.signature {
  border: 1px dashed lightgray;
  border-bottom: 2px solid gray;
  margin-bottom: 20px;
}

@media (max-width: 599px) {
  .referrals-list table th:nth-child(5),
  .referrals-list table td:nth-child(5) {
    display: none;
  }
}
@media (max-width: 767px) {
  /* hide r-id, start cols when narrow */
  .referrals-list table th:first-child,
  .referrals-list table td:first-child,
  .referrals-list table th:nth-child(6),
  .referrals-list table td:nth-child(6),
  .referrals-list table th:nth-child(7),
  .referrals-list table td:nth-child(7),
  .referrals-list table th:nth-child(8),
  .referrals-list table td:nth-child(8) {
    display: none;
  }
}
.referrals-list table tr.second-row td {
  display: table-cell !important;
  border-top: none;
  padding: 2px 8px 15px;
}

@media (min-width: 768px) {
  /* hide status summary when wide */
  .referrals-list table th:nth-child(9),
  .referrals-list table td:nth-child(9) {
    display: none;
  }
}

.menu-container {
  min-height: 480px;
}

/* terms and conditions */
#tnc {
  font-size: 0.9em;
  margin-top: 5px;
  text-align: center;
}
#tnc .col-xs-4 {
  padding-left: 0;
  padding-right: 0;
}
#tnc .space {
  margin-left: 5px;
  margin-right: 5px;
  display: inline-block;
}

.changelog-release span {
  margin-left: 30px;
  position: relative;
  display: inline-block;
}
.changelog-release dd {
  margin-bottom: 8px;
  margin-top: 8px;
}

.changelog-release .change i {
  color: #ff9400;
}
.changelog-release .feature i {
  color: #11c611;
}
.changelog-release .improvement i {
  color: #c69111;
}
.changelog-release .wip i {
  color: #c69111;
}
.changelog-release .fix i {
  color: #e71313;
}

.changelog-release .love i {
  color: #ff6363;
}

html.reports .searchable-list form {
  margin: 0 8px;
}

h4.events-section {
  font-size: 100%;
  font-weight: bold;
}

/* Cards */
.cards-container {
  padding-bottom: 15px;
  margin: 15px;
}

.cards-container h4 {
  text-align: center;
  font-size: 1em;
}

.card-group {
  margin-bottom: 15px;
}

.vertical-center {
  min-height: 90%; /* Fallback for browsers do NOT support vh unit */
  min-height: 90vh; /* These two lines are counted as one :-)       */

  display: flex; /* IE 10+ */
  align-items: center;
}

/* Include some sense from Bootstrap reset stuff for when doing pure Material UI */
html.v3 {
  font-size: 16px; /* Override Bootstrap because we have rem sizings in Material UI which are relative to root element */
}

.v3,
.v3 * {
  box-sizing: border-box;
}
.v3 body {
  margin: 0;
}

.v3 .e-row {
  margin: 12px 8px;
}

.v3 .e-label button.button.btn.btn-link {
  text-transform: inherit;
  min-width: inherit;
  margin-right: 8px;
  font-size: 16px;
}

/* Reset what propeller.css does that we don't want unless .pmd-tabs is in use */
.nav-tabs > li > a {
  opacity: 1;
}
.pmd-tabs .nav-tabs > li > a {
  opacity: 0.54;
}

.v3 img {
  vertical-align: middle;
}

#modal-title h2,
#modal-title h6 {
  text-transform: lowercase;
}

.m-ui #modal-title h2,
.m-ui #modal-title h6 {
  text-transform: initial;
}

.v3 .modal-footer {
  border: none;
}

.v3 h3 {
  padding: 16px 0;
  border-bottom: 1px solid #eee;
}

.v3 h4 {
  font-size: 1rem; /* reset propeller.css */
  padding: 16px 0 4px;
}

.v3 textarea,
.v3 select,
.v3 input,
.v3 textarea:focus,
.v3 select:focus,
.v3 input:focus {
  box-shadow: none;
  -moz-box-shadow: none;
}

.v3 .btn {
  text-transform: inherit;
  min-width: initial;
}

.v3 .btn-link,
.v3 .btn-default {
  color: #337ab7;
}

.v3 .btn-primary,
.v3 .dropdown-toggle.btn-primary {
  background-color: #337ab7;
}

.v3 #editor-actions button.btn {
  color: white;
  background-color: transparent;
}

/* MATERIAL DESIGN - see https://material.io/guidelines/style/typography.html#typography-styles
 * NOTE: Use px wherever sp or dp are stated in Material Design docs

 * USE propeller.css and h1,h2 etc as a first preference
 */
.md-headline {
  font-weight: normal;
  size: 24px;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"],
  input[type="month"] {
    line-height: initial;
  }
}
