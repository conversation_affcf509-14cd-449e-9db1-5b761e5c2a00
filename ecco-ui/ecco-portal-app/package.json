{"name": "ecco-portal-app", "version": "0.1.0", "private": true, "type": "module", "exports": {}, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "0.0.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/user-event": "^12.1.10", "@types/node": "^14.18.12", "@types/react": "^16.9.56", "@types/react-dom": "^16.9.24", "@types/react-router": "^5.1.11", "@types/react-router-dom": "^5.1.7", "application-properties": "0.0.0", "ecco-components": "0.0.0", "ecco-components-core": "0.0.0", "ecco-dto": "0.0.0", "ecco-offline": "0.0.0", "ecco-rota": "0.0.0", "ecco-spa-global": "0.0.0", "font-awesome": "^4.3.0", "jquery": "3.6.0", "jquery-migrate": "3.3.2", "prop-types": "^15.5.8", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "workbox-core": "^5.1.4", "workbox-expiration": "^5.1.4", "workbox-precaching": "^5.1.4", "workbox-routing": "^5.1.4", "workbox-strategies": "^5.1.4"}, "devDependencies": {"@jest/globals": "29.7.0", "camelcase": "^6.1.0", "ecco-webpack-config": "0.0.0", "eslint": "^7.14.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "rimraf": "5.0.10", "typescript": "5.8.3"}, "scripts": {"analyze": "ecco-webpack --env visualize", "build": "ecco-webpack", "clean": "<PERSON><PERSON><PERSON> build", "emit": "ecco-webpack", "fix": "tsc -p src -p service-worker && eslint --fix src service-worker", "lint": "tsc -p src -p service-worker && eslint src service-worker", "lint-strict": "tsc -p src -p service-worker && eslint --max-warnings 0 src service-worker", "start": "ecco-webpack serve --env publicUrl=/ --open", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Should be ecco-jest but no tests", "test-sequential": "echo Nothing to do"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}