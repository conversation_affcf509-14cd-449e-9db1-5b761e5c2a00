import MomentUtils from "@date-io/moment";
import {EccoDate} from "@eccosolutions/ecco-common";
import {createStyles, makeStyles, Theme} from "@material-ui/core";
import {DatePicker, MuiPickersUtilsProvider} from "@material-ui/pickers";
import moment = require("moment");
import * as React from "react";
import {eccoDateToIso} from "./datePickers";
import {ReactElement} from "react";

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        root: {
            marginRight: theme.spacing(),
        }
    })
);

export function DateRangePickerEccoDate(props: {
    classes?: string | undefined;
    start: EccoDate | null;
    onStartChange: (value: EccoDate | null) => void;
    end: EccoDate | null;
    onEndChange: (value: EccoDate | null) => void;
    renderer?: ((el: ReactElement) => ReactElement) | undefined;
}) {
    return (
        <DateRangePickerMoment
            classes={props.classes}
            start={eccoDateToIso(props.start)}
            onStartChange={m => props.onStartChange(m ? EccoDate.fromMoment(m) : null)}
            end={eccoDateToIso(props.end)}
            onEndChange={m => props.onEndChange(m ? EccoDate.fromMoment(m) : null)}
            renderer={props.renderer}
        />
    );
}

export function DateRangePicker(props: {
    classes?: string | undefined;
    start: string | null;
    onStartChange: (value: string | null) => void;
    end: string | null;
    onEndChange: (value: string | null) => void;
    renderer?: ((el: ReactElement) => ReactElement) | undefined;
    limitDays?: number | undefined;
}) {
    return (
        <DateRangePickerMoment
            classes={props.classes}
            start={props.start}
            onStartChange={m => props.onStartChange(m ? m.toISOString() : null)}
            end={props.end}
            onEndChange={m => props.onEndChange(m ? m.toISOString() : null)}
            limitDays={props.limitDays}
            renderer={props.renderer}
        />
    );
}

/**
 * This should become internal only to anti-corrupt the spread of Moment which is useful internally for formatting
 * @deprecated
 */
export function DateRangePickerMoment(props: {
    classes?: string | undefined;
    start?: moment.Moment | string | null | undefined;
    onStartChange: (value: moment.Moment | null) => void;
    end?: moment.Moment | string | null | undefined;
    onEndChange: (value: moment.Moment | null) => void;
    renderer?: (el: ReactElement) => ReactElement | undefined;
    limitDays?: number | undefined;
}) {
    const classes = useStyles();
    const renderer = props.renderer || (el => el);

    return (
        <MuiPickersUtilsProvider utils={MomentUtils}>
            {renderer(
                <DatePicker
                    clearable={true}
                    className={props.classes ?? classes.root}
                    format="DD/MM/YYYY"
                    id="date-picker-inline"
                    label="start date"
                    name="startDate"
                    value={props.start}
                    onChange={props.onStartChange}
                />
            )}
            {renderer(
                <DatePicker
                    clearable={true}
                    className={props.classes ?? classes.root}
                    format="DD/MM/YYYY"
                    label="end date"
                    name="endDate"
                    minDate={props.start || undefined}
                    maxDate={
                        props.limitDays &&
                        props.start &&
                        moment(props.start).add(props.limitDays, "days")
                    }
                    value={props.end}
                    onChange={props.onEndChange}
                />
            )}
        </MuiPickersUtilsProvider>
    );
}