import {default as MomentUtils} from "@date-io/moment";
import {EccoTime} from "@eccosolutions/ecco-common";
import {createStyles, makeStyles, Theme} from "@material-ui/core";
import {DateTimePicker, KeyboardTimePicker, MuiPickersUtilsProvider, TimePicker} from "@material-ui/pickers";
import * as moment from "moment";
import * as React from "react";
import {ReactNode} from "react";

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        root: {
            margin: 0 // theme.spacing(),
        }
    })
);

export function TimePickerEccoTime(props: {
    classes?: string | undefined;
    time: EccoTime | null;
    onTimeChange: (value: EccoTime | null) => void;
    label: ReactNode;
    required?: boolean | undefined;
    disabled?: boolean | undefined;
    minsStep?: number | undefined;
}) {
    const classes = useStyles();
    return (
        <MuiPickersUtilsProvider utils={MomentUtils}>
            <TimePicker
                label={props.label}
                clearable
                disabled={props.disabled}
                required={props.required}
                className={props.classes || classes.root}
                minutesStep={props.minsStep || 5}
                value={props.time ? "2010-06-15T" + props.time.formatHoursMinutes() : null}
                onChange={m => props.onTimeChange(!m ? null : EccoTime.fromMomentExactMinutes(m))}
            />
        </MuiPickersUtilsProvider>
    );
}

export function TimePickerMoment(props: {
    classes?: string | undefined;
    value: moment.Moment | null;
    onChange: (value: moment.Moment | null) => void;
    label: ReactNode;
    required?: boolean | undefined;
}) {
    const classes = useStyles();
    return (
        <MuiPickersUtilsProvider utils={MomentUtils}>
            <KeyboardTimePicker {...props} className={props.classes || classes.root} />
        </MuiPickersUtilsProvider>
    );
}

export function createTimePickerInput(
    name: string,
    label: ReactNode,
    value: string,
    onChange: (isoTime: string | null) => void,
    disabled?: boolean | undefined,
    required?: boolean | undefined
): JSX.Element {
    return (
        <MuiPickersUtilsProvider utils={MomentUtils}>
            <TimePicker
                value={value ? `2010-06-15T${value}` : null}
                label={label}
                // format="HH:mm"
                onChange={e => onChange(e ? e.format("HH:mm") : null)}
                disabled={disabled}
                required={required}
                minutesStep={5}
            />
        </MuiPickersUtilsProvider>
    );
}

/**
 * Note we keep the timezone offset while manipulating, because onChange emitted value is passed back in.
 * If we want local time, we can then .substr(0, 16) to get the datetime-local value to send to a back end
 */
export function createDateTimePickerInput(
    name: string,
    label: ReactNode,
    dateTimeWithOffset: string | null | undefined,
    onChange: (isoDateTimeOffset: string | null) => void,
    disabled?: boolean | undefined,
    required?: boolean | undefined,
    minsStep?: number | undefined
): JSX.Element {
    return (
        <MuiPickersUtilsProvider utils={MomentUtils}>
            <DateTimePicker
                value={dateTimeWithOffset ?? null} // Force to null as internally moment treats undefined as "now()"
                label={label}
                format={"ddd Do MMM Y, hh:mm a"}
                onChange={(e: moment.Moment | null) => onChange(e ? e.format() : null)} // Default format is
                disabled={disabled}
                required={required}
                minutesStep={minsStep || 5}
            />
        </MuiPickersUtilsProvider>
    );
}
