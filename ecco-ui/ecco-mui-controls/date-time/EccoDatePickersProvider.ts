import { IUtils } from "@date-io/core/IUtils";
import {EccoDateTime} from "@eccosolutions/ecco-common";

interface Opts {
    locale?: string;
}


export default class EccoDateUtils implements IUtils<EccoDateTime> {
    public locale?: string;

    public yearFormat = "YYYY";

    public yearMonthFormat = "MMMM YYYY";

    public dateTime12hFormat = "MMMM Do hh:mm a";

    public dateTime24hFormat = "MMMM Do HH:mm";

    public time12hFormat = "hh:mm A";

    public time24hFormat = "HH:mm";

    public dateFormat = "MMMM Do";

    constructor({locale}: Opts = {}) {
        this.locale = locale;
    }

    public parse(value: string, format: string) {
        if (value === "") {
            return null;
        }

        // TODO: perhaps dateFns parse as that'll tree-shake
        console.warn(`parse(value, ${format}`);
        return null; // this.moment(value, format, true);
    }

    public date(value?: any) {
        if (value === null) {
            return null;
        }

        return EccoDateTime.fromLocalJsDate(new Date(value)); // i.e. now for value === undefined
    }

    public isValid(value: any) {
        // FIXME: probably not what we're after
        console.warn(`isValid(${value})`);
        return EccoDateTime.parseIso8601(value) != null;
    }

    public isNull(date: EccoDateTime) {
        return date === null;
    }

    public getDiff(date: EccoDateTime, comparing: EccoDateTime | string): number {
        throw new Error("getDiff() not implemented");
    }

    public isAfter(date: EccoDateTime, value: EccoDateTime) {
        return date.laterThan(value);
    }

    public isBefore(date: EccoDateTime, value: EccoDateTime) {
        return date.earlierThan(value);
    }

    public isAfterDay(date: EccoDateTime, value: EccoDateTime) {
        return date.toEccoDate().laterThan(value.toEccoDate());
    }

    public isBeforeDay(date: EccoDateTime, value: EccoDateTime) {
        return date.toEccoDate().earlierThan(value.toEccoDate());
    }

    public isBeforeYear(date: EccoDateTime, value: EccoDateTime) {
        return date.getYear() < value.getYear();
    }

    public isAfterYear(date: EccoDateTime, value: EccoDateTime) {
        return date.getYear() > value.getYear();
    }

    public startOfDay(date: EccoDateTime) {
        return new EccoDateTime(date.getYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
    }

    public endOfDay(date: EccoDateTime) {
        return new EccoDateTime(date.getYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);
    }

    public format(date: EccoDateTime, formatString: string) {
        // date.locale(this.locale);
        return date.format(formatString);
    }

    public formatNumber(numberToFormat: string) {
        return numberToFormat;
    }

    public getHours(date: EccoDateTime) {
        return date.getHours();
    }

    public addDays(date: EccoDateTime, count: number) {
        return date.addDays(count);
    }

    public setHours(date: EccoDateTime, count: number) {
        return date.withHours(count);
    }

    public getMinutes(date: EccoDateTime) {
        return date.getMinutes();
    }

    public setMinutes(date: EccoDateTime, count: number) {
        return date.withMinutes(count);
    }

    public getSeconds(date: EccoDateTime) {
        return date.getSeconds();
    }

    public setSeconds(date: EccoDateTime, count: number) {
        return date.withSeconds(count);
    }

    public getMonth(date: EccoDateTime) {
        return date.getMonth();
    }

    public isSameDay(date: EccoDateTime, comparing: EccoDateTime) {
        return date.toEccoDate().equals(comparing.toEccoDate());
    }

    public isSameMonth(date: EccoDateTime, comparing: EccoDateTime) {
        return date.getMonth() == comparing.getMonth();
    }

    public isSameYear(date: EccoDateTime, comparing: EccoDateTime) {
        return date.getYear() == comparing.getYear();
    }

    public isSameHour(date: EccoDateTime, comparing: EccoDateTime) {
        return date.getHours() == comparing.getHours();
    }

    public setMonth(date: EccoDateTime, count: number) {
        return date.withMonth(count);
    }

    public getMeridiemText(ampm: "am" | "pm") {
        return ampm === "am" ? "AM" : "PM";
    }

    public startOfMonth(date: EccoDateTime) {
        return date.withDate(1); // TODO: hours mins secs?
    }

    public endOfMonth(date: EccoDateTime) {
        return date.addMonths(1).withDate(1).subtractDays(1); // TODO: hours mins secs
    }

    public getNextMonth(date: EccoDateTime) {
        return date.addMonths(1);
    }

    public getPreviousMonth(date: EccoDateTime) {
        return date.subtractMonths(1);
    }

    public getMonthArray(date: EccoDateTime) {
        const months: EccoDateTime[] = [];

        for (let month = 1; month <= 12; month++) {
            months.push(new EccoDateTime(date.getYear(), month, 1, 1, 0, 0, 0));
        }
        return months;
    }

    public getYear(date: EccoDateTime) {
        return date.getYear();
    }

    public setYear(date: EccoDateTime, year: number) {
        return date.withYear(year);
    }

    public mergeDateAndTime(date: EccoDateTime, time: EccoDateTime) {
        return this.setMinutes(this.setHours(date, this.getHours(time)), this.getMinutes(time));
    }

    public getWeekdays() {
        return ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        // see also https://github.com/dmtrKovalenko/date-io/blob/master/packages/date-fns/src/date-fns-utils.ts#L372
        // return this.moment.weekdaysShort(true);
    }

    public isEqual(value: EccoDateTime, comparing: any) {
        if (value === null && comparing === null) {
            return true;
        }

        if (value == null && comparing != null) {
            return false;
        }

        return value.equals(comparing);
    }

    public getWeekArray(date: EccoDateTime) {
        const nestedWeeks: EccoDateTime[][] = [];
        // const start = date
        //     .clone()
        //     .startOf("month")
        //     .startOf("week");
        // const end = date
        //     .clone()
        //     .endOf("month")
        //     .endOf("week");
        //
        // let count = 0;
        // let current = start;
        //
        // while (current.isBefore(end)) {
        //     const weekNumber = Math.floor(count / 7);
        //     nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
        //     nestedWeeks[weekNumber].push(current);
        //
        //     current = current.clone().add(1, "day");
        //     count += 1;
        // }

        return nestedWeeks;
    }

    public getYearRange(start: EccoDateTime, end: EccoDateTime) {
        const years: EccoDateTime[] = [];

        for (let year = start.getYear(); year <= end.getYear(); year++) {
            years.push(new EccoDateTime(year, 1, 1, 1, 0, 0, 0));
        }
        return years;
    }

    // displaying methods
    public getCalendarHeaderText(date: EccoDateTime) {
        return this.format(date, this.yearMonthFormat);
    }

    public getYearText(date: EccoDateTime) {
        return this.format(date, "YYYY");
    }

    public getDatePickerHeaderText(date: EccoDateTime) {
        return this.format(date, "ddd, MMM D");
    }

    public getDateTimePickerHeaderText(date: EccoDateTime) {
        return this.format(date, "MMM D");
    }

    public getMonthText(date: EccoDateTime) {
        return this.format(date, "MMMM");
    }

    public getDayText(date: EccoDateTime) {
        return this.format(date, "D");
    }

    public getHourText(date: EccoDateTime, ampm: boolean) {
        return this.format(date, ampm ? "hh" : "HH");
    }

    public getMinuteText(date: EccoDateTime) {
        return this.format(date, "mm");
    }

    public getSecondText(date: EccoDateTime) {
        return this.format(date, "ss");
    }
}
