package com.ecco.service.reports.clg.bridge;

import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.joda.time.DateTime;

import com.ecco.dom.OutcomeSupport;
import com.ecco.dto.reports.clg.ActionResolved;

public interface HAConverter {

    public void convert(HSSFWorkbook workbook, DateTime qrtFrom, List<OutcomeSupport> outcomes, List<ActionResolved> outcomeData);

}
