package com.ecco.service.reports.clg.bridge;

import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.joda.time.DateTime;

import com.ecco.dom.OutcomeSupport;
import com.ecco.dto.reports.clg.ActionResolved;
import com.ecco.service.reports.ExcelConverter;

// it is likely that any new template will be wanted for pre-existing data
// so we retain only one document for the conversion
// but we still want to retain all the converting logic through different versions, so we version the implementing class
// and retain the interface to ensure all versions remain operational
public class HAConverter2012 extends ExcelConverter implements HAConverter {

    public void convert(HSSFWorkbook workbook, DateTime qrtFrom, List<OutcomeSupport> outcomes, List<ActionResolved> data) {

        HSSFSheet sheet;
        HSSFRow sheetRow;
        HSSFCell cell;

        sheet = workbook.createSheet("ecco");
        int row=0;
        int col=0;
        cell = getCell(sheet, row, col++);
        setText(cell, "first name");
    }

}
