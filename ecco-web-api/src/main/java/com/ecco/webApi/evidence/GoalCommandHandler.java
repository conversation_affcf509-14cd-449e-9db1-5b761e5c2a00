package com.ecco.webApi.evidence;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dao.EvidenceSupportActionRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.*;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.dom.EvidenceSupportWorkAction;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.SupportWorkActionAssociationRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.ReportUnsecuredDelegator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.CalendarService;
import com.querydsl.jpa.JPQLQuery;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.jspecify.annotations.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.Collections;
import java.util.List;

public class GoalCommandHandler extends BaseGoalCommandHandler<GoalUpdateCommandViewModel> {

    @PersistenceContext
    EntityManager entityManager;

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final EvidenceSupportActionRepository actionRepository;

    @NonNull
    private final EvidenceSupportWorkRepository workRepository;

    @NonNull
    private final SupportWorkActionAssociationRepository supportWorkActionAssociationRepository;

    @NonNull
    private final ListDefinitionRepository listDefinitionRepository;

    @NonNull
    private final ReportUnsecuredDelegator reportUnsecuredDelegator;

    public GoalCommandHandler(ObjectMapper objectMapper,
                              @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                              @NonNull EvidenceSupportActionRepository actionRepository, @NonNull EvidenceSupportWorkRepository workRepository,
                              @NonNull ClientRepository clientRepository, @NonNull ServiceRecipientRepository serviceRecipientRepository,
                              @NonNull SupportWorkActionAssociationRepository supportWorkActionAssociationRepository,
                              @NonNull ServiceRepository serviceRepository, @NonNull ParentChildResolver parentChildResolver,
                              @NonNull ListDefinitionRepository listDefinitionRepository,
                              @NonNull EntityUriMapper entityUriMapper,
                              @NonNull CalendarService calendarService,
                              @NonNull TaskDefinitionService taskDefinitionService,
                              @NonNull ReportUnsecuredDelegator reportUnsecuredDelegator) {
        super(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository,
                serviceRepository, parentChildResolver, calendarService, taskDefinitionService, entityUriMapper,
                GoalUpdateCommandViewModel.class);
        this.actionRepository = actionRepository;
        this.workRepository = workRepository;
        this.clientRepository = clientRepository;
        this.supportWorkActionAssociationRepository = supportWorkActionAssociationRepository;
        this.listDefinitionRepository = listDefinitionRepository;
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
    }

    @Override
    public CommandResult handleInternal(int parentServiceRecipientId, Integer childServiceRecipientId,
                                        Authentication auth, @NonNull GoalParams params, GoalUpdateCommandViewModel viewModel) {

        EvidenceGroup grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
        Assert.state(grp != EvidenceGroup.THREAT);

        var type = taskDefinitionService.getTaskType(EvidenceTask.fromTaskName(params.taskName));
        if (taskDefinitionService.isAuditBased(type)) {
            return null; // we only use command history for my plan as it's not supported on back end
        }

        // NB mysql column don't hold ms - it does from 5.6.4 if use datetime(3),
        // therefore the inserting of a timestamp is rounded - see applyCommonUpdates,
        // so for rapid updates (eg testing) then this query <= approach doesn't find it
        // so we round up the query to ensure we find it
        // NB however, this just moves the problem as rapid updates could find 2+ rows
        // NB so perhaps we play safe, and just fix the test?
        DateTime created = viewModel.timestamp.toDateTime(DateTimeZone.UTC);//.withMillisOfSecond(0).plusSeconds(1);
        // get latest snapshot that is before or equal to the created timestamp of this entry, so that it is
        // inserted in the correct place
        List<EvidenceSupportAction> snapshots = actionRepository
                .findLatestByServiceRecipientIdAndActionInstanceUuidAndCreatedLessOrEqualTo(parentServiceRecipientId, viewModel.actionInstanceUuid,
                        created, PageRequest.of(0, 1));

        // Use proper snapshot query instead of deprecated method to avoid dirty reads
        // Get the latest snapshot for this action instance using the proper snapshot query logic
        JPQLQuery<EvidenceSupportAction> snapshotQuery = reportUnsecuredDelegator
                .supportActionInstanceSnapshotQuery(parentServiceRecipientId, Collections.singletonList(grp), created, created, false);

        // Filter to only the specific action instance we're interested in
        List<EvidenceSupportAction> allSnapshots = snapshotQuery.fetch();
        List<EvidenceSupportAction> snapshots = allSnapshots.stream()
                .filter(action -> action.getActionInstanceUuid().equals(viewModel.actionInstanceUuid))
                .limit(1)
                .toList();

        boolean statusHasChanged = viewModel.hasChanges();

        EvidenceSupportAction newSnapshot, previousSnapshot;
        if (snapshots.isEmpty()) {
            newSnapshot = EvidenceSupportAction.builder(parentServiceRecipientId, viewModel.actionInstanceUuid,
                        viewModel.parentActionInstanceUuid, params.actionDefId)
                    .withStatusChange(statusHasChanged)
                    .build();
            if (viewModel.statusChange != null && viewModel.statusChange.to != null) {
                newSnapshot.setStatus(viewModel.statusChange.to);
            }
            previousSnapshot = newSnapshot; // we're starting with this.
        }
        else {
            previousSnapshot = snapshots.get(0);
            // if we are updating the same workUuid, then the new snapshot is the previous - with amends to come
            if (previousSnapshot.getWork().getId().equals(viewModel.workUuid)) {
                newSnapshot = previousSnapshot;
            } else {
                newSnapshot = EvidenceSupportAction.fromPrevious(previousSnapshot);
            }
        }

        findOrCreateWork(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel, newSnapshot);

        if (viewModel.hasChanges()) {
            applyUpdatesToNewSnapshot(auth, parentServiceRecipientId, params.actionDefId, viewModel, previousSnapshot, newSnapshot);
            actionRepository.save(newSnapshot);
        }
        else {
            // this is for a command for marking a SupportWorkAction as relevant
            supportWorkActionAssociationRepository.save(new EvidenceSupportWorkAction(params.actionDefId, viewModel.workUuid));
        }
        return null; // TODO: Could return Hateoas Link to created/updated
    }

    /** Other commands may create work first, so we may be creating a new work item or updating it.
     */
    private void findOrCreateWork(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth, @NonNull GoalParams params,
            BaseGoalUpdateCommandViewModel viewModel, EvidenceSupportAction newSnapshot) {

        EvidenceSupportWork work = workRepository.findById(viewModel.workUuid).orElse(null);

        if (work == null) {
            work = createNewWork(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel);
        }
        newSnapshot.setWork(work);
    }

    /** For new work, we use the timestamp for the workDate as we otherwise don't know.
     *  A later CommentCommand may then update it with a real work date.
     */
    private EvidenceSupportWork createNewWork(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth, @NonNull GoalParams params,
                                              BaseGoalUpdateCommandViewModel viewModel) {

        // NOTE: This will be null for non-Referral evidence
        ClientDetail client = clientRepository.findOneByServiceRecipientId(parentServiceRecipientId);
        EvidenceTask task = EvidenceTask.fromTaskName(params.getTaskName());
        EvidenceGroup grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
        SupportEvidenceBuilder builder = createNewSupportWork(parentServiceRecipientId, childServiceRecipientId, auth, task, grp,
                viewModel.workUuid, viewModel.timestamp, client);
        EvidenceSupportWork work = builder.build();
        entityManager.persist(work);
        return work;
    }

    private void applyUpdatesToNewSnapshot(Authentication authentication, long referralId, long actionDefId,
            GoalUpdateCommandViewModel viewModel, EvidenceSupportAction previousSnapshot,
            EvidenceSupportAction newSnapshot) {

        applyCommonUpdates(authentication, viewModel, previousSnapshot, newSnapshot);

        if (viewModel.statusChangeReason != null) {
            warnIfPrevValueDoesntMatch(viewModel.serviceRecipientId, actionDefId, viewModel.statusChangeReason,
                    previousSnapshot.getStatusChangeReason() == null ? null : previousSnapshot.getStatusChangeReason().getId(),
                    "statusChangeReason");
            ListDefinitionEntry to = viewModel.statusChangeReason.to == null ? null :
                    listDefinitionRepository.findById(viewModel.statusChangeReason.to).orElse(null);  // TODO: JPA ref via getOne()
            newSnapshot.setStatusChangeReason(to);
        }

    }
}
