@file:Suppress("UnstableApiUsage")

import io.spring.gradle.dependencymanagement.DependencyManagementPlugin
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.springframework.boot.gradle.tasks.run.BootRun

val isCiServer = System.getenv().containsKey("CI")

plugins {
    id("org.springframework.boot") version springBootVersion apply false // false because we want this version for dep mgt but not lifecycle here
    id("io.spring.dependency-management") version dependencyManagementVersion
    kotlin("jvm")
    kotlin("plugin.spring") version kotlinVersion
    kotlin("plugin.lombok") version kotlinVersion
    id("com.diffplug.spotless") version "7.0.2"
}

spotless {
    if (!isCiServer) {
        java {
            toggleOffOn()
            removeUnusedImports() // should be done by Google Java Format
            //        googleJavaFormat("1.23.0") // TODO work out a decent format or adopt this - IDEA settings exist to match
            //        indentWithTabs(2) // see https://github.com/diffplug/spotless/issues/420#issuecomment-512350756
            //        leadingTabsToSpaces(4) // Uncomment this if you want to convert tabs to spaces
            target("ecco*/src/**/*.java")
        }

        kotlinGradle {
        }
        kotlin {
            ktlint("1.5.0")
                // Do this here because putting them in .editorconfig doesn't work (they're there too for other invocations of ktlint that may use them)
                .editorConfigOverride(
                    mapOf(
                        "ktlint_standard_no-wildcard-imports" to "disabled",
                        "ktlint_standard_filename" to "disabled",
                    ),
                )
            toggleOffOn()
            target("ecco*/src/**/*.kt")
        }
    }
//    format("misc") {
//        target("ecco*/*.md", "ecco*/*.yaml", "ecco*/*.yml")
//        trimTrailingWhitespace()
//        endWithNewline()
//    }
}
// We override the versions provided by default here.
// See https://docs.spring.io/spring-boot/docs/2.7.18/reference/html/dependency-versions.html#appendix.dependency-versions.properties
// for the list of properties that can be overridden
// extra["spring.version"] = "5.3.x" // defer to spring-boot-dependencies
extra["kotlin.version"] = kotlinVersion
extra["selenium.version"] = "3.141.59"
extra["spring-security.version"] = "5.8.7"
extra["spring-hateoas.version"] = "1.3.7"
extra["h2.version"] = "1.4.200"
// extra["hibernate.version"] = hibernateVersion
extra["hibernate-validator.version"] = "6.0.22.Final"
extra["lombok.version"] = lombokVersion
extra["tomcat.version"] = tomcatVersion
// See https://docs.spring.io/spring-boot/docs/3.2.0/gradle-plugin/reference/htmlsingle/#managing-dependencies.dependency-management-plugin
// for how to customise versions
apply(plugin = "io.spring.dependency-management")

group = "org.eccosolutions"
version = "1.0.0.CI-SNAPSHOT"

subprojects {
//    apply<SpringBootPlugin>() - need to apply only to the fat JARs otherwise plugin tries looking for main class
    apply<JavaLibraryPlugin>()
    apply<DependencyManagementPlugin>()
    apply(plugin = "org.eccosolutions.java-conventions")
    apply(plugin = "io.spring.dependency-management")
    apply(plugin = "org.jetbrains.kotlin.jvm")
    apply(plugin = "org.jetbrains.kotlin.plugin.spring")
    apply(plugin = "org.jetbrains.kotlin.plugin.lombok")
    //    apply<HibernatePlugin>() -- Revert commit for this line to get what's needed if we ever need it

    // ext["hibernate.version"] = hibernateVersion // Doesn't work check docs when have time

    dependencyManagement {
        imports {
            mavenBom(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES)
            mavenBom("org.springframework.cloud:spring-cloud-dependencies:$springCloudVersion")
        }
        dependencies {
            dependency("com.azure.spring:azure-spring-boot-starter-active-directory:3.6.1") {
                exclude("org.springframework.boot:spring-boot-starter-logging")
            }
            dependency("com.aayushatharva.brotli4j:brotli4j:$brotliVersion")
            // spring-security 5.7.1 from Spring Boot 2.7.0, but we have 5.8.7
            /* 24.11 is on 966cd771, as main was at the time - but should be 6b3131f6 ? */
            dependency("com.github.eccosolutions:cosmo-core:42b59b4e")
            dependency("org.mnode.ical4j:ical4j:3.0.29") // poss best to match with cosmo-core but could be higher

            // Guava isn't managed by Spring Boot, but we want to be explicit about the version
            dependency("com.google.guava:guava:33.2.1-jre")

            dependency("com.querydsl:querydsl-apt:$queryDslVersion")
            dependency("com.querydsl:querydsl-core:$queryDslVersion")
            dependency("com.querydsl:querydsl-jpa:$queryDslVersion")
            dependency("com.querydsl:querydsl-codegen:$queryDslVersion")
            dependency("org.synyx:messagesource:$synyxMessagesourceVersion") {
                exclude("org.springframework:org.springframework.jdbc")
                exclude("org.springframework:org.springframework.context.support")
                exclude("org.springframework:org.springframework.beans")
                exclude("commons-logging:commons-logging-api")
            }
            dependency("com.lowagie:itext:2.1.7") {
                exclude("bouncycastle:bcmail-jdk14")
                exclude("bouncycastle:bcprov-jdk14")
                exclude("org.bouncycastle:bctsp-jdk14")
            }
            dependency("com.oracle.database.jdbc:ojdbc8:12.2.0.1") {
                exclude("com.oracle.database.jdbc:ucp")
                exclude("com.oracle.database.ha:simplefan")
                exclude("com.oracle.database.ha:ons")
                exclude("com.oracle.database.security:oraclepki")
                exclude("com.oracle.database.security:osdt_cert")
                exclude("com.oracle.database.security:osdt_core")
            }

            dependency("commons-collection:commons-collection:3.2.2")
            dependency("commons-io:commons-io:2.16.1")
            dependency("commons-lang:commons-lang:2.6")
            dependency("commons-fileupload:commons-fileupload:1.5")

            dependency("org.mapstruct:mapstruct:$mapstructVersion")
            dependency("org.mapstruct:mapstruct-processor:$mapstructVersion")

            dependency("org.dbunit:dbunit:2.7.3") {
                exclude("org.slf4j:slf4j-nop")
            }
//            dependency("org.hibernate:hibernate-core:${hibernateVersion}")
//            dependency("org.hibernate:hibernate-entitymanager:${hibernateVersion}")
//            dependency("org.hibernate:hibernate-envers:${hibernateVersion}")
//            dependency("org.hibernate:hibernate-java8:${hibernateVersion}")
//            dependency("org.hibernate:hibernate-jcache:${hibernateVersion}")

            dependency("org.liquibase:liquibase-core:3.10.3") {
                exclude("ch.qos.logback:logback-classic")
            }

            dependency("org.jasypt:jasypt:1.9.2")
            dependency("org.jasypt:jasypt-spring31:1.9.2")
            dependency("com.github.eccosolutions:jasypt-hibernate5:bea53d4e9f")

            dependency("org.jetbrains:annotations:15.0")

            dependency("net.engio:mbassador:1.2.4")
            dependency("org.mbassy:mbassador-spring:1.0.0.beta-SNAPSHOT") {
                exclude("org.aspectj:aspectjweaver")
                exclude("cglib:cglib")
            }
            dependency("org.mockito:mockito-core:4.11.0")
            dependency("org.mockito.kotlin:mockito-kotlin:4.1.0")

            // Esendex SMS supporting dependencies
            dependency("io.github.x-stream:mxparser:1.2.2")
            dependency("xmlpull:xmlpull:1.1.3.1")
            dependency("com.thoughtworks.xstream:xstream:1.4.21")
        }
        configurations.all {
            exclude(group = "ch.qos.logback", module = "logback-classic")
            exclude(group = "ch.qos.logback", module = "logback-core")
            exclude(group = "org.slf4j", module = "slf4j-reload4j")
        }
    }

    dependencies {
        modules {
            module("log4j:log4j") {
                replacedBy("org.apache.logging.log4j:log4j-core", "Version 2 uses org.apache namespace")
            }
            module("commons-logging:commons-logging") {
                replacedBy("org.slf4j:jcl-over-slf4j", "This provides the commons-logging API")
            }
            module("org.apache.logging.log4j:log4j-to-slf4j") {
                replacedBy("org.apache.logging.log4j:log4j-slf4j-impl", "Because we can either ")
            }
            module("com.sun.xml.bind:jaxb-impl") {
                replacedBy("org.glassfish.jaxb:jaxb-runtime", "Because that's the Spring Boot managed version")
            }
            module("com.sun.xml.bind:jaxb-core") {
                replacedBy("org.glassfish.jaxb:jaxb-runtime", "Because that's the Spring Boot managed version")
            }
            module("org.hamcrest:hamcrest-core") {
                replacedBy("org.hamcrest:hamcrest", "merged into hamcrest")
            }
            module("org.hamcrest:hamcrest-library") {
                replacedBy("org.hamcrest:hamcrest", "merged into hamcrest")
            }
        }
        implementation(platform("org.springframework.boot:spring-boot-dependencies:$springBootVersion"))
        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor:$springBootVersion")

        compileOnly("org.projectlombok:lombok")
        testImplementation("org.projectlombok:lombok")
        implementation("org.mapstruct:mapstruct")
        // seems not to be automatic with SpringBootPlugin
        annotationProcessor("org.projectlombok:lombok-mapstruct-binding:0.2.0")
        annotationProcessor("org.mapstruct:mapstruct-processor")
        annotationProcessor("org.projectlombok:lombok:$lombokVersion")
        testAnnotationProcessor("org.projectlombok:lombok:$lombokVersion")

        implementation("org.jspecify:jspecify:1.0.0")
        implementation("io.github.oshai:kotlin-logging-jvm:7.0.6")
        implementation("org.slf4j:slf4j-api")
        // runtime only as we don't want to see the API. We use SLF4J as the API
        runtimeOnly("org.slf4j:jcl-over-slf4j")
        // Should have log4j-slf4j-impl but not log4j-to-slf4j
        implementation("org.apache.logging.log4j:log4j-slf4j-impl")
        runtimeOnly("org.apache.logging.log4j:log4j-api")
        implementation("org.apache.logging.log4j:log4j-core") // we could do runtimeOnly here and compileOnly in ecco-infrastructure
        runtimeOnly("org.apache.logging.log4j:log4j-web")

        runtimeOnly("commons-lang:commons-lang:2.6") // we only want commons-lang3 in our apps

        implementation("javax.annotation:javax.annotation-api:1.3.2")
        implementation("org.jetbrains:annotations")

        // we provide customisations of JPA repositories, so we can export this dep to other projects - actually this is every project
        implementation("javax.persistence:javax.persistence-api:2.2")
        implementation("org.hibernate:hibernate-core")
        implementation("org.springframework.data:spring-data-jpa")
        implementation("org.springframework.data:spring-data-commons")
        implementation("org.springframework.security:spring-security-core")
        implementation("org.springframework:spring-aspects")

        implementation("javax.validation:validation-api:2.0.1.Final")

        annotationProcessor("javax.annotation:javax.annotation-api:1.3.2")

        compileOnly("javax.servlet:javax.servlet-api:3.1.0") // provided by container but may be different version
        testImplementation("javax.servlet:javax.servlet-api:3.1.0")

        testImplementation("org.springframework:spring-test")
        testImplementation("org.assertj:assertj-core")
        testImplementation("org.mockito:mockito-core")
        testImplementation("org.mockito.kotlin:mockito-kotlin")

        // JUnit 5 support
        testImplementation("org.junit.jupiter:junit-jupiter-api")
        testRuntimeOnly("org.junit.jupiter:junit-jupiter")

        // JUnit 5 Params - for parameterized tests
        testImplementation("org.junit.jupiter:junit-jupiter-params")

        // JUnit 5 Vintage Engine - allows running JUnit 4 tests with JUnit 5
        testImplementation("junit:junit")
        testRuntimeOnly("org.junit.vintage:junit-vintage-engine")

        // Kotlin test support
//        testImplementation("org.jetbrains.kotlin:kotlin-test:1.9.20")
//        testImplementation("org.jetbrains.kotlin:kotlin-test-junit5:1.9.20")
    }

    java {
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(17))
        }
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    tasks.withType<KotlinCompile> {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_17
        }
    }

    tasks.withType<JavaCompile> {
        options.compilerArgs.add("-parameters")
        options.encoding = "UTF-8"
    }

    tasks.withType<Test> {
        filter {
            isFailOnNoMatchingTests = false
        }
        enabled = !project.hasProperty("skipITs")
        logger.info("skipITs: ${project.hasProperty("skipITs")}")
        useJUnitPlatform() // Use JUnit Platform to support both JUnit 4 (via vintage) and JUnit 5
        systemProperty("user.timezone", "UTC")
        systemProperty("java.locale.providers", "JRE,SPI")
        jvmArgs("--add-opens=java.base/java.util=ALL-UNNAMED")
        jvmArgs("--add-opens=java.base/java.lang.reflect=ALL-UNNAMED")
        jvmArgs("--add-opens=java.base/java.lang=ALL-UNNAMED")
        jvmArgs("--add-opens=java.base/java.text=ALL-UNNAMED")
        jvmArgs("--add-opens=java.desktop/java.awt.font=ALL-UNNAMED")
    }

    tasks.withType<BootRun> {
        systemProperty("user.timezone", "UTC")
        systemProperty("java.locale.providers", "JRE,SPI")
        jvmArgs("--add-opens=java.base/java.util=ALL-UNNAMED")
        jvmArgs("--add-opens=java.base/java.lang.reflect=ALL-UNNAMED")
        jvmArgs("--add-opens=java.base/java.lang=ALL-UNNAMED")
        jvmArgs("--add-opens=java.base/java.text=ALL-UNNAMED")
        jvmArgs("--add-opens=java.desktop/java.awt.font=ALL-UNNAMED")
    }

//    task integrationTest(type: Test) {
//        include '**/*IntegrationTest.*'
//
//        doFirst {
//            tomcatRun.daemon = true
//            tomcatRun.execute()
//        }
//
//        doLast {
//            tomcatStop.execute()
//        }
//    }
//
//    test {
//        exclude '**/*IntegrationTest.*'
//    }
}

/*
TODO:

  testing {
    suites {
      test {
        useJUnitJupiter()

        targets {
          all {
            testTask.configure {
              enabled = runUnitTests
              finalizedBy jacocoTestReport
              systemProperty 'user.timezone', 'UTC'
            }
          }
        }
      }

      integrationTest(JvmTestSuite) {
        dependencies {
          implementation project()
          implementation "org.projectlombok:lombok:${lombokVersion}"
        }

        targets {
          all {
            testTask.configure {
              enabled = runITs
              finalizedBy jacocoTestReport
              systemProperty 'user.timezone', 'UTC'
            }
          }
        }
      }
    }
  }
  tasks.named('check') {
      dependsOn(testing.suites.integrationTest)
//    }
  }


  jacocoTestReport {
    shouldRunAfter(test)
    shouldRunAfter(integrationTest)
    reports {
      xml.required = true
      html.required = true
    }
  }

  spotless {
    if (runITs) {
      java {
        toggleOffOn()
        googleJavaFormat()
      }

      groovy {
        excludeJava()
        importOrder('\\#', '')
      }
    }
  }
 */

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }

    // Ensure jsr305 is never on the compile classpath
    compileClasspath {
        exclude(group = "com.google.code.findbugs", module = "jsr305")
    }
}

// FIXME: This is applying kotlin.jvm at top level, but this isn't a project, just the aggregator, but our dep mgt doesn't work without it

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs += "-Xjsr305=strict -Xjspecify-annotations=strict"
        jvmTarget = "17"
        javaParameters = true
    }
}

repositories {
    mavenCentral()
}